# 10. profile-storage

Date: 2024-11-18

## Status

Accepted

## Context

Данные пользователя из ГБДФЛ (сведения о месте рождения, адресе регистрации, национальности, гражданстве),
на данный момент, система получает из ЦОИД после верификации пользователя. Есть
необходимость организации хранения этих данных в контуре нашей информационной системы с привязкой к конкретному
пользователю. Хранение пользовательских данных сейчас осуществляется сервисом `users` в таблице profile.

## Decision

Согласно ETL паттерну, мы решили хранить данные, получаемые из внешних источников непосредственно
в прокси-сервисе, который эти данные получил. Полученные данные будут сохраняться в БД данного
прокси-сервиса, а другие сервисы, которым эти данные необходимы, будут запрашивать эту информацию
посредством grpc запросов.

Применительно к вышеописанному контексту, было решено информацию, которая нам необходима на данном
этапе проекта (ФИО, дата рождения, гражданство), мы будем сохранять в таблице в БД Postgres. Остальные данные,
ввиду их объема, разнообразия и мульти-язычности, мы будем в дальнейшем хранить в MongoDB.

## Consequences

- Реализовать возможность хранения данных в MongoDB
- Удалить из БД сервиса `users` таблицу profile, реализовать вызовы сервиса `coid` для получения этих данных.
- Необходимо добавить слой storage для сервиса `coid`, дополнить контракты grpc
