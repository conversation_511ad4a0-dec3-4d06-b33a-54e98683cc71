# 9. backend-verions

Date: 2024-11-12

## Status

Accepted

## Context

Продукт разрабатывается несколькими командами параллельно. Мы хотим двигаться быстро, не испытывать болей при слиянии кода, 
а также предоставлять стенды с понятным набором фич и изменений.
Несмотря на то, что мы можем постоянно мержить код в главную ветку и выкатывать его в любое время (trunk-based подход),
для задач тестирования и особенно демо важно, чтобы скоуп был определен и не менялся на время использования.

## Decision

Мы привязываем версию бэкэнда к версии спецификаций гейтвея, с которым происходит работа.
Внутренние сервисы гейтвея должны обеспечивать требуемые изменения без ломающих изменений (после первого релиза на прод).
Версии гейтвея должны расширять API или вводить новые эндпоинты, т.е. выпускать минорные или патчевые версии. 

Если это невозможно, выпускается новая мажорная версия, которая поднимается отдельно. Старая версия при этом помечается deprecated.
Время жизни устарешних мажорных версий - 3 месяца. За это время все потребители API должны переехать на новую версию.
Бэк обеспечивает поддержку 2х последних deprecated версий, которая подразумевает под собой работоспособность и критические фиксы.

Для разработки будут собираться промежуточные версии в релизной ветке на stage-контурах, которые после тестирования, будут превращаться в основные релизы.

Сбор и фиксирование версии требует использования на бэке более сложной методологии разработки, чем trunk-based.
Решено выбрать подход с релизной веткой, которая соответствует спринту. В первую часть спринта разработчики независимо в фичевых ветках делают задачи,
которые вместе заливаются в релизную ветку. В определенный момент скоуп изменений выкатывается на тестовый стенд, таким образом фиксируется.
Все новые и неуспевшие доделаться фичи уйдут в следующую релизную ветку. В релизной ветке выпускается версия -rc1, затем фиксы и -rc2, и т.д. пока не будет готова.
Каждый раз новая версия рассчитывает дифф изменений спецификации от того, что выкачено в main. После готовности к релизу, -rc версия мержится в main и выпускается основная версия по SemVer.


## Consequences

Подход с версиями требует наличия нескольких дев-стендов разного контура (минимум дев и тест).
Потому что нам нужно давать возможность разработчикам мержиться и выкатывать код (частые обновления нет версий), а для QA фиксировать скоуп и не обновлять.

Скорее всего, будет закономерно увязать момент фиксации версии для -rc кандидата с версиями на МП, нужно обсуждать процесс всей командой.

На первое время, пока нет стендов и нет большого числа команд, мы можем жить в парадигме trunk-based dev.

Для создания версий и составления чейнджлогов в meroving добавлен инструмент oas-diff, который умеет это делать автоматически. Требуется встроить инструмент в процесс CI/CD, чтобы еще и блокировать фичи с ломающими изменениями и помогать код-ревью.

Также с ростом числа команд и стендов потребуется инструмент синхронизации, чтобы понимать - на каком стенде что находится.
С помощью meroving и версий мы сможем понимать, на какой стенде конкретно какие фичи сейчас есть, но это не решит вопрос того, 
а какой команде какой стенд и когда нужен. Это явно организационная задача на будущее.
