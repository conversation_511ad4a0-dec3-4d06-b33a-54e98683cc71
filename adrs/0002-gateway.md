# 2. gateway

Date: 2024-08-26

## Status

Accepted

## Context

В рамках разработки микросервисного архитектурного стиля в нашем проекте требуется реализация gateway, который будет выполнять функции маршрутизации запросов, аутентификации, агрегации данных из микросервисов, реализации ограничения запросов, кеширования и балансировки нагрузки между микросервисами. 
Gateway также должен обеспечивать безопасность и масштабируемость приложения, поддержку различных протоколов (HTTP, WebSocket, gRPC и т.д.).

## Alternatives 

1. **Использование API Gateway на базе Nginx**
    - Плюсы:
        - Широкая поддержка сообществом.
        - Высокая производительность и гибкость настройки.
        - Поддержка множества протоколов.
    - Минусы:
        - Сложность настройки и поддержки.
        - Отсутствие встроенных возможностей для управления API (например, лимиты по использованию API).

2. **Использование API Gateway на базе Kong**
    - Плюсы:
        - Встроенные плагины для аутентификации, мониторинга и лимитирования запросов.
        - Гибкость и масштабируемость.
        - Хорошая интеграция с существующими DevOps инструментами.
    - Минусы:
        - Более высокая сложность внедрения и эксплуатации по сравнению с Nginx.
        - Стоимость Enterprise версии для продвинутых функций.

3. **Использование AWS API Gateway**
    - Плюсы:
        - Полностью управляемое решение, не требует администрирования.
        - Легкая интеграция с другими сервисами AWS.
        - Поддержка множества протоколов и API.
    - Минусы:
        - Привязка к облачной платформе AWS.
        - Ограниченная гибкость по сравнению с кастомными решениями.

4. **Использование KrakenD**
    - Плюсы:
        - Готовое, легко масштабируемое решение уровня production, поддерживающее основные функции, такие как маршрутизация, агрегация данных, аутентификация и контроль доступа.
        - Активное развитие и поддержка со стороны сообщества и коммерческих провайдеров.
    - Минусы:
        - В редакции Community Edition отсутствует поддержка gRPC.
        - Open Source расширение для gRPC устарело и имеет статус deprecated, что снижает его надежность и безопасность.
        - Стоимость Enterprise версии для поддержки gRPC значительно превышает затраты на собственную разработку аналогичной функциональности.

5. **Собственная реализация**
    - Плюсы:
        - Полный контроль над функциональностью и производительностью, возможность адаптации решения под конкретные потребности проекта.
        - Отсутствие необходимости платить за коммерческую поддержку, что снижает затраты на долгосрочное сопровождение.
        - Гибкость в выборе технологий и подходов, что позволяет использовать лучшие инструменты для решения конкретных задач.
        - Минусы нивелируются за счет кодогенерации и использования собственной разработки meroving.
    - Минусы:
        - Высокие начальные временные затраты на разработку и тестирование.
        - Более длительное время на внедрение по сравнению с готовыми решениями.

## Decision

Мы решили реализовать собственный gateway, учитывая следующие преимущества:
- Полный контроль над функциональностью, что позволяет разработать решение, идеально подходящее под наши специфические потребности и архитектурные особенности.
- Гибкость в интеграции с существующими микросервисами и системами безопасности.
- Возможность оптимизации производительности и минимизации накладных расходов за счет использования только необходимых функций и собственной библиотеки сборки микросервисных проектов meroving, основанной на кодогенерации.
- Избежание долгосрочных затрат на лицензии и поддержку коммерческих решений, что позволит направить ресурсы на дальнейшее развитие проекта.


## Consequences

- Потребуется выделение дополнительного времени и ресурсов на разработку и тестирование библиотеки meroving на старте проекта.
- Возможность точной интеграции с архитектурными особенностями нашего проекта, что приведет к лучшей производительности и надежности системы.


## Authors

Александр Кузьмук, Трусов Илья, Сергей Ретивых