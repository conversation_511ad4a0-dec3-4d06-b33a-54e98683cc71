# 0013. distionary service

Date: 2025-01-15

## Status

Accepted

## Context

Для хранения справочных данных необходимо создать сервис справочников.

## Decision

Для работы со справочниками принимается следующая терминология:
* `dictionary` - словарь. Идентифицируется читаемым мнемоническим именем;
* `document` - документ. Документ - JSON-данные, которые могут храниться в строках справочника. Идентифицируются в
соответствии с настройкой справочника - либо суррогатными ключами (UUID), либо читаемыми мнемоническими именами.

Предполагаемая структура БД:

Таблица `dictionary`:
* id - суррогатный ID (UUID);
* name - читаемое мнемоническое имя словаря;
* description - описание словаря на естественном языке;
* schema - JSON-схема для данного справочника;
* created_at - время создания;
* updated_at - время последнего обновления;

PK: (id)
Indexes: (name)

Таблица `documents`:
* id - суррогатный ID (UUID);
* dictionary_documents - ID справочника;
* name - имя документа для обращения к нему по имени;
* data - JSONB данные документа;
* valid - boolean метка соответствия документа текущей схеме справочника (данные по схеме проверяются при добавлении 
документа и при изменении схемы);
* order_num - поле для "ручной" сортировки результатов при выдаче (по умолчанию - 0);
* created_at - время создания;
* updated_at - время последнего изменения;

PK: (id)
Indexes: (dictionary_documents, name), (data)

TODO: Таблица `documents` должна создаваться как партицированная с разбивкой по суррогатному ID словаря. Новая партиция 
создается при добавлении нового справочника в таблицу `dictionary` и удаляется при его удалении.

## Consequences

Такой подход позволяет эффективно и гибко настраивать и использовать данные в справочниках.

Изначально предполагалось использовать архитектуру с размещением значений полей словаря с разворачиванием их в качестве 
отдельных записей в БД. Такой вариант часто использовался ранее в случае если БД не дает возможности хранения и 
эффективной работы с не жестко структурированными данными. У нас есть возможность в БД Postgresql использовать поля 
типа JSON для хранения таких данных. Поэтому было предложено использовать именно такие поля для хранения данных с 
произвольной структурой. Кроме того, в Postgresql есть эффективные способы работы с такими данными. После согласования 
с привлечением нескольких разработчиков, было решено делать вариант с использованием JSON полей.
