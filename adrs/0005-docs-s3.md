# 5. docs-s3

Date: 2024-08-30

## Status

Accepted

## Context

Нам необходимо хранить документы в виде физических файлов, подписанных пользователями.
Помимо файлов, нам нужно хранить мета информацию об этих документах, например атрибуты с фактом подписи и временем подписи.

## Decision

За хранение документов отвечает микросервис Documents. В момент генерации он присваивает файлу идентификатор, заполняет необходимые атрибуты.
А также формирует ссылку для с3 и кладет в хранилище. При запросе пользователь получает сначала мета-информацию и ссылку на файл из Documents,
а затем явно запрашиваем документ. При этом нам важно проверять факт владения документом при запросе, поэтому ссылка на физический файл
должна содержать в себе userID и вести на gateway. На gateway мы проверяем валидность токена авторизации, из него получаем userId и сверяем с тем,
что указан в ссылке. При успешных проверках - документа скачивается из s3 и возвращается на фронт как application/pdf.

## Consequences

При такой схеме у нас хранилище s3 недоступно напрямую из интернета, но требуется доработка gateway, чтобы обрабатывать ссылки на файлы.