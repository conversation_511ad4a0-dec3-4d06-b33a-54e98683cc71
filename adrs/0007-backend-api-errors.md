# 7. backend-api-errors

Date: 2024-09-09

## Status

Accepted

## Context

Необходимо определить основные подходы к взаимодействию фронт-бэк.

## Decision

- бэк предоставляет фронту спецификацию в формате REST openapi, по которой фронт интегрируется
- спека будет хоститься отдельно для каждого стенда и быть актуальной для этого стенда
- заложена возможность описывать возможные коды ошибок в каждом эндпоинте отдельно, важно следить за этим на бэке
- стараемся делать коды ошибок, возвращаемые в 400 уникальными, 1 код = 1 ошибочная ситуация. Это позволит обрабатывать их на UI без учета текста в message, и при локализации хранить однозначные переводы локально.
- в случае обработки данных цепочкой микросервисов, используем строгий подход - если упал хотя бы один, возвращаем общую ошибку на фронт.

## Consequences

1. Подход с ошибкой при частичной недоступности микросервисов ограничивает UX и тратит ресурсы, т.к. часть работы оказывается ненужной и надо делать перезапрос.
Но он не требует дополнительных затрат на разработку и взаимодействие между командами, что на старте нам критически важно.
Были рассмотрены еще несколько вариантов взаимодействия, перечислены их плюсы и минусы.
Есть 2 возможные альтренативы, к которым решено вернуться позже на стабильном этапе проекте
- подход с опорой на обязательность полей и ошибку только если не пришли именно required поля (сложность в кодогенерации на бэке, и нужен регулярный процесс согласования полей с фронтом)
- подход с мета-полем errors в каждом запросе (проще бэку, сложная обработка для фронта, нужен еще подход на UI как обрабатывать)
2. Заносить все коды ошибок в каждый эндпоинт может быть сложно в случае большой цепочки микросервисов.
Нужно посмотреть на практике, как будет соблюдаться актуальность. Для ее обеспечения хорошо помогла бы доработка валидации кода ошибок на gateway при возврате, и WARN сообщение или что-то подобное, если возвращается неописанная ошибка

## Authors

Сергей Ретивых, Илья Трусов, Сергей Иванов, Дмитрий Гавриков 