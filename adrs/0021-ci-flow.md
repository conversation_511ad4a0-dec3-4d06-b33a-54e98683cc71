# 21. ci-flow

Date: 2025-05-23

## Status

Accepted

## Context

Для ускорения разработки и доставки фичей нам нужен стабильный и быстро работающий пайплайн.
Само приложение собирается командой meroving build, которая выполняет кодогенерацию.
Также настроены определенные линтеры и тесты, которые также должны выполняться.

## Decision

1. Выполнение билда в CI по времени происходило достаточно долго, с учетом загрузки зависимостей.
Поэтому первым шагом было решено производить билд локально и хранить ген файлы в репозитории.
Были произведены доработки meroving для ускорения билда, можно рассмотреть варианты возврата в будущем.
2. Для линтинга и тестов нужно стянуть все golang-зависимости проекта. Для кеширования использовался механизм cache самого CI.
Он оказался очень медленным из-за того, скачивается много файлов, которые в конце джобы архивируются на диск машины-хоста, а в начале джобы соответвенно разархивируются.
Почему-то создание кеша занимало по полторы минуты, а его использование в таске по 3, что тоже долго.
3. Было решено предподготавливать образ на базе meroving:latest с установленными зависимостями go проекта.
Дополнительно в этом образе запускается golangci-lint, результаты которого тоже кешируются в образе и ускоряют линтеры.
Этот образ достаточно собирать **периодически**, но не замедлять основные пайпы.
4. Была попытка делать запуск тестов только для измененных сервисов, но она в общем итоге проигрывает по эффективности.
Потому что в ветка может запуститься тест только для 1 сервиса и отработает за 2.5 минуты вместо 4-5.
Но для деплоя на stage выпускается версия,в которой пересобираются все образы, и тогда параллельный прогон тестов в разных джобах занимает 14 минут вместо 4-5.

## Consequences

С предподготовленным образом выкат изменений на дев-стенд из develop происходит за 5-6 минут. На stage - 8-10 минут.
Изчезла проблема очереди на мерж, когда приходилось ждать окончания пайплайна и бесконечно подмерживать к себе develop.
Остается проблема с лишними мержами из-за кодген файлов meroving, и просто большой поток задач в монорепозитории также требует частых мержей.
