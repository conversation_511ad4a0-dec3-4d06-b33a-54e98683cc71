# 3. BFF

Date: 2024-08-26

## Status

Accepted

## Context

В микросервисной архитектуре роль связи бэкэнда с фронтом по большей части на себя берет gateway.
При этом нам требуется преобразовывать данные из бизнес-доменов, которые возвращают микросервисы, в формат, требуемый фронту.
Существует специальный подход и термин Backend For Frontend, который может для этого использоваться.

## Decision

Мы не используем BFF на проекте в явном виде. Gateway не является BFF, поскольку решает другой ряд задач.
Мы не хотим заводить явно специальный слой-сервис под эту задачу по следующим причинам:
- он становится точкой, которая собирает в себе разные сервисы. А значит много команд будут коммитить что-то в него, при этом сложно назначить явно ответственного
- по смыслу этот сервис очень уязвим к проблеме утекания бизнес-логики из микросервисов в себя, но этого строго там не должно быть

## Consequences

Хотя Gateway это не BFF, он может взять на себя простейшую часть этой работы - сбор данных из разных источников и формирование нужных полей ответа возможно.
Также нужно следить, чтобы Gateway также не содержал никакой бизнес-логики, а только делал вызовы внутренних сервисов.