# 8. backend-spec-errors

Date: 2024-11-12

## Status

Accepted

## Context

Мы хотим раскрывать на фронт как можно меньше информации, чтобы злоумышленники знали как можно меньше.

Текстовые коды ошибок в этом разрезе представляют некоторую информацию, не говоря уже о описании или технической ошибке.

## Decision

Решено на фронт отдавать цифровой код, и только его. В спецификации требуется вести справочник ошибок с описанием.

## Consequences

Поскольку у нас микросервисная архитектура, решено делать коды двухуровневыми, условно код_сервиса.код_ошибки.

Также решено выделить скоуп кодов для общий generic-ошибок, например AuthenticationRequired.

Также решено использовать для ответа на фронт с ошибкой всегда код 400, независимо от ситуации, даже при Internal Server Error.

Задача ведения словаря ошибок требует проработки. Актуализация словаря чисто руками не будет работать из-за загрузки (и лени) разработчиков,
нужен дополнительный инструмент-помощник. Сюда же относится и задача указания конкретных возможных ошибок в спецификации эндпоинта