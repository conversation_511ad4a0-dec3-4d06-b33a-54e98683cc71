# 6. keycloak

Date: 2024-09-03

## Status

Accepted

## Context

Нам необходима авторизация для ограничения доступов к разным эндпоинтам нашего сервиса.

## Decision

Для ускорения разработки решено использовать Keycloak как сервис, предлагающий из коробки решения по авторизации.
Он может выступать полноценным openID connect провайдером, в том числе осуществляющим single sign on для разных сервисов.
Для нас эта часть избыточна, и мы не хотим публиковать дополнительную точку наружу. Также у нас будет сервис OTP, в случае
отдельного сервиса нужны будут доделки по функционалу выпуска и валидации OTP.
Решено разворачивать Keycloak в контуре, и использовать его функционал по части работы с токенами.
Конкретно заведение пользователя и смена ролей в Keycloak после смены статуса в сервисе Users будет выполняться от имени специальной учетки админа для приложения.
Выпуск токенов для юзеров будет происходить c помощью token exchange flow.
![zaman_keycloak flow](../docs/zaman_keycloak.png)
Конфигурация может быть осуществлена [по этому мануалу](https://github.com/masalinas/poc-keycloak-token-exchange).
- [Сконфигурированный реалм](../docs/realm-export.json)
- [Коллекция Postman с примерами запросов к Keycloak](../docs/zaman%20Keycloak.postman_collection.json)

## Consequences

Мы не используем Keycloak как полноценный openID connect provider, и не должны хранить в нем никакую информацию по пользователю.
Кроме той, которая необходима нам для решения задач авторизации.
Мы можем брать из JWT токена данные о пользователе для обогащения контекста запроса на бэкэнде, возможно для этой задачи еще какие-то поля будет решено хранить тоже.
В приведенных примерах конфигурации указана только необходимая часть для прямой интеграции.
Требуется доконфигурация самого Keycloak в части инфраструктуры для production, а также настройки токенов тоже уточнить с дефолтных.
Также в выбранном флоу все запросы отправляются на клиента-админа, который потом или через admin API изменяет юзера, или через /token работает и с получение и с рефрешем.
