package qazpost

import (
	"fmt"
)

type HTTPError struct {
	StatusCode int
	Status     string
	Message    string
}

func (e *HTTPError) Error() string {
	return fmt.Sprintf("HTTP error: %d %s - %s", e.StatusCode, e.Status, e.Message)
}

type AuthResponse struct {
	AccessToken   string `json:"accessToken"`
	Status        string `json:"status"`
	StatusMessage string `json:"statusMessage"`
}

type NpiSearchResponseData struct {
	Postcode   string `json:"postcode"`
	AddressKaz string `json:"addressKaz"`
	AddressRus string `json:"addressRus"`
	AddressLat string `json:"addressLat"`
}

type NpiSearchResponse struct {
	Data []NpiSearchResponseData `json:"data"`
}

type OldIndexResponseInfo struct {
	ResponseCode    string `json:"responseCode"`
	ResponseGenTime int64  `json:"responseGenTime"`
	ResponseText    string `json:"responseText"`
}

type OldIndexResponse struct {
	OldPostcode  string               `json:"oldpostcode"`
	ResponseInfo OldIndexResponseInfo `json:"responseInfo"`
}

type NpiSearchResult struct {
	Postcode   string `json:"postcode"`
	AddressRus string `json:"addressRus"`
}

type OldIndexResult struct {
	OldPostcode string `json:"oldpostcode"`
}
