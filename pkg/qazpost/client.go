package qazpost

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"time"

	"github.com/rs/zerolog"
)

var _ QazPostProvider = (*client)(nil)

const (
	defaultTimeout = 10 * time.Second
	loginPath      = "/npi-integration/api/auth/login"
	searchPath     = "/npi-integration/api/npi/search"
	oldIndexPath   = "/npi-integration/api/npi/oldIndex"
)

type client struct {
	baseURL    *url.URL
	httpClient *http.Client
	cfg        *Config
	logger     *zerolog.Logger
}

func NewClient(cfg *Config, log *zerolog.Logger) (QazPostProvider, error) {
	if cfg == nil {
		return nil, errors.New("qazpost config cannot be nil")
	}
	parsedURL, err := url.Parse(cfg.BaseURL)
	if err != nil {
		return nil, fmt.Errorf("invalid base URL: %w", err)
	}

	timeout := defaultTimeout
	if cfg.Timeout > 0 {
		timeout = cfg.Timeout
	}

	httpClient := &http.Client{
		Timeout: timeout,
	}

	return &client{
		baseURL:    parsedURL,
		httpClient: httpClient,
		cfg:        cfg,
		logger:     log,
	}, nil
}

func (c *client) GetAuthToken(ctx context.Context) (string, error) {
	// Prepare request
	bodyBytes, err := json.Marshal(map[string]string{
		"username": c.cfg.Username,
		"password": c.cfg.Password,
	})
	if err != nil {
		return "", fmt.Errorf("failed to marshal auth request body: %w", err)
	}

	// Create request
	loginURL := c.baseURL.ResolveReference(&url.URL{Path: loginPath})
	req, err := http.NewRequestWithContext(ctx, http.MethodPost, loginURL.String(), bytes.NewReader(bodyBytes))
	if err != nil {
		return "", fmt.Errorf("failed to create auth request: %w", err)
	}
	req.Header.Set("Content-Type", "application/json")

	// Execute request
	resp, err := c.httpClient.Do(req)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	// Read and process response
	respBodyBytes, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", fmt.Errorf("failed to read auth response: %w", err)
	}

	if resp.StatusCode != http.StatusOK {
		return "", &HTTPError{
			StatusCode: resp.StatusCode,
			Status:     resp.Status,
			Message:    string(respBodyBytes),
		}
	}

	// Parse response
	var authResp AuthResponse
	if err = json.Unmarshal(respBodyBytes, &authResp); err != nil {
		return "", fmt.Errorf("failed to decode auth response: %w", err)
	}

	if authResp.Status != "OK" {
		return "", fmt.Errorf("auth operation returned non-OK status: %s - %s",
			authResp.Status, authResp.StatusMessage)
	}

	if authResp.AccessToken == "" {
		return "", errors.New("auth response missing access token")
	}

	return authResp.AccessToken, nil
}

// SearchNPI searches for the New Postal Index (NPI).
func (c *client) SearchNPI(ctx context.Context, token, addressQuery string) ([]NpiSearchResult, error) {
	// Prepare URL
	searchURL := c.baseURL.ResolveReference(&url.URL{Path: searchPath})
	query := url.Values{}
	query.Set("query", addressQuery)
	searchURL.RawQuery = query.Encode()

	// Create request
	req, err := http.NewRequestWithContext(ctx, http.MethodGet, searchURL.String(), nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create search request: %w", err)
	}
	req.Header.Set("Authorization", "Bearer "+token)

	// Execute request
	resp, err := c.httpClient.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	// Handle empty response
	if resp.StatusCode == http.StatusNoContent {
		return []NpiSearchResult{}, nil
	}

	// Read response body
	respBodyBytes, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read search response: %w", err)
	}

	// Check status code
	if resp.StatusCode != http.StatusOK {
		return nil, &HTTPError{
			StatusCode: resp.StatusCode,
			Status:     resp.Status,
			Message:    string(respBodyBytes),
		}
	}

	// Parse response
	var searchResp NpiSearchResponse
	if err = json.Unmarshal(respBodyBytes, &searchResp); err != nil {
		return nil, fmt.Errorf("failed to decode search response: %w", err)
	}

	// Transform response
	results := make([]NpiSearchResult, len(searchResp.Data))
	for i, data := range searchResp.Data {
		results[i] = NpiSearchResult{
			Postcode:   data.Postcode,
			AddressRus: data.AddressRus,
		}
	}

	return results, nil
}

// GetOldIndex retrieves the Old Postal Index (SPI).
func (c *client) GetOldIndex(ctx context.Context, token, npi string) (*OldIndexResult, error) {
	// Validate input
	if npi == "" {
		return nil, errors.New("NPI cannot be empty for GetOldIndex")
	}

	// Prepare URL
	oldIndexURL := c.baseURL.ResolveReference(&url.URL{Path: oldIndexPath})
	query := url.Values{}
	query.Set("postcode", npi)
	oldIndexURL.RawQuery = query.Encode()

	// Create request
	req, err := http.NewRequestWithContext(ctx, http.MethodGet, oldIndexURL.String(), nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create old index request: %w", err)
	}
	req.Header.Set("Authorization", "Bearer "+token)

	// Execute request
	resp, err := c.httpClient.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	// Read response body
	respBodyBytes, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read old index response: %w", err)
	}

	// Check status code
	if resp.StatusCode != http.StatusOK {
		return nil, &HTTPError{
			StatusCode: resp.StatusCode,
			Status:     resp.Status,
			Message:    string(respBodyBytes),
		}
	}

	// Parse response
	var oldIndexResp OldIndexResponse
	if err = json.Unmarshal(respBodyBytes, &oldIndexResp); err != nil {
		return nil, fmt.Errorf("failed to decode old index response: %w", err)
	}

	// Check response status
	if oldIndexResp.ResponseInfo.ResponseCode != "0" {
		return nil, fmt.Errorf("old index operation returned non-OK status: code=%s, text=%s",
			oldIndexResp.ResponseInfo.ResponseCode, oldIndexResp.ResponseInfo.ResponseText)
	}

	return &OldIndexResult{
		OldPostcode: oldIndexResp.OldPostcode,
	}, nil
}
