package gkb

import (
	"context"
	"net/http"

	"git.redmadrobot.com/zaman/backend/zaman/pkg/utils"
)

type (
	// GkbProvider определяет интерфейс для операций с Государственным кредитным бюро
	GkbProvider interface {
		// SendLoanApplicationInfo отправляет заявку на кредит в Государственное кредитное бюро
		SendLoanApplicationInfo(ctx context.Context, params ParamsGkbSendLoanApplicationInfo) (*GkbSendLoanApplicationInfoResponse, error)
	}

	// GkbProviderImpl реализует интерфейс GkbProvider
	GkbProviderImpl struct {
		HTTPClient *http.Client
		BaseURL    string
		Username   string
		Password   string
	}
)

// NewGkbProvider создает новый экземпляр поставщика Государственного кредитного бюро
func NewGkbProvider(cfg *Config) GkbProvider {
	// insecureSkipVerify = true - отключаем проверку SSL
	// enableUserIDHeader = cfg.UseMock - добавляем user_id header

	httpClient := utils.CreateHTTPClientWithCertAndUserID(cfg.CustomCertBase64, cfg.UseMock)
	return &GkbProviderImpl{
		HTTPClient: httpClient,
		BaseURL:    cfg.BaseURL,
		Username:   cfg.Username,
		Password:   cfg.Password,
	}
}
