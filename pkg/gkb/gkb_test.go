package gkb

import (
	"context"
	"encoding/xml"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"git.redmadrobot.com/backend-go/rmr-pkg/tools/conversion"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

const (
	testUsername = "test_user"
	testPassword = "test_pass"
)

// Example success response XML
const successResponseXML = `
<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:data="http://data.chdb.scb.kz" xmlns:rep="http://data.chdb.scb.kz" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <soapenv:HeaderRequest>
    <userId>b16cadf5-5afe-453d-9685-3933dba966a5</userId>
    <creditorId>test-creditor</creditorId>
    <timestamp>2024-03-14T12:00:00</timestamp>
  </soapenv:HeaderRequest>
  <soapenv:Body>
    <data:createApplicationResponse>
      <result>
        <resultCode>1</resultCode>
        <resultMessage>success</resultMessage>
      </result>
    </data:createApplicationResponse>
  </soapenv:Body>
</soapenv:Envelope>
`

// Example validation fault response XML
const validationFaultResponseXML = `
<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:data="http://data.chdb.scb.kz" xmlns:rep="http://data.chdb.scb.kz" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <soapenv:Body>
    <soapenv:Fault>
      <faultcode>soap:Server</faultcode>
      <faultstring>List of constraint violations</faultstring>
      <detail>
        <data:ValidationFault>
          <constraintViolations>
            <code>SBF-VE-8</code>
            <description>Номер «ИИН» должен содержать 12 цифр</description>
          </constraintViolations>
          <constraintViolations>
            <code>VAL-C-444</code>
            <description>Поле «Сумма кредита» должно быть заполнено и не может быть меньше 0</description>
          </constraintViolations>
        </data:ValidationFault>
      </detail>
    </soapenv:Fault>
  </soapenv:Body>
</soapenv:Envelope>
`

// Example malformed XML response
const malformedResponseXML = `
<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/">
  <soapenv:Body><
    <data:createApplicationResponse>
      <result>
        <resultCode>1</resultCode>
        <resultMessage>success</resultMessage>
      </result>
    </data:createApplicationResponse>
  </soapenv:Body>
</soapenv:Envelope>
`

func getTestGKBClient(url string, client *http.Client) *GkbProviderImpl {
	return &GkbProviderImpl{
		HTTPClient: client,
		BaseURL:    url,
		Username:   testUsername,
		Password:   testPassword,
	}
}

func TestGkbProviderImpl_SendLoanApplication(t *testing.T) {
	tests := []struct {
		name           string
		params         ParamsGkbSendLoanApplicationInfo
		response       string
		httpStatus     int
		wantErr        bool
		validateResult func(*testing.T, *GkbSendLoanApplicationInfoResponse)
	}{
		{
			name: "success case",
			params: ParamsGkbSendLoanApplicationInfo{
				Iin:                   "123456789012",
				Sum:                   float64(100000),
				LoanPurpose:           defaultLoanPurpose,
				LoanObject:            defaultLoanObject,
				CreditorApplicationID: conversion.Ptr("test-app-id"),
				ApplicationTimestamp:  conversion.Ptr(time.Now()),
			},
			response:   successResponseXML,
			httpStatus: http.StatusOK,
			wantErr:    false,
			validateResult: func(t *testing.T, resp *GkbSendLoanApplicationInfoResponse) {
				assert.Equal(t, 1, resp.Body.CreateApplicationResponse.Result.ResultCode)
				assert.Equal(t, "success", resp.Body.CreateApplicationResponse.Result.ResultMessage)
				assert.Equal(t, "b16cadf5-5afe-453d-9685-3933dba966a5", resp.Header.UserID)
				assert.Equal(t, "test-creditor", resp.Header.CreditorID)
				assert.Equal(t, "2024-03-14T12:00:00", resp.Header.Timestamp)
			},
		},
		{
			name: "validation error case",
			params: ParamsGkbSendLoanApplicationInfo{
				Iin:                   "123",      // Invalid IIN
				Sum:                   float64(0), // Invalid amount
				LoanPurpose:           defaultLoanPurpose,
				LoanObject:            defaultLoanObject,
				CreditorApplicationID: conversion.Ptr("test-app-id"),
				ApplicationTimestamp:  conversion.Ptr(time.Now()),
			},
			response:   validationFaultResponseXML,
			httpStatus: http.StatusBadRequest,
			wantErr:    true,
		},
		{
			name: "malformed XML case",
			params: ParamsGkbSendLoanApplicationInfo{
				Iin:                   "123456789012",
				Sum:                   float64(100000),
				LoanPurpose:           defaultLoanPurpose,
				LoanObject:            defaultLoanObject,
				CreditorApplicationID: conversion.Ptr("test-app-id"),
				ApplicationTimestamp:  conversion.Ptr(time.Now()),
			},
			response:   malformedResponseXML,
			httpStatus: http.StatusOK,
			wantErr:    true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create mock server
			mockServer := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
				// Verify request method
				assert.Equal(t, http.MethodPost, r.Method)

				// Verify basic auth
				username, password, ok := r.BasicAuth()
				require.True(t, ok)
				assert.Equal(t, testUsername, username)
				assert.Equal(t, testPassword, password)

				// Verify content type
				assert.Equal(t, "application/xml", r.Header.Get("Content-Type"))

				// Send response
				w.WriteHeader(tt.httpStatus)
				w.Header().Set("Content-Type", "application/xml")
				_, err := w.Write([]byte(tt.response))
				require.NoError(t, err)
			}))
			defer mockServer.Close()

			// Create client with mock server URL
			client := &GkbProviderImpl{
				HTTPClient: &http.Client{},
				BaseURL:    mockServer.URL,
				Username:   testUsername,
				Password:   testPassword,
			}

			// Send request
			got, err := client.SendLoanApplicationInfo(context.Background(), tt.params)

			// Validate results
			if tt.wantErr {
				assert.Error(t, err)
				assert.Nil(t, got)
				return
			}

			require.NoError(t, err)
			assert.NotNil(t, got)

			if tt.validateResult != nil {
				tt.validateResult(t, got)
			}
		})
	}
}

func TestGkbProviderImpl_SendLoanApplication_Validation(t *testing.T) {
	tests := []struct {
		name    string
		params  ParamsGkbSendLoanApplicationInfo
		wantErr error
	}{
		{
			name: "valid parameters",
			params: ParamsGkbSendLoanApplicationInfo{
				Iin:                   "123456789012",
				Sum:                   float64(100000),
				LoanPurpose:           defaultLoanPurpose,
				LoanObject:            defaultLoanObject,
				CreditorApplicationID: conversion.Ptr("test-app-id"),
				ApplicationTimestamp:  conversion.Ptr(time.Now()),
			},
			wantErr: nil,
		},
		{
			name: "invalid IIN length",
			params: ParamsGkbSendLoanApplicationInfo{
				Iin:                   "123",
				Sum:                   float64(100000),
				LoanPurpose:           defaultLoanPurpose,
				LoanObject:            defaultLoanObject,
				CreditorApplicationID: conversion.Ptr("test-app-id"),
				ApplicationTimestamp:  conversion.Ptr(time.Now()),
			},
			wantErr: ErrInvalidIIN,
		},
		{
			name: "invalid amount",
			params: ParamsGkbSendLoanApplicationInfo{
				Iin:                   "123456789012",
				Sum:                   float64(0),
				LoanPurpose:           defaultLoanPurpose,
				LoanObject:            defaultLoanObject,
				CreditorApplicationID: conversion.Ptr("test-app-id"),
				ApplicationTimestamp:  conversion.Ptr(time.Now()),
			},
			wantErr: ErrInvalidAmount,
		},
		{
			name: "missing creditor application ID",
			params: ParamsGkbSendLoanApplicationInfo{
				Iin:                   "123456789012",
				Sum:                   float64(100000),
				LoanPurpose:           defaultLoanPurpose,
				LoanObject:            defaultLoanObject,
				CreditorApplicationID: conversion.Ptr(""),
				ApplicationTimestamp:  conversion.Ptr(time.Now()),
			},
			wantErr: ErrMissingCreditorAppID,
		},
		{
			name: "missing timestamp",
			params: ParamsGkbSendLoanApplicationInfo{
				Iin:                   "123456789012",
				Sum:                   float64(100000),
				LoanPurpose:           defaultLoanPurpose,
				LoanObject:            defaultLoanObject,
				CreditorApplicationID: conversion.Ptr("test-app-id"),
				ApplicationTimestamp:  conversion.Ptr(time.Time{}),
			},
			wantErr: ErrMissingTimestamp,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := validateParams(tt.params)
			if tt.wantErr != nil {
				assert.Error(t, err)
				assert.Equal(t, tt.wantErr, err)
				return
			}
			assert.NoError(t, err)
		})
	}
}

func TestGkbProviderImpl_SendLoanApplication_ServerError(t *testing.T) {
	mockServer := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusInternalServerError)
		w.Header().Set("Content-Type", "application/xml")
		err := xml.NewEncoder(w).Encode(&GkbValidationFault{
			XMLNS: struct {
				Soapenv string `xml:"xmlns:soapenv,attr"`
				Data    string `xml:"xmlns:data,attr"`
				Rep     string `xml:"xmlns:rep,attr"`
				Xsi     string `xml:"xmlns:xsi,attr"`
			}{
				Soapenv: gkbXMLNsSoapenv,
				Data:    gkbXMLNsData,
				Rep:     gkbXMLNsRep,
				Xsi:     gkbXMLNsXsi,
			},
			Body: struct {
				Fault struct {
					FaultCode   string `xml:"faultcode"`
					FaultString string `xml:"faultstring"`
					Detail      struct {
						ValidationFault struct {
							ConstraintViolations []struct {
								Code        string `xml:"code"`
								Description string `xml:"description"`
							} `xml:"constraintViolations"`
						} `xml:"ValidationFault"`
					} `xml:"detail"`
				} `xml:"Fault"`
			}{
				Fault: struct {
					FaultCode   string `xml:"faultcode"`
					FaultString string `xml:"faultstring"`
					Detail      struct {
						ValidationFault struct {
							ConstraintViolations []struct {
								Code        string `xml:"code"`
								Description string `xml:"description"`
							} `xml:"constraintViolations"`
						} `xml:"ValidationFault"`
					} `xml:"detail"`
				}{
					FaultCode:   "SOAP-ENV:Server",
					FaultString: "Internal server error",
					Detail: struct {
						ValidationFault struct {
							ConstraintViolations []struct {
								Code        string `xml:"code"`
								Description string `xml:"description"`
							} `xml:"constraintViolations"`
						} `xml:"ValidationFault"`
					}{
						ValidationFault: struct {
							ConstraintViolations []struct {
								Code        string `xml:"code"`
								Description string `xml:"description"`
							} `xml:"constraintViolations"`
						}{
							ConstraintViolations: []struct {
								Code        string `xml:"code"`
								Description string `xml:"description"`
							}{
								{
									Code:        "ERROR_CODE",
									Description: "Error description",
								},
							},
						},
					},
				},
			},
		})
		require.NoError(t, err)
	}))
	defer mockServer.Close()

	client := getTestGKBClient(mockServer.URL, mockServer.Client())

	params := ParamsGkbSendLoanApplicationInfo{
		Iin:                   "123456789012",
		Sum:                   float64(100000),
		LoanPurpose:           defaultLoanPurpose,
		LoanObject:            defaultLoanObject,
		CreditorApplicationID: conversion.Ptr("test-app-id"),
		ApplicationTimestamp:  conversion.Ptr(time.Now()),
	}

	got, err := client.SendLoanApplicationInfo(context.Background(), params)
	assert.Error(t, err)
	assert.Nil(t, got)
}

func TestGkbProviderImpl_SendLoanApplication_InvalidResponse(t *testing.T) {
	mockServer := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		w.Header().Set("Content-Type", "application/xml")
		// Send invalid XML response
		w.Write([]byte(`<invalid>xml</invalid>`))
	}))
	defer mockServer.Close()

	client := getTestGKBClient(mockServer.URL, mockServer.Client())

	params := ParamsGkbSendLoanApplicationInfo{
		Iin:                   "123456789012",
		Sum:                   float64(100000),
		LoanPurpose:           defaultLoanPurpose,
		LoanObject:            defaultLoanObject,
		CreditorApplicationID: conversion.Ptr("test-app-id"),
		ApplicationTimestamp:  conversion.Ptr(time.Now()),
	}

	got, err := client.SendLoanApplicationInfo(context.Background(), params)
	assert.Error(t, err)
	assert.Nil(t, got)
}
