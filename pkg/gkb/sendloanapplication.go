package gkb

import (
	"bytes"
	"context"
	"encoding/base64"
	"encoding/xml"
	"fmt"
	"io"
	"net/http"
)

const (
	sendLoanApplicationEndpoint = "/creditHistoryDataServices/OnlineApplicationService/CreateApplication"
)

// SendLoanApplicationInfo отправляет заявку на кредит в Государственное кредитное бюро
func (p *GkbProviderImpl) SendLoanApplicationInfo(ctx context.Context, params ParamsGkbSendLoanApplicationInfo) (*GkbSendLoanApplicationInfoResponse, error) {
	// Проверяем входные параметры
	if err := validateParams(params); err != nil {
		return nil, fmt.Errorf("invalid parameters: %w", err)
	}

	// Создаем SOAP запрос
	req := NewGkbSendLoanApplicationInfoRequest(params)

	// Маршалируем запрос в XML
	reqXML, err := xml.MarshalIndent(req, "", "  ")
	if err != nil {
		return nil, fmt.Errorf("error marshaling request: %w", err)
	}

	// Создаем HTTP запрос
	httpReq, err := http.NewRequestWithContext(ctx, "POST", fmt.Sprintf("%s%s", p.BaseURL, sendLoanApplicationEndpoint), bytes.NewBuffer(reqXML))
	if err != nil {
		return nil, fmt.Errorf("error creating request: %w", err)
	}

	// Устанавливаем заголовки
	httpReq.Header.Set("Content-Type", "application/xml")

	// Устанавливаем Basic Auth
	auth := base64.StdEncoding.EncodeToString([]byte(fmt.Sprintf("%s:%s", p.Username, p.Password)))
	httpReq.Header.Set("Authorization", fmt.Sprintf("Basic %s", auth))

	// Отправляем запрос
	resp, err := p.HTTPClient.Do(httpReq)
	if err != nil {
		return nil, fmt.Errorf("error sending request: %w", err)
	}
	defer resp.Body.Close()

	// Читаем тело ответа
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("error reading response: %w", err)
	}

	// Проверяем, является ли ответ ошибкой
	if resp.StatusCode != http.StatusOK {
		var fault GkbValidationFault
		if err := xml.Unmarshal(body, &fault); err != nil {
			return nil, fmt.Errorf("error unmarshaling fault response: %w", err)
		}
		return nil, fmt.Errorf("SOAP fault: %s", fault.Body.Fault.FaultString)
	}

	// Парсим успешный ответ
	var soapResp GkbSendLoanApplicationInfoResponse
	if err := xml.Unmarshal(body, &soapResp); err != nil {
		return nil, fmt.Errorf("error unmarshaling response: %w", err)
	}

	return &soapResp, nil
}

// validateParams проверяет входные параметры
func validateParams(params ParamsGkbSendLoanApplicationInfo) error {
	if len(params.Iin) != 12 {
		return ErrInvalidIIN
	}

	if params.Sum <= 0 {
		return ErrInvalidAmount
	}

	if params.CreditorApplicationID != nil {
		if *params.CreditorApplicationID == "" {
			return ErrMissingCreditorAppID
		}
	}

	if params.ApplicationTimestamp.IsZero() {
		return ErrMissingTimestamp
	}

	return nil
}
