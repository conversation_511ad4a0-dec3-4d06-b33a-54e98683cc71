package gkb

import "errors"

var (
	ErrGkbInternal = errors.New("gkb internal error")

	// Validation errors
	ErrInvalidIIN               = errors.New("invalid IIN: must be 12 digits")
	ErrInvalidAmount            = errors.New("invalid amount: must be greater than 0")
	ErrMissingCreditorAppID     = errors.New("missing creditor application ID")
	ErrMissingTimestamp         = errors.New("missing application timestamp")
	ErrInvalidLoanPurposeObject = errors.New("invalid loan purpose or object")
)

// ValidationError represents a validation error from the GKB service
type ValidationError struct {
	Code        string
	Description string
}

func (e *ValidationError) Error() string {
	return e.Description
}
