package gkb

import (
	"encoding/xml"
	"time"
)

const (
	gkbXMLNsSoapenv = "http://schemas.xmlsoap.org/soap/envelope/"
	gkbXMLNsData    = "http://data.chdb.scb.kz"
	gkbXMLNsRep     = "http://data.chdb.scb.kz"
	gkbXMLNsXsi     = "http://www.w3.org/2001/XMLSchema-instance"
	gkbXMLNsNs2     = "http://data.chdb.scb.kz"

	defaultUserID      = "b16cadf5-5afe-453d-9685-3933dba966a5"
	defaultLoanPurpose = 1
	defaultLoanObject  = 10

	gkbDateFormat = "2006-01-02T15:04:05"

	// Error codes
	ErrCodeAmountValidation  = "VAL-C-444"
	ErrCodeLoanPurposeObject = "SBF-VE-14"
	ErrCodeIINValidation     = "SBF-VE-8"
	ErrCodeSuccess           = "1"
	ErrCodeInvalidValue      = "0"
)

// ParamsGkbSendLoanApplicationInfo represents the parameters for sending loan application info
type ParamsGkbSendLoanApplicationInfo struct {
	Iin                   string     `json:"iin"`
	Sum                   float64    `json:"sum"`
	LoanPurpose           int32      `json:"loanPurpose"`
	LoanObject            int32      `json:"loanObject"`
	CreditorApplicationID *string    `json:"creditorApplicationId"`
	ApplicationTimestamp  *time.Time `json:"applicationTimestamp"`
}

type (
	// GkbSendLoanApplicationInfoRequest represents the SOAP request structure
	GkbSendLoanApplicationInfoRequest struct {
		XMLName xml.Name `xml:"Envelope"`
		XMLNS   struct {
			Soapenv string `xml:"xmlns:soapenv,attr"`
			Data    string `xml:"xmlns:data,attr"`
			Rep     string `xml:"xmlns:rep,attr"`
			Xsi     string `xml:"xmlns:xsi,attr"`
		} `xml:"soapenv:Envelope"`
		Header struct {
			UserID string `xml:"userId"`
		} `xml:"HeaderRequest"`
		Body struct {
			CreateApplication struct {
				Params struct {
					OnlineApplication struct {
						Iin                   string  `xml:"iin"`
						Sum                   float64 `xml:"sum"`
						LoanPurpose           int32   `xml:"loanPurpose"`
						LoanObject            int32   `xml:"loanObject"`
						CreditorApplicationID *string `xml:"creditorApplicationId"`
						ApplicationTimestamp  string  `xml:"applicationTimestamp"`
					} `xml:"onlineApplication"`
				} `xml:"params"`
			} `xml:"createApplication"`
		} `xml:"Body"`
	}

	// GkbSendLoanApplicationInfoResponse represents the SOAP response structure
	GkbSendLoanApplicationInfoResponse struct {
		XMLName xml.Name `xml:"Envelope"`
		XMLNS   struct {
			Soapenv string `xml:"xmlns:soapenv,attr"`
			Data    string `xml:"xmlns:data,attr"`
			Rep     string `xml:"xmlns:rep,attr"`
			Xsi     string `xml:"xmlns:xsi,attr"`
		} `xml:"soapenv:Envelope"`
		Header struct {
			UserID     string `xml:"userId"`
			CreditorID string `xml:"creditorId"`
			Timestamp  string `xml:"timestamp"`
		} `xml:"HeaderRequest"`
		Body struct {
			CreateApplicationResponse struct {
				Result struct {
					ResultCode    int    `xml:"resultCode"`
					ResultMessage string `xml:"resultMessage"`
				} `xml:"result"`
			} `xml:"createApplicationResponse"`
		} `xml:"Body"`
	}

	// GkbValidationFault represents the SOAP fault response structure
	GkbValidationFault struct {
		XMLName xml.Name `xml:"Envelope"`
		XMLNS   struct {
			Soapenv string `xml:"xmlns:soapenv,attr"`
			Data    string `xml:"xmlns:data,attr"`
			Rep     string `xml:"xmlns:rep,attr"`
			Xsi     string `xml:"xmlns:xsi,attr"`
		} `xml:"soapenv:Envelope"`
		Body struct {
			Fault struct {
				FaultCode   string `xml:"faultcode"`
				FaultString string `xml:"faultstring"`
				Detail      struct {
					ValidationFault struct {
						ConstraintViolations []struct {
							Code        string `xml:"code"`
							Description string `xml:"description"`
						} `xml:"constraintViolations"`
					} `xml:"ValidationFault"`
				} `xml:"detail"`
			} `xml:"Fault"`
		} `xml:"Body"`
	}
)

// NewGkbSendLoanApplicationInfoRequest creates a new SOAP request for sending loan application info
func NewGkbSendLoanApplicationInfoRequest(params ParamsGkbSendLoanApplicationInfo) *GkbSendLoanApplicationInfoRequest {
	return &GkbSendLoanApplicationInfoRequest{
		XMLNS: struct {
			Soapenv string `xml:"xmlns:soapenv,attr"`
			Data    string `xml:"xmlns:data,attr"`
			Rep     string `xml:"xmlns:rep,attr"`
			Xsi     string `xml:"xmlns:xsi,attr"`
		}{
			Soapenv: gkbXMLNsSoapenv,
			Data:    gkbXMLNsData,
			Rep:     gkbXMLNsRep,
			Xsi:     gkbXMLNsXsi,
		},
		Header: struct {
			UserID string `xml:"userId"`
		}{
			UserID: defaultUserID,
		},
		Body: struct {
			CreateApplication struct {
				Params struct {
					OnlineApplication struct {
						Iin                   string  `xml:"iin"`
						Sum                   float64 `xml:"sum"`
						LoanPurpose           int32   `xml:"loanPurpose"`
						LoanObject            int32   `xml:"loanObject"`
						CreditorApplicationID *string `xml:"creditorApplicationId"`
						ApplicationTimestamp  string  `xml:"applicationTimestamp"`
					} `xml:"onlineApplication"`
				} `xml:"params"`
			} `xml:"createApplication"`
		}{
			CreateApplication: struct {
				Params struct {
					OnlineApplication struct {
						Iin                   string  `xml:"iin"`
						Sum                   float64 `xml:"sum"`
						LoanPurpose           int32   `xml:"loanPurpose"`
						LoanObject            int32   `xml:"loanObject"`
						CreditorApplicationID *string `xml:"creditorApplicationId"`
						ApplicationTimestamp  string  `xml:"applicationTimestamp"`
					} `xml:"onlineApplication"`
				} `xml:"params"`
			}{
				Params: struct {
					OnlineApplication struct {
						Iin                   string  `xml:"iin"`
						Sum                   float64 `xml:"sum"`
						LoanPurpose           int32   `xml:"loanPurpose"`
						LoanObject            int32   `xml:"loanObject"`
						CreditorApplicationID *string `xml:"creditorApplicationId"`
						ApplicationTimestamp  string  `xml:"applicationTimestamp"`
					} `xml:"onlineApplication"`
				}{
					OnlineApplication: struct {
						Iin                   string  `xml:"iin"`
						Sum                   float64 `xml:"sum"`
						LoanPurpose           int32   `xml:"loanPurpose"`
						LoanObject            int32   `xml:"loanObject"`
						CreditorApplicationID *string `xml:"creditorApplicationId"`
						ApplicationTimestamp  string  `xml:"applicationTimestamp"`
					}{
						Iin:                   params.Iin,
						Sum:                   params.Sum,
						LoanPurpose:           defaultLoanPurpose,
						LoanObject:            defaultLoanObject,
						CreditorApplicationID: params.CreditorApplicationID,
						ApplicationTimestamp:  params.ApplicationTimestamp.Format(gkbDateFormat),
					},
				},
			},
		},
	}
}
