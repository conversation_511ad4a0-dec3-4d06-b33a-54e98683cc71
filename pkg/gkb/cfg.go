package gkb

import (
	"git.redmadrobot.com/backend-go/rmr-pkg/core/cfg"
	"git.redmadrobot.com/backend-go/rmr-pkg/core/cfg/viperx"
	"github.com/spf13/viper"
)

const (
	CfgGKBBaseURL          cfg.Key = "GKB_BASE_URL"
	CfgGKBLogin            cfg.Key = "GKB_USERNAME"
	CfgGKBPassword         cfg.Key = "GKB_PASSWORD"
	CfgGKBUseMock          cfg.Key = "GKB_USE_MOCK"
	CfgGKBCustomCertBase64 cfg.Key = "GKB_CUSTOM_CERT_BASE64"

	defaultGKBBaseURL       = ""
	defaultGKBLogin         = "login"
	defaultGKBPassword      = "password"
	deafultGKBUseMock       = false
	defaultCustomCertBase64 = ""
)

type (
	// Config represents the configuration for the GKB provider
	Config struct {
		// BaseURL - базовый URL сервиса GKB
		BaseURL string `env:"GKB_BASE_URL"`
		// Username - имя пользователя для аутентификации
		Username string `env:"GKB_USERNAME"`
		// Password - пароль для аутентификации
		Password string `env:"GKB_PASSWORD"`
		// UseMock - флаг использования mock-сервиса и вкладывания user_id
		UseMock bool `env:"GKB_USE_MOCK"`
		// CustomCertBase64 - base64-encoded сертификат в формате PEM
		CustomCertBase64 string `env:"GKB_CUSTOM_CERT_BASE64"`
	}
)

func CfgFromViper(loader *viper.Viper, keyMapping ...cfg.KeyMap) *Config {
	loader.SetDefault(CfgGKBBaseURL.String(), defaultGKBBaseURL)
	loader.SetDefault(CfgGKBLogin.String(), defaultGKBLogin)
	loader.SetDefault(CfgGKBPassword.String(), defaultGKBPassword)
	loader.SetDefault(CfgGKBUseMock.String(), deafultGKBUseMock)
	loader.SetDefault(CfgGKBCustomCertBase64.String(), defaultCustomCertBase64)

	return &Config{
		BaseURL:          viperx.Get(loader, CfgGKBBaseURL.Map(keyMapping...), ""),
		Username:         viperx.Get(loader, CfgGKBLogin.Map(keyMapping...), ""),
		Password:         viperx.Get(loader, CfgGKBPassword.Map(keyMapping...), ""),
		UseMock:          viperx.Get(loader, CfgGKBUseMock.Map(keyMapping...), deafultGKBUseMock),
		CustomCertBase64: viperx.Get(loader, CfgGKBCustomCertBase64.Map(keyMapping...), defaultCustomCertBase64),
	}
}
