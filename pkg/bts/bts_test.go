package bts

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/http/httptest"
	"reflect"
	"testing"

	"github.com/google/uuid"
	"github.com/stretchr/testify/require"
)

func TestClient_GetLiveness3DPhoto(t *testing.T) {
	mockServer := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		switch r.Header.Get("Authorization") {
		case "Bearer test":
			w.<PERSON><PERSON>().Set("request_id", "test")
			w.<PERSON><PERSON>().Set("Content-Type", "image/jpeg")
			w.<PERSON>er().Set("Content-Length", fmt.Sprintf("%d", len([]byte("test"))))
			w.<PERSON>rite<PERSON>ead<PERSON>(http.StatusOK)
			_, err := w.Write([]byte("test"))
			require.NoError(t, err)
		case "Bearer test-err":
			w.<PERSON><PERSON>().Set("request_id", "test-err")
			w.<PERSON>rite<PERSON>ead<PERSON>(http.StatusInternalServerError)
			w.<PERSON><PERSON>().Set("Content-Type", "application/json")
			w.<PERSON><PERSON>().Set("Content-Length", "0")
			err := json.NewEncoder(w).Encode(map[string]any{
				"error":            "some internal error",
				"errorDescription": "some internal error desc",
			})
			require.NoError(t, err)
		default:
			w.Header().Set("request_id", "test-auth-err")
			w.WriteHeader(http.StatusUnauthorized)
			w.Header().Set("Content-Type", "application/json")
			w.Header().Set("Content-Length", "0")
			err := json.NewEncoder(w).Encode(map[string]any{
				"error":            "some auth error",
				"errorDescription": "some auth error desc",
			})
			require.NoError(t, err)
		}
	}))
	defer mockServer.Close()

	c := &Client{
		HTTPClient:          &http.Client{},
		BaseURL:             mockServer.URL,
		COIDProxyURL:        mockServer.URL,
		Token:               "test",
		Login:               "test",
		Password:            "test",
		COIDAdapterLogin:    "test",
		COIDAdapterPassword: "test",
	}

	type args struct {
		ctx     context.Context
		reqData GetLiveness3DPhotoReq
	}
	tests := []struct {
		name    string
		args    args
		want    *GetLiveness3DPhotoResp
		wantErr bool
	}{
		{
			name: "success",
			args: args{
				ctx: context.Background(),
				reqData: GetLiveness3DPhotoReq{
					Token: "test",
				},
			},
			want: &GetLiveness3DPhotoResp{
				RequestID: "test",
				Photo:     []byte("test"),
			},
			wantErr: false,
		},
		{
			name: "internal server error",
			args: args{
				ctx: nil,
				reqData: GetLiveness3DPhotoReq{
					Token: "Bearer test-err",
				},
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "auth error",
			args: args{
				ctx: context.Background(),
				reqData: GetLiveness3DPhotoReq{
					Token: "",
				},
			},
			want:    nil,
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := c.GetLiveness3DPhoto(tt.args.ctx, tt.args.reqData)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetLiveness3DPhoto() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetLiveness3DPhoto() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestClient_GetLiveness3DVideo(t *testing.T) {
	mockServer := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		switch r.Header.Get("Authorization") {
		case "Bearer test":
			w.Header().Set("request_id", "test")
			w.Header().Set("Content-Type", "video/mp4")
			w.Header().Set("Content-Length", fmt.Sprintf("%d", len([]byte("test"))))
			w.WriteHeader(http.StatusOK)
			_, err := w.Write([]byte("test"))
			require.NoError(t, err)
			return
		case "Bearer test-err":
			w.Header().Set("request_id", "test-err")
			w.WriteHeader(http.StatusInternalServerError)
			w.Header().Set("Content-Type", "application/json")
			w.Header().Set("Content-Length", "0")
			err := json.NewEncoder(w).Encode(map[string]any{
				"error":            "some internal error",
				"errorDescription": "some internal error desc",
			})
			require.NoError(t, err)
			return
		default:
			w.Header().Set("request_id", "test-auth-err")
			w.WriteHeader(http.StatusUnauthorized)
			w.Header().Set("Content-Type", "application/json")
			w.Header().Set("Content-Length", "0")
			err := json.NewEncoder(w).Encode(map[string]any{
				"error":            "some auth error",
				"errorDescription": "some auth error desc",
			})
			require.NoError(t, err)
			return
		}
	}))
	defer mockServer.Close()

	c := &Client{
		HTTPClient:          &http.Client{},
		BaseURL:             mockServer.URL,
		COIDProxyURL:        mockServer.URL,
		Token:               "test",
		Login:               "test",
		Password:            "test",
		COIDAdapterLogin:    "test",
		COIDAdapterPassword: "test",
	}

	type args struct {
		ctx     context.Context
		reqData GetLiveness3DVideoReq
	}
	tests := []struct {
		name    string
		args    args
		want    *GetLiveness3DVideoResp
		wantErr bool
	}{
		{
			name: "success",
			args: args{
				ctx: context.Background(),
				reqData: GetLiveness3DVideoReq{
					Token: "test",
				},
			},
			want: &GetLiveness3DVideoResp{
				RequestID: "test",
				Video:     []byte("test"),
			},
			wantErr: false,
		},
		{
			name: "internal server error",
			args: args{
				ctx: nil,
				reqData: GetLiveness3DVideoReq{
					Token: "Bearer test-err",
				},
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "auth error",
			args: args{
				ctx: context.Background(),
				reqData: GetLiveness3DVideoReq{
					Token: "",
				},
			},
			want:    nil,
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := c.GetLiveness3DVideo(tt.args.ctx, tt.args.reqData)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetLiveness3DVideo() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetLiveness3DVideo() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestClient_GetPersonalData(t *testing.T) {
	personalDataRespExample := GetPersonalDataResp{
		Person: Person{
			Name:        "Some test name",
			Patronymic:  "Some test patronymic",
			Surname:     "Some test surname",
			Mobilephone: "88005553535",
			Iin:         "222222222221",
			BirthDate:   "1990-01-01",
			BirthPlace:  BirthPlace{},
			RegAddress:  RegAddress{},
			Documents:   Documents{},
			Citizenship: Citizenship{},
			LifeStatus:  LifeStatus{},
			Gender:      Gender{},
			Nationality: Nationality{},
		},
	}

	mockServer := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		query := r.URL.Query().Get("iin")
		switch query {
		case "222222222221":
			w.Header().Set("request_id", "test")
			w.WriteHeader(http.StatusOK)
			w.Header().Set("Content-Type", "application/json")
			err := json.NewEncoder(w).Encode(personalDataRespExample)
			require.NoError(t, err)
		case "222222222222":
			w.Header().Set("request_id", "test-err")
			w.WriteHeader(http.StatusInternalServerError)
			w.Header().Set("Content-Type", "application/json")
			w.Header().Set("Content-Length", "0")
			err := json.NewEncoder(w).Encode(map[string]any{
				"error":            "some internal error",
				"errorDescription": "some internal error desc",
			})
			require.NoError(t, err)
		case "222222222223":
			w.Header().Set("request_id", "test-err")
			w.WriteHeader(http.StatusNotFound)
			w.Header().Set("Content-Type", "application/json")
			w.Header().Set("Content-Length", "0")
			err := json.NewEncoder(w).Encode(map[string]any{
				"error":            "some not_found error",
				"errorDescription": "some not_found desc",
			})
			require.NoError(t, err)
		default:
			w.Header().Set("request_id", "test-auth-err")
			w.WriteHeader(http.StatusUnauthorized)
			w.Header().Set("Content-Type", "application/json")
			w.Header().Set("Content-Length", "0")
			err := json.NewEncoder(w).Encode(map[string]any{
				"error":            "some auth error",
				"errorDescription": "some auth error desc",
			})
			require.NoError(t, err)
		}
	}))
	defer mockServer.Close()

	c := &Client{
		HTTPClient:          &http.Client{},
		BaseURL:             mockServer.URL,
		COIDProxyURL:        mockServer.URL,
		Token:               "test",
		Login:               "test",
		Password:            "test",
		COIDAdapterLogin:    "test",
		COIDAdapterPassword: "test",
	}

	type args struct {
		ctx     context.Context
		reqData GetPersonalDataReq
	}

	tests := []struct {
		name    string
		args    args
		want    *GetPersonalDataResp
		wantErr bool
	}{
		{
			name: "success",
			args: args{
				ctx: context.Background(),
				reqData: GetPersonalDataReq{
					IIN:       "222222222221",
					RequestID: "test",
				},
			},
			want:    &personalDataRespExample,
			wantErr: false,
		},
		{
			name: "internal server error",
			args: args{
				ctx: context.Background(),
				reqData: GetPersonalDataReq{
					IIN:       "222222222222",
					RequestID: "test",
				},
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "not found error",
			args: args{
				ctx: context.Background(),
				reqData: GetPersonalDataReq{
					IIN:       "222222222223",
					RequestID: "test",
				},
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "auth error",
			args: args{
				ctx: context.Background(),
				reqData: GetPersonalDataReq{
					IIN:       "222222222223",
					RequestID: "test",
				},
			},
			want:    nil,
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := c.GetPersonalData(tt.args.ctx, tt.args.reqData)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetPersonalData() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetPersonalData() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestClient_GetToken(t *testing.T) {
	exampleGetTokenResp := GetTokenResp{
		AccessToken: "some access token",
		ExpiresIn:   1000,
		IDToken:     "some id token",
		Scope:       "some scope",
		TokenType:   "some token type",
		RequestID:   "test",
	}

	mockServer := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		err := r.ParseForm()
		require.NoError(t, err)
		code := r.FormValue("code")

		switch code {
		case "test":
			w.Header().Set("request_id", "test")
			w.WriteHeader(http.StatusOK)
			w.Header().Set("Content-Type", "application/json")
			err := json.NewEncoder(w).Encode(exampleGetTokenResp)
			require.NoError(t, err)
		case "test-err":
			w.Header().Set("request_id", "test-err")
			w.WriteHeader(http.StatusInternalServerError)
			w.Header().Set("Content-Type", "application/json")
			w.Header().Set("Content-Length", "0")
			err := json.NewEncoder(w).Encode(map[string]any{
				"error":            "some internal error",
				"errorDescription": "some internal error desc",
			})
			require.NoError(t, err)
		default:
			w.Header().Set("request_id", "test-auth-err")
			w.WriteHeader(http.StatusUnauthorized)
			w.Header().Set("Content-Type", "application/json")
			w.Header().Set("Content-Length", "0")
			err := json.NewEncoder(w).Encode(map[string]any{
				"error":            "some auth error",
				"errorDescription": "some auth error desc",
			})
			require.NoError(t, err)
		}
	}))
	defer mockServer.Close()

	c := &Client{
		HTTPClient:          &http.Client{},
		BaseURL:             mockServer.URL,
		COIDProxyURL:        mockServer.URL,
		Token:               "test",
		Login:               "test",
		Password:            "test",
		COIDAdapterLogin:    "test",
		COIDAdapterPassword: "test",
	}

	type args struct {
		ctx     context.Context
		reqData GetTokenReq
	}
	tests := []struct {
		name    string
		args    args
		want    *GetTokenResp
		wantErr bool
	}{
		{
			name: "success",
			args: args{
				ctx: context.Background(),
				reqData: GetTokenReq{
					Code:        "test",
					RedirectURI: "some-url",
					GrantType:   "authorization_code",
				},
			},
			want: &exampleGetTokenResp,
		},
		{
			name: "internal servererror",
			args: args{
				ctx: context.Background(),
				reqData: GetTokenReq{
					Code:        "test-err",
					RedirectURI: "some-url",
					GrantType:   "authorization_code",
				},
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "unauthorized err",
			args: args{
				ctx: context.Background(),
				reqData: GetTokenReq{
					Code:        "err",
					RedirectURI: "some-url",
					GrantType:   "authorization_code",
				},
			},
			want:    nil,
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := c.GetToken(tt.args.ctx, tt.args.reqData)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetToken() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetToken() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestClient_CreateTrustedPhone(t *testing.T) {
	exampleTrustedPhoneResp := &TrustedPhoneResp{
		RequestID: uuid.New().String(),
		Secret:    uuid.New().String(),
	}

	mockServer := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// Check request body
		bodyBytes, err := io.ReadAll(r.Body)
		require.NoError(t, err)

		actualReqBody := &TrustedPhoneReq{}

		if len(bodyBytes) > 0 {
			decoder := json.NewDecoder(bytes.NewReader(bodyBytes))
			err = decoder.Decode(&actualReqBody)
			require.NoError(t, err)
		}

		switch actualReqBody.Phone {
		case "test":
			w.Header().Set("requestId", exampleTrustedPhoneResp.RequestID)
			w.WriteHeader(http.StatusOK)
			w.Header().Set("Content-Type", "application/json")
			err = json.NewEncoder(w).Encode(exampleTrustedPhoneResp)
			require.NoError(t, err)
		case "test-err":
			fallthrough
		default:
			w.Header().Set("requestId", "test-err")
			w.WriteHeader(http.StatusInternalServerError)
			w.Header().Set("Content-Type", "application/json")
			w.Header().Set("Content-Length", "0")
			err := json.NewEncoder(w).Encode(map[string]any{
				"error":            "some internal error",
				"errorDescription": "some internal error desc",
			})
			require.NoError(t, err)
		}
	}))
	defer mockServer.Close()

	c := &Client{
		HTTPClient:          &http.Client{},
		BaseURL:             mockServer.URL,
		COIDProxyURL:        mockServer.URL,
		Token:               "test",
		Login:               "test",
		Password:            "test",
		COIDAdapterLogin:    "test",
		COIDAdapterPassword: "test",
	}

	tests := []struct {
		name    string
		req     *TrustedPhoneReq
		want    *TrustedPhoneResp
		wantErr bool
	}{
		{
			name: "success",
			req: &TrustedPhoneReq{
				Phone: "test",
			},
			want:    exampleTrustedPhoneResp,
			wantErr: false,
		},
		{
			name: "internal server error",
			req: &TrustedPhoneReq{
				Phone: "test-err",
			},
			want:    nil,
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := c.CreateTrustedPhone(context.Background(), *tt.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("CreateTrustedPhone() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("CreateTrustedPhone() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestClient_UploadBinaryFileForSigning(t *testing.T) {
	exampleUploadResp := &UploadFileResp{
		RequestID: uuid.New().String(),
		SignID:    uuid.New().String(),
	}

	mockServer := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// Check request body
		bodyBytes, err := io.ReadAll(r.Body)
		require.NoError(t, err)

		actualReqBody := &UploadFileReq{}

		if len(bodyBytes) > 0 {
			decoder := json.NewDecoder(bytes.NewReader(bodyBytes))
			err = decoder.Decode(&actualReqBody)
			require.NoError(t, err)
		}

		switch actualReqBody.Name {
		case "test":
			w.Header().Set("requestId", exampleUploadResp.RequestID)
			w.WriteHeader(http.StatusOK)
			w.Header().Set("Content-Type", "application/json")
			err = json.NewEncoder(w).Encode(exampleUploadResp)
			require.NoError(t, err)
		case "test-err":
			fallthrough
		default:
			w.Header().Set("request_id", "test-err")
			w.WriteHeader(http.StatusInternalServerError)
			w.Header().Set("Content-Type", "application/json")
			w.Header().Set("Content-Length", "0")
			err := json.NewEncoder(w).Encode(map[string]any{
				"error":            "some internal error",
				"errorDescription": "some internal error desc",
			})
			require.NoError(t, err)
		}
	}))
	defer mockServer.Close()

	c := &Client{
		HTTPClient:          &http.Client{},
		BaseURL:             mockServer.URL,
		COIDProxyURL:        mockServer.URL,
		Token:               "test",
		Login:               "test",
		Password:            "test",
		COIDAdapterLogin:    "test",
		COIDAdapterPassword: "test",
	}

	tests := []struct {
		name    string
		req     *UploadFileReq
		want    *UploadFileResp
		wantErr bool
	}{
		{
			name: "success",
			req: &UploadFileReq{
				Name:  "test",
				Bytes: "bytes",
			},
			want:    exampleUploadResp,
			wantErr: false,
		},
		{
			name: "internal server error",
			req: &UploadFileReq{
				Name:  "test-err",
				Bytes: "bytes",
			},
			want:    nil,
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := c.UploadBinaryFileForSigning(context.Background(), *tt.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("UploadBinaryFileForSigning() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("UploadBinaryFileForSigning() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestClient_GetSignedPdfFiles(t *testing.T) {
	exampleBtsResponse := []SignedFile{
		{
			SignID:         uuid.New().String(),
			Name:           uuid.New().String(),
			SignedPDF:      uuid.New().String(),
			DocumentCopy:   uuid.New().String(),
			RegCertificate: uuid.New().String(),
			QrLink:         uuid.New().String(),
		},
	}

	exampleGetFilesResp := &GetSignedFilesResp{
		RequestID: uuid.New().String(),
		Files:     exampleBtsResponse,
	}

	mockServer := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		switch r.Header.Get("Authorization") {
		case "Bearer test":
			w.Header().Set("requestId", exampleGetFilesResp.RequestID)
			w.Header().Set("Content-Type", "application/json")
			w.WriteHeader(http.StatusOK)
			err := json.NewEncoder(w).Encode(exampleBtsResponse)
			require.NoError(t, err)
			return
		case "Bearer test-err":
			fallthrough
		default:
			w.Header().Set("request_id", "test-err")
			w.WriteHeader(http.StatusInternalServerError)
			w.Header().Set("Content-Type", "application/json")
			w.Header().Set("Content-Length", "0")
			err := json.NewEncoder(w).Encode(map[string]any{
				"error":            "some internal error",
				"errorDescription": "some internal error desc",
			})
			require.NoError(t, err)
			return
		}
	}))
	defer mockServer.Close()

	c := &Client{
		HTTPClient:          &http.Client{},
		BaseURL:             mockServer.URL,
		COIDProxyURL:        mockServer.URL,
		Token:               "test",
		Login:               "test",
		Password:            "test",
		COIDAdapterLogin:    "test",
		COIDAdapterPassword: "test",
	}

	tests := []struct {
		name    string
		req     GetSignedFilesReq
		want    *GetSignedFilesResp
		wantErr bool
	}{
		{
			name: "success",
			req: GetSignedFilesReq{
				Token: "test",
			},
			want:    exampleGetFilesResp,
			wantErr: false,
		},
		{
			name: "internal server error",
			req: GetSignedFilesReq{
				Token: "test-err",
			},
			want:    nil,
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := c.GetSignedPdfFiles(context.Background(), tt.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetSignedPdfFiles() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetSignedPdfFiles() got = %v, want %v", got, tt.want)
			}
		})
	}
}
