package bts

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"

	"git.redmadrobot.com/backend-go/rmr-pkg/core/errs"
)

type (
	GetTokenReq struct {
		Code        string `json:"code"`
		RedirectURI string `json:"redirect_uri"`
		GrantType   string `json:"grant_type"`
	}

	GetTokenResp struct {
		AccessToken string `json:"access_token"`
		ExpiresIn   int    `json:"expires_in"`
		IDToken     string `json:"id_token"`
		Scope       string `json:"scope"`
		TokenType   string `json:"token_type"`
		RequestID   string `json:"request_id"`
	}

	GetLiveness3DPhotoReq struct {
		Token string
	}

	GetLiveness3DPhotoResp struct {
		RequestID string `json:"request_id"`
		Photo     []byte `json:"photo"`
	}

	GetLiveness3DVideoReq struct {
		Token string
	}

	GetLiveness3DVideoResp struct {
		RequestID string `json:"request_id"`
		Video     []byte `json:"video"`
	}

	GetPersonalDataReq struct {
		IIN       string `json:"iin"`
		RequestID string `json:"request_id"`
	}

	BirthPlace struct {
		Country  Country  `json:"country"`
		City     string   `json:"city"`
		District District `json:"district"`
		Region   Region   `json:"region"`
	}

	Country struct {
		NameRu     string      `json:"nameRu"`
		Code       interface{} `json:"code"`
		NameKz     string      `json:"nameKz"`
		ChangeDate string      `json:"changeDate"`
	}

	District struct {
		NameRu     string      `json:"nameRu"`
		Code       interface{} `json:"code"`
		NameKz     string      `json:"nameKz"`
		ChangeDate string      `json:"changeDate"`
	}

	Region struct {
		NameRu     string      `json:"nameRu"`
		Code       interface{} `json:"code"`
		NameKz     string      `json:"nameKz"`
		ChangeDate string      `json:"changeDate"`
	}

	RegAddress struct {
		Country    Country          `json:"country"`
		ArCode     interface{}      `json:"arCode"`
		BeginDate  string           `json:"beginDate"`
		Street     string           `json:"street"`
		Flat       int              `json:"flat"`
		District   District         `json:"district"`
		Region     Region           `json:"region"`
		Building   interface{}      `json:"building"`
		City       string           `json:"city"`
		Corpus     string           `json:"corpus"`
		Invalidity Invalidity       `json:"invalidity"`
		Status     RegAddressStatus `json:"status"`
	}

	Invalidity struct {
		NameRu     string      `json:"nameRu"`
		Code       interface{} `json:"code"`
		NameKz     string      `json:"nameKz"`
		ChangeDate string      `json:"changeDate"`
	}

	RegAddressStatus struct {
		NameRu     string      `json:"nameRu"`
		Code       interface{} `json:"code"`
		NameKz     string      `json:"nameKz"`
		ChangeDate string      `json:"changeDate"`
	}

	Document struct {
		Number            string                    `json:"number"`
		BeginDate         string                    `json:"beginDate"`
		Patronymic        string                    `json:"patronymic"`
		EndDate           string                    `json:"endDate"`
		Surname           string                    `json:"surname"`
		Name              string                    `json:"name"`
		Type              DocumentType              `json:"type"`
		BirthDate         string                    `json:"birthDate"`
		IssueOrganization DocumentIssueOrganization `json:"issueOrganization"`
		Status            DocumentStatus            `json:"status"`
	}

	DocumentType struct {
		NameRu     string      `json:"nameRu"`
		Code       interface{} `json:"code"`
		NameKz     string      `json:"nameKz"`
		ChangeDate string      `json:"changeDate"`
	}

	DocumentIssueOrganization struct {
		NameRu     string      `json:"nameRu"`
		Code       interface{} `json:"code"`
		NameKz     string      `json:"nameKz"`
		ChangeDate string      `json:"changeDate"`
	}

	DocumentStatus struct {
		NameRu     string      `json:"nameRu"`
		Code       interface{} `json:"code"`
		NameKz     string      `json:"nameKz"`
		ChangeDate string      `json:"changeDate"`
	}

	Documents struct {
		Document []Document `json:"document"`
	}

	Citizenship struct {
		NameRu     string      `json:"nameRu"`
		Code       interface{} `json:"code"`
		NameKz     string      `json:"nameKz"`
		ChangeDate string      `json:"changeDate"`
	}

	LifeStatus struct {
		NameRu     string      `json:"nameRu"`
		Code       interface{} `json:"code"`
		NameKz     string      `json:"nameKz"`
		ChangeDate string      `json:"changeDate"`
	}

	Gender struct {
		NameRu     string      `json:"nameRu"`
		Code       interface{} `json:"code"`
		NameKz     string      `json:"nameKz"`
		ChangeDate string      `json:"changeDate"`
	}

	Nationality struct {
		NameRu     string      `json:"nameRu"`
		Code       interface{} `json:"code"`
		NameKz     string      `json:"nameKz"`
		ChangeDate string      `json:"changeDate"`
	}

	Person struct {
		Name        string      `json:"name"`
		Patronymic  string      `json:"patronymic"`
		Surname     string      `json:"surname"`
		Mobilephone string      `json:"mobilephone"`
		Iin         string      `json:"iin"`
		BirthDate   string      `json:"birthDate"`
		BirthPlace  BirthPlace  `json:"birthPlace"`
		RegAddress  RegAddress  `json:"regAddress"`
		Documents   Documents   `json:"documents"`
		Citizenship Citizenship `json:"citizenship"`
		LifeStatus  LifeStatus  `json:"lifeStatus"`
		Gender      Gender      `json:"gender"`
		Nationality Nationality `json:"nationality"`
	}

	GetPersonalDataResp struct {
		Person Person `json:"person"`
	}

	TrustedPhoneReq struct {
		Phone string `json:"phone"`
	}

	TrustedPhoneResp struct {
		RequestID string
		Secret    string `json:"secret"`
	}

	UploadFileReq struct {
		Name  string  `json:"name"`
		Bytes string  `json:"bytes"`
		Link  *string `json:"link,omitempty"`
	}

	UploadFileResp struct {
		RequestID string
		SignID    string `json:"signableId"`
	}

	GetSignedFilesReq struct {
		Token string
	}

	GetSignedFilesResp struct {
		RequestID string
		Files     []SignedFile
	}

	SignedFile struct {
		SignID         string `json:"signableId"`
		Name           string `json:"name"`
		SignedPDF      string `json:"signedPdf"`
		DocumentCopy   string `json:"documentCopy"`
		RegCertificate string `json:"registrationCertificate"`
		QrLink         string `json:"qrLink"`
	}
)

func NewGetTokenResp(respBody io.ReadCloser, respHeader http.Header) (*GetTokenResp, error) {
	reqID := respHeader.Get("request_id")

	var result GetTokenResp
	if err := json.NewDecoder(respBody).Decode(&result); err != nil {
		return nil, fmt.Errorf("error decoding http response body: %w", err)
	}
	result.RequestID = reqID

	return &result, nil
}

func (r *GetTokenReq) GetMultipartFormData() (*bytes.Buffer, error) {
	data := url.Values{}
	data.Set("grant_type", r.GrantType)
	data.Set("code", r.Code)
	data.Set("redirect_uri", r.RedirectURI)
	body := bytes.NewBufferString(data.Encode())

	return body, nil
}

func NewGetLiveness3DPhotoResp(respBody io.ReadCloser, respHeader http.Header) (*GetLiveness3DPhotoResp, error) {
	reqID := respHeader.Get("request_id")

	var (
		result GetLiveness3DPhotoResp
		buf    bytes.Buffer
	)

	if _, err := io.Copy(&buf, respBody); err != nil {
		return nil, fmt.Errorf("error decoding http response body: %w", err)
	}

	contentType := respHeader.Get("Content-Type")
	if contentType != "image/jpeg" {
		return nil, fmt.Errorf("unexpected content type: %s, expected image/jpeg", contentType)
	}

	imgData := buf.Bytes()
	result.RequestID = reqID
	result.Photo = imgData

	return &result, nil
}

func newGetPersonalDataResp(respBody io.ReadCloser) (*GetPersonalDataResp, error) {
	var result GetPersonalDataResp
	if err := json.NewDecoder(respBody).Decode(&result); err != nil {
		var respBodyStruct map[string]interface{}
		if err = json.NewDecoder(respBody).Decode(&respBodyStruct); err != nil {
			return nil, errs.Wrapf(err, "failed to decode person http response to map")
		}

		return nil, errs.Wrapf(err, "failed to decode person http response: %v", respBodyStruct)
	}

	return &result, nil
}

func NewGetLiveness3DVideoResp(respBody io.ReadCloser, respHeader http.Header) (*GetLiveness3DVideoResp, error) {
	reqID := respHeader.Get("request_id")

	var (
		result GetLiveness3DVideoResp
		buf    bytes.Buffer
	)

	if _, err := io.Copy(&buf, respBody); err != nil {
		return nil, fmt.Errorf("error decoding http response body: %w", err)
	}

	contentType := respHeader.Get("Content-Type")
	if contentType != "video/mp4" {
		return nil, fmt.Errorf("unexpected content type: %s, expected video/mp4", contentType)
	}

	videoData := buf.Bytes()
	result.RequestID = reqID
	result.Video = videoData

	return &result, nil
}

func NewTrustedPhoneResp(respBody io.ReadCloser, respHeader http.Header) (*TrustedPhoneResp, error) {
	reqID := respHeader.Get("requestId")

	var result TrustedPhoneResp
	if err := json.NewDecoder(respBody).Decode(&result); err != nil {
		return nil, fmt.Errorf("error decoding http response body: %w", err)
	}
	result.RequestID = reqID

	return &result, nil
}

func NewUploadFileResp(respBody io.ReadCloser, respHeader http.Header) (*UploadFileResp, error) {
	reqID := respHeader.Get("requestId")

	var result UploadFileResp
	if err := json.NewDecoder(respBody).Decode(&result); err != nil {
		return nil, fmt.Errorf("error decoding http response body: %w", err)
	}
	result.RequestID = reqID

	return &result, nil
}

func NewGetSignedFilesResp(respBody io.ReadCloser, respHeader http.Header) (*GetSignedFilesResp, error) {
	reqID := respHeader.Get("requestId")

	var files []SignedFile
	if err := json.NewDecoder(respBody).Decode(&files); err != nil {
		return nil, fmt.Errorf("error decoding http response body: %w", err)
	}

	return &GetSignedFilesResp{
		Files:     files,
		RequestID: reqID,
	}, nil
}
