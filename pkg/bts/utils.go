package bts

import (
	"fmt"
	"net/http"
)

const (
	StatusOriginIsUnreachable = 523
)

func handleStatusCode(statusCode int, bodyBytes []byte) error {
	switch {
	case statusCode == http.StatusInternalServerError:
		return ErrInternalServerError
	case statusCode == http.StatusGatewayTimeout:
		return ErrStatusGatewayTimeout
	case statusCode == StatusOriginIsUnreachable:
		return ErrOriginIsUnreachable
	default:
		return fmt.Errorf("error status of http response [%d]: %s", statusCode, bodyBytes)
	}
}
