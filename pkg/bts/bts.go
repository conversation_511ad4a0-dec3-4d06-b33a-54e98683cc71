package bts

import (
	"bytes"
	"context"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"

	"git.redmadrobot.com/backend-go/rmr-pkg/core/logs"
)

var _ BTSProvider = (*Client)(nil)

type (
	BTSProvider interface {
		GetToken(ctx context.Context, reqData GetTokenReq) (*GetTokenResp, error)
		GetLiveness3DPhoto(ctx context.Context, reqData GetLiveness3DPhotoReq) (*GetLiveness3DPhotoResp, error)
		GetLiveness3DVideo(ctx context.Context, reqData GetLiveness3DVideoReq) (*GetLiveness3DVideoResp, error)
		GetPersonalData(ctx context.Context, reqData GetPersonalDataReq) (*GetPersonalDataResp, error)
		CreateTrustedPhone(ctx context.Context, reqData TrustedPhoneReq) (*TrustedPhoneResp, error)
		UploadBinaryFileForSigning(ctx context.Context, reqData UploadFileReq) (*UploadFileResp, error)
		GetSignedPdfFiles(ctx context.Context, reqData GetSignedFilesReq) (*GetSignedFilesResp, error)
	}

	Client struct {
		HTTPClient          *http.Client
		BaseURL             string
		COIDProxyURL        string
		Token               string
		Login               string
		Password            string
		COIDAdapterLogin    string
		COIDAdapterPassword string
	}
)

const (
	//nolint:gosec
	btsGetTokenPath           = "/oauth2/token"
	btsGetLiveness3DPhotoPath = "/api/v1/liveness-3d/photo"
	btsGetLiveness3DVideoPath = "/api/v1/liveness-3d/video"
	btsGetTrustedPhonePath    = "/api/v1/trusted-phone"
	btsUploadFilePath         = "/api/v2/oauth/signable/pdf"
	btsGetSignedPdfFilesPath  = "/api/v2/oauth/signatures/pdf"
)

func NewClient(ctx context.Context, cfg *Config) BTSProvider {
	logs.FromContext(ctx).Info().Msgf("creating new BTS client with base url: %s", cfg.BaseURL)

	return &Client{
		HTTPClient:          &http.Client{},
		BaseURL:             cfg.BaseURL,
		Token:               cfg.Token,
		COIDProxyURL:        cfg.COIDProxyURL,
		Login:               cfg.Login,
		Password:            cfg.Password,
		COIDAdapterLogin:    cfg.COIDAdapterLogin,
		COIDAdapterPassword: cfg.COIDAdapterPassword,
	}
}

func (c *Client) GetToken(ctx context.Context, reqData GetTokenReq) (*GetTokenResp, error) {
	req, err := c.newGetTokenReq(ctx, reqData)
	if err != nil {
		return nil, fmt.Errorf("failed to create new GetToken http request: %w", err)
	}

	resp, err := c.HTTPClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to do do GetToken http request: %w", err)
	}
	defer resp.Body.Close()

	switch {
	case resp.StatusCode == http.StatusOK:
		break
	case resp.StatusCode >= http.StatusInternalServerError:
		return nil, ErrInternalServerError
	default:
		bodyBytes, _ := io.ReadAll(resp.Body)
		return nil, fmt.Errorf("error status of http GetToken response [%d]: %s", resp.StatusCode, bodyBytes)
	}

	result, err := NewGetTokenResp(resp.Body, resp.Header)
	if err != nil {
		return nil, err
	}

	return result, nil
}

func (c *Client) GetLiveness3DPhoto(ctx context.Context, reqData GetLiveness3DPhotoReq) (*GetLiveness3DPhotoResp, error) {
	req, err := c.newGetLiveness3DPhotoReq(ctx, reqData)
	if err != nil {
		return nil, err
	}

	resp, err := c.HTTPClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to do GetLiveness3DPhoto http request: %w", err)
	}
	defer resp.Body.Close()

	switch {
	case resp.StatusCode == http.StatusOK:
		break
	case resp.StatusCode >= http.StatusInternalServerError:
		return nil, ErrInternalServerError
	default:
		bodyBytes, _ := io.ReadAll(resp.Body)
		return nil, fmt.Errorf("error status of http GetLiveness3DPhoto response [%d]: %s", resp.StatusCode, bodyBytes)
	}

	result, err := NewGetLiveness3DPhotoResp(resp.Body, resp.Header)
	if err != nil {
		return nil, err
	}

	return result, nil
}

func (c *Client) GetPersonalData(ctx context.Context, reqData GetPersonalDataReq) (*GetPersonalDataResp, error) {
	req, err := c.newGetPersonalDataReq(ctx, reqData)
	if err != nil {
		return nil, err
	}

	resp, err := c.HTTPClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to do GetPersonalData http request: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		bodyBytes, _ := io.ReadAll(resp.Body)
		return nil, handleStatusCode(resp.StatusCode, bodyBytes)
	}

	result, err := newGetPersonalDataResp(resp.Body)
	if err != nil {
		return nil, err
	}

	return result, nil
}

func (c *Client) GetLiveness3DVideo(ctx context.Context, reqData GetLiveness3DVideoReq) (*GetLiveness3DVideoResp, error) {
	req, err := c.newGetLiveness3DVideoReq(ctx, reqData)
	if err != nil {
		return nil, err
	}

	resp, err := c.HTTPClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to do GetLiveness3DVideo http request: %w", err)
	}
	defer resp.Body.Close()

	switch {
	case resp.StatusCode == http.StatusOK:
		break
	case resp.StatusCode >= http.StatusInternalServerError:
		return nil, ErrInternalServerError
	default:
		bodyBytes, _ := io.ReadAll(resp.Body)
		return nil, fmt.Errorf("error status of http GetLiveness3DVideo response [%d]: %s", resp.StatusCode, bodyBytes)
	}

	result, err := NewGetLiveness3DVideoResp(resp.Body, resp.Header)
	if err != nil {
		return nil, err
	}

	return result, nil
}

func (c *Client) CreateTrustedPhone(ctx context.Context, reqData TrustedPhoneReq) (*TrustedPhoneResp, error) {
	req, err := c.newTrustedPhoneReq(ctx, reqData)
	if err != nil {
		return nil, err
	}

	resp, err := c.HTTPClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to do CreateTrustedPhone http request: %w", err)
	}
	defer resp.Body.Close()

	switch {
	case resp.StatusCode == http.StatusOK:
		break
	case resp.StatusCode >= http.StatusInternalServerError:
		return nil, ErrInternalServerError
	default:
		bodyBytes, _ := io.ReadAll(resp.Body)
		return nil, fmt.Errorf("error status of http CreateTrustedPhone response [%d]: %s", resp.StatusCode, bodyBytes)
	}

	result, err := NewTrustedPhoneResp(resp.Body, resp.Header)
	if err != nil {
		return nil, err
	}

	return result, nil
}

func (c *Client) UploadBinaryFileForSigning(ctx context.Context, reqData UploadFileReq) (*UploadFileResp, error) {
	req, err := c.newUploadFileReq(ctx, reqData)
	if err != nil {
		return nil, err
	}

	resp, err := c.HTTPClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to do UploadBinaryFileForSigning http request: %w", err)
	}
	defer resp.Body.Close()

	switch {
	case resp.StatusCode == http.StatusOK:
		break
	case resp.StatusCode >= http.StatusInternalServerError:
		return nil, ErrInternalServerError
	default:
		bodyBytes, _ := io.ReadAll(resp.Body)
		return nil, fmt.Errorf("error status of http UploadBinaryFileForSigning response [%d]: %s", resp.StatusCode, bodyBytes)
	}

	result, err := NewUploadFileResp(resp.Body, resp.Header)
	if err != nil {
		return nil, err
	}

	return result, nil
}

func (c *Client) GetSignedPdfFiles(ctx context.Context, reqData GetSignedFilesReq) (*GetSignedFilesResp, error) {
	req, err := c.newGetSignedPdfFilesReq(ctx, reqData)
	if err != nil {
		return nil, err
	}

	resp, err := c.HTTPClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to do GetSignedPdfFiles http request: %w", err)
	}
	defer resp.Body.Close()

	switch {
	case resp.StatusCode == http.StatusOK:
		break
	case resp.StatusCode >= http.StatusInternalServerError:
		return nil, ErrInternalServerError
	default:
		bodyBytes, _ := io.ReadAll(resp.Body)
		return nil, fmt.Errorf("error status of http GetSignedPdfFiles response [%d]: %s", resp.StatusCode, bodyBytes)
	}

	result, err := NewGetSignedFilesResp(resp.Body, resp.Header)
	if err != nil {
		return nil, err
	}

	return result, nil
}

func (c *Client) newGetTokenReq(ctx context.Context, reqData GetTokenReq) (*http.Request, error) {
	body, err := reqData.GetMultipartFormData()
	if err != nil {
		return nil, err
	}

	getTokenURL, err := c.getRequestURL(btsGetTokenPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequestWithContext(ctx, http.MethodPost, getTokenURL, body)
	if err != nil {
		return nil, fmt.Errorf("failed to create new GetToken http request: %w", err)
	}

	c.setBasicAuth(req, c.Login, c.Password)
	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")

	return req, nil
}

func (c *Client) setBasicAuth(req *http.Request, login, password string) {
	auth := login + ":" + password
	encoded := base64.StdEncoding.EncodeToString([]byte(auth))
	req.Header.Set("Authorization", "Basic "+encoded)
}

func (c *Client) newGetLiveness3DPhotoReq(ctx context.Context, reqData GetLiveness3DPhotoReq) (*http.Request, error) {
	liveness3DPhotoURL, err := c.getRequestURL(btsGetLiveness3DPhotoPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequestWithContext(ctx, http.MethodGet, liveness3DPhotoURL, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create new GetLiveness3DPhoto http request: %w", err)
	}
	req.Header.Set("Authorization", "Bearer "+reqData.Token)

	return req, nil
}

func (c *Client) newGetPersonalDataReq(ctx context.Context, reqData GetPersonalDataReq) (*http.Request, error) {
	req, err := http.NewRequestWithContext(ctx, http.MethodGet,
		fmt.Sprintf("%s?iin=%s&requestId=%s", c.COIDProxyURL, reqData.IIN, reqData.RequestID), nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create new GetPersonalData http request: %w", err)
	}

	req.SetBasicAuth(c.COIDAdapterLogin, c.COIDAdapterPassword)

	return req, nil
}

func (c *Client) newGetLiveness3DVideoReq(ctx context.Context, reqData GetLiveness3DVideoReq) (*http.Request, error) {
	liveness3DVideoURL, err := c.getRequestURL(btsGetLiveness3DVideoPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequestWithContext(ctx, http.MethodGet, liveness3DVideoURL, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create new GetLiveness3DVideo http request: %w", err)
	}
	req.Header.Set("Authorization", "Bearer "+reqData.Token)

	return req, nil
}

func (c *Client) newTrustedPhoneReq(ctx context.Context, reqData TrustedPhoneReq) (*http.Request, error) {
	trustedPhoneURL, err := c.getRequestURL(btsGetTrustedPhonePath)
	if err != nil {
		return nil, err
	}

	var buf bytes.Buffer
	err = json.NewEncoder(&buf).Encode(reqData)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequestWithContext(ctx, http.MethodPost, trustedPhoneURL, &buf)
	if err != nil {
		return nil, fmt.Errorf("failed to create TrustedPhone http request: %w", err)
	}

	c.setBasicAuth(req, c.Login, c.Password)
	req.Header.Set("Content-Type", "application/json")

	return req, nil
}

func (c *Client) newUploadFileReq(ctx context.Context, reqData UploadFileReq) (*http.Request, error) {
	uploadFileURL, err := c.getRequestURL(btsUploadFilePath)
	if err != nil {
		return nil, err
	}

	var buf bytes.Buffer
	err = json.NewEncoder(&buf).Encode(reqData)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequestWithContext(ctx, http.MethodPost, uploadFileURL, &buf)
	if err != nil {
		return nil, fmt.Errorf("failed to create UploadFile http request: %w", err)
	}

	c.setBasicAuth(req, c.Login, c.Password)
	req.Header.Set("Content-Type", "application/json")

	return req, nil
}

func (c *Client) newGetSignedPdfFilesReq(ctx context.Context, reqData GetSignedFilesReq) (*http.Request, error) {
	getFilesURL, err := c.getRequestURL(btsGetSignedPdfFilesPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequestWithContext(ctx, http.MethodGet, getFilesURL, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create GetSignedPdfFiles http request: %w", err)
	}
	req.Header.Set("Authorization", "Bearer "+reqData.Token)

	return req, nil
}

func (c *Client) getRequestURL(path string) (string, error) {
	result, err := url.JoinPath(c.BaseURL, path)
	if err != nil {
		return "", fmt.Errorf("failed to join path for url %s; path %s; err: %w", c.BaseURL, path, err)
	}
	return result, nil
}
