package bts

import (
	"github.com/spf13/viper"

	"git.redmadrobot.com/backend-go/rmr-pkg/core/cfg"
	"git.redmadrobot.com/backend-go/rmr-pkg/core/cfg/viperx"
)

const (
	// CfgBTSBaseURL ключ в конфиге для получения базового URL сервиса BTS.
	CfgBTSBaseURL cfg.Key = "BTS_BASE_URL"
	// CfgBTSToken ключ в конфиге для получения токена авторизации сервиса BTS.
	CfgBTSToken cfg.Key = "BTS_TOKEN"
	// CfgCOIDProxyURL ключ в конфиге для получения URL прокси для обращения в ЦОИД.
	CfgCOIDProxyURL cfg.Key = "BTS_COID_PROXY_URL"
	// CfgBTSLogin ключ в конфиге для получения логина сервиса BTS.
	CfgBTSLogin cfg.Key = "BTS_LOGIN"
	// CfgBTSPassword ключ в конфиге для получения пароля сервиса BTS.
	CfgBTSPassword cfg.Key = "BTS_PASSWORD"
	// CfgBTSCoidAdapterLogin ключ в конфиге для получения логина сервиса COID Adapter.
	CfgBTSCoidAdapterLogin cfg.Key = "BTS_COID_ADAPTER_LOGIN"
	// CfgBTSCoidAdapterPassword ключ в конфиге для получения пароля сервиса COID Adapter.
	CfgBTSCoidAdapterPassword cfg.Key = "BTS_COID_ADAPTER_PASSWORD" //nolint:gosec

	CfgDefaultBTSURL                 = ""
	CfgDefaultBTSToken               = ""
	CfgDefaultBTSCOIDAdapterLogin    = ""
	CfgDefaultBTSCOIDAdapterPassword = ""
	CfgDefaultCOIDProxyURL           = ""
	CfgDefaultBTSLogin               = ""
	CfgDefaultBTSPassword            = ""
)

type (
	Config struct {
		// BaseURL базовый URL сервиса BTS.
		BaseURL string `env:"BTS_BASE_URL"`
		// Token токен авторизации сервиса BTS.
		Token string `env:"BTS_TOKEN"`
		// COIDProxyURL URL прокси для обращения в ЦОИД.
		COIDProxyURL string `env:"BTS_COID_PROXY_URL"`
		// Login логин сервиса BTS.
		Login string `env:"BTS_LOGIN"`
		// Password пароль сервиса BTS.
		Password string `env:"BTS_PASSWORD"`
		// COIDAdapterLogin логин сервиса COID Adapter.
		COIDAdapterLogin string `env:"BTS_COID_ADAPTER_LOGIN"`
		// COIDAdapterPassword пароль сервиса COID Adapter.
		COIDAdapterPassword string `env:"BTS_COID_ADAPTER_PASSWORD"`
	}
)

func CfgFromViper(loader *viper.Viper, keyMapping ...cfg.KeyMap) *Config {
	loader.SetDefault(CfgBTSBaseURL.String(), CfgDefaultBTSURL)
	loader.SetDefault(CfgBTSToken.String(), CfgDefaultBTSToken)
	loader.SetDefault(CfgCOIDProxyURL.String(), CfgDefaultCOIDProxyURL)
	loader.SetDefault(CfgBTSLogin.String(), CfgDefaultBTSLogin)
	loader.SetDefault(CfgBTSPassword.String(), CfgDefaultBTSPassword)
	loader.SetDefault(CfgBTSCoidAdapterLogin.String(), CfgDefaultBTSCOIDAdapterLogin)
	loader.SetDefault(CfgBTSCoidAdapterPassword.String(), CfgDefaultBTSCOIDAdapterPassword)

	return &Config{
		BaseURL:             viperx.Get(loader, CfgBTSBaseURL.Map(keyMapping...), CfgDefaultBTSURL),
		Token:               viperx.Get(loader, CfgBTSToken.Map(keyMapping...), CfgDefaultBTSToken),
		COIDProxyURL:        viperx.Get(loader, CfgCOIDProxyURL.Map(keyMapping...), CfgDefaultCOIDProxyURL),
		Login:               viperx.Get(loader, CfgBTSLogin.Map(keyMapping...), CfgDefaultBTSLogin),
		Password:            viperx.Get(loader, CfgBTSPassword.Map(keyMapping...), CfgDefaultBTSPassword),
		COIDAdapterLogin:    viperx.Get(loader, CfgBTSCoidAdapterLogin.Map(keyMapping...), CfgDefaultBTSCOIDAdapterLogin),
		COIDAdapterPassword: viperx.Get(loader, CfgBTSCoidAdapterPassword.Map(keyMapping...), CfgDefaultBTSCOIDAdapterPassword),
	}
}
