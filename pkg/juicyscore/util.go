package juicyscore

import (
	"errors"
	"regexp"
)

func getFirstSixDigits(phoneNumber string) (string, error) {
	// validate phone number format - should start with + followed by digits
	re := regexp.MustCompile(`^\+\d+$`)
	if !re.MatchString(phoneNumber) {
		return "", errors.New("invalid phone number format")
	}

	// Remove the + prefix
	digitsOnly := phoneNumber[1:]

	// Get country code length (assuming 1-3 digits for country code)
	countryCodeLength := 1
	switch {
	case len(digitsOnly) >= 2 && (digitsOnly[0] == '1' || digitsOnly[0] == '7'):
		// US, Canada, Russia, Kazakhstan (1 digit country code)
		countryCodeLength = 1
	case len(digitsOnly) >= 3 && (digitsOnly[0:2] == "33" || digitsOnly[0:2] == "44" || digitsOnly[0:2] == "49"):
		// France, UK, Germany, etc. (2 digit country code)
		countryCodeLength = 2
	case len(digitsOnly) >= 4 && (digitsOnly[0:3] == "380" || digitsOnly[0:3] == "375"):
		// Ukraine, Belarus, etc. (3 digit country code)
		countryCodeLength = 3
	}

	// Check if there are at least 6 digits after country code
	if len(digitsOnly) < countryCodeLength+6 {
		return "", errors.New("phone number too short")
	}

	// Get first 6 digits after country code
	return digitsOnly[countryCodeLength : countryCodeLength+6], nil
}

func getCountryCode(phoneNumber string) (string, error) {
	// validate phone number format - should start with + followed by digits
	re := regexp.MustCompile(`^\+\d+$`)
	if !re.MatchString(phoneNumber) {
		return "", errors.New("invalid phone number format")
	}

	// Remove the + prefix
	digitsOnly := phoneNumber[1:]

	// Determine country code based on prefix
	if len(digitsOnly) >= 1 {
		if digitsOnly[0] == '1' {
			// US, Canada
			return "1", nil
		} else if digitsOnly[0] == '7' {
			// Russia, Kazakhstan
			return "7", nil
		}
	}

	if len(digitsOnly) >= 2 {
		prefix2 := digitsOnly[0:2]
		// Check common 2-digit country codes
		switch prefix2 {
		case "33": // France
			return "33", nil
		case "44": // UK
			return "44", nil
		case "49": // Germany
			return "49", nil
		case "86": // China
			return "86", nil
		}
	}

	if len(digitsOnly) >= 3 {
		prefix3 := digitsOnly[0:3]
		// Check common 3-digit country codes
		switch prefix3 {
		case "380": // Ukraine
			return "380", nil
		case "375": // Belarus
			return "375", nil
		case "998": // Uzbekistan
			return "998", nil
		}
	}

	// If we can't determine the country code, return the first digit as a fallback
	if len(digitsOnly) > 0 {
		return digitsOnly[0:1], nil
	}

	return "", errors.New("could not determine country code")
}
