package juicyscore

import (
	"context"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"strconv"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestJuicyScoreClient_GetJuicyScore(t *testing.T) {
	// Setup test server
	mockResponse := GetJuicyScoreResponse{
		AntiFraudScore: "0.21571",
		DeviceID:       "24120613515536598",
		BrowserHash:    "7f3da35eeda752849ecabbe2c28cbdc5",
		Success:        true,
	}

	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// Verify request headers
		assert.Equal(t, "application/json", r.Header.Get("Content-Type"))
		assert.Equal(t, "test-api-key", r.Header.Get("session"))

		// Decode request body
		var reqBody GetJuicyScoreRequest
		err := json.NewDecoder(r.Body).Decode(&reqBody)
		require.NoError(t, err)

		// Verify phone number handling
		assert.Equal(t, "7", reqBody.PhoneCountry)
		assert.Equal(t, "777123", reqBody.Phone)

		// Send mock response
		w.Header().Set("Content-Type", "application/json")
		json.NewEncoder(w).Encode(mockResponse)
	}))
	defer server.Close()

	// Create client
	client := NewJuicyScoreClient(
		&Config{
			Host:      server.URL,
			APIKey:    "test-api-key",
			AccountID: "test-account-id",
		},
	)

	// Test parameters
	params := ParamsGetJuicyScore{
		ApplicationID: "test-app-id",
		Amount:        100000,
		ClientID:      "test-client-id",
		SessionID:     "test-session-id",
		RequestTime:   time.Date(2024, 3, 15, 12, 0, 0, 0, time.UTC),
		TimeZone:      "Asia/Almaty",
		IP:            "***********",
		UserAgent:     "test-user-agent",
		PhoneNumber:   "+***********",
		Tenor:         12,
	}

	// Execute test
	resp, err := client.GetJuicyScore(context.Background(), params)
	require.NoError(t, err)
	require.NotNil(t, resp)

	// Verify response
	assert.Equal(t, mockResponse.AntiFraudScore, resp.AntiFraudScore)
	assert.Equal(t, mockResponse.DeviceID, resp.DeviceID)
	assert.Equal(t, mockResponse.BrowserHash, resp.BrowserHash)
}

func TestJuicyScoreClient_RequestFormation(t *testing.T) {
	client := &JuicyScoreClient{
		Host:      "http://test.com",
		apiKey:    "test-key",
		accountID: "test-account",
	}

	params := ParamsGetJuicyScore{
		ApplicationID: "test-app-id",
		ClientID:      "test-client-id",
		Amount:        100000,
		SessionID:     "test-session-id",
		RequestTime:   time.Date(2024, 3, 15, 12, 0, 0, 0, time.UTC),
		TimeZone:      "Asia/Almaty",
		IP:            "***********",
		UserAgent:     "test-user-agent",
		PhoneNumber:   "+***********",
		Tenor:         12,
	}

	reqBody, err := client.getJuicyScoreRequestBody(params)
	require.NoError(t, err)

	// Verify phone number handling
	assert.Equal(t, "7", reqBody.PhoneCountry)
	assert.Equal(t, "777123", reqBody.Phone)

	// Verify other fields
	assert.Equal(t, params.ApplicationID, reqBody.ApplicationID)
	assert.Equal(t, strconv.Itoa(params.Amount), reqBody.Amount)
	assert.Equal(t, params.SessionID, reqBody.SessionID)
	assert.Equal(t, params.TimeZone, reqBody.TimeZone)
	assert.Equal(t, params.IP, reqBody.IP)
	assert.Equal(t, params.UserAgent, reqBody.UserAgent)
	assert.Equal(t, strconv.Itoa(params.Tenor), reqBody.Tenor)

	// Verify constants
	assert.Equal(t, ChannelPhoneApp, reqBody.Channel)
	assert.Equal(t, Version, reqBody.Version)
	assert.Equal(t, CountryCodeKZ, reqBody.CountryCodeBilling)
	assert.Equal(t, ResponseContentTypeJSON, reqBody.ResponseContentType)

	// Verify client specific fields
	assert.Equal(t, client.accountID, reqBody.AccountID)
}
