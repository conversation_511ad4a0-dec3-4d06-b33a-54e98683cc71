package juicyscore

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strconv"
	"time"

	"git.redmadrobot.com/zaman/backend/zaman/pkg/utils"
)

const (
	GetJuiscyScoreEndpoint  = "/getscore"
	ChannelPhoneApp         = "PHONE_APP"
	Version                 = "16"
	CountryCodeKZ           = "KZ"
	ResponseContentTypeJSON = "json"
)

var _ JuicyScoreProvider = (*JuicyScoreClient)(nil)

// JuicyScoreProvider - определяет методы для работы с сервисом JuicyScore для получения скоринговых данных.
type JuicyScoreProvider interface {
	// GetJuicyScore получает скоринговые данные от сервиса JuicyScore.
	GetJuicyScore(ctx context.Context, params ParamsGetJuicyScore) (*GetJuicyScoreResponse, error)
}

// JuicyScoreClient реализует интерфейс JuicyScore для взаимодействия с API JuicyScore.
type JuicyScoreClient struct {
	// Host содержит базовый URL сервиса JuicyScore.
	Host       string
	apiKey     string
	accountID  string
	httpClient *http.Client
}

// NewJuicyScoreClient создает новый экземпляр клиента JuicyScore.
func NewJuicyScoreClient(cfg *Config) JuicyScoreProvider {
	return &JuicyScoreClient{
		Host:       cfg.Host,
		apiKey:     cfg.APIKey,
		accountID:  cfg.AccountID,
		httpClient: utils.CreateHTTPClientWithUserIDTransport(http.DefaultTransport, cfg.UseMock),
	}
}

// GetJuicyScore отправляет запрос на получение скоринговых данных и возвращает результат.
func (c *JuicyScoreClient) GetJuicyScore(ctx context.Context, params ParamsGetJuicyScore) (*GetJuicyScoreResponse, error) {
	requestBody, err := c.getJuicyScoreRequestBody(params)
	if err != nil {
		return nil, err
	}

	requestBodyJSON, err := json.Marshal(requestBody)
	if err != nil {
		return nil, err
	}

	request, err := http.NewRequestWithContext(ctx, http.MethodPost, fmt.Sprintf("%s%s", c.Host, GetJuiscyScoreEndpoint), bytes.NewBuffer(requestBodyJSON))
	if err != nil {
		return nil, err
	}
	request.Header.Set("Content-Type", "application/json")
	request.Header.Set("session", c.apiKey)

	resp, err := c.httpClient.Do(request)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	var response GetJuicyScoreResponse
	response.HTTPStatusCode = fmt.Sprintf("%d", resp.StatusCode)
	if resp.StatusCode != http.StatusOK {
		if resp.StatusCode >= http.StatusInternalServerError {
			return nil, ErrJuicyScoreInternal
		}
		bodyBytes, _ := io.ReadAll(resp.Body)
		respErr := fmt.Errorf("error status of http response [%d]: %s", resp.StatusCode, bodyBytes)
		response.ErrorMsg = respErr.Error()
		return &response, respErr
	}

	if err = json.NewDecoder(resp.Body).Decode(&response); err != nil {
		return nil, fmt.Errorf("failed to decode resp body: %w", err)
	}

	return &response, nil
}

// getJuicyScoreRequestBody формирует тело запроса для получения скоринговых данных.
func (c *JuicyScoreClient) getJuicyScoreRequestBody(params ParamsGetJuicyScore) (*GetJuicyScoreRequest, error) {
	const timeFormat = "02.01.2006 15:04:05"

	// Используем RequestTime из параметров как базовое время (предполагается, что оно в UTC)
	baseTime := params.RequestTime.UTC()

	// Создаем локацию UTC+3
	utc3Loc := time.FixedZone("UTC+3", 3*60*60)

	// Конвертируем время запроса в UTC+3
	utc3Time := baseTime.In(utc3Loc)

	// Получаем локальное время, используя часовой пояс из параметров
	localLoc, err := time.LoadLocation(params.TimeZone)
	if err != nil {
		// Используем UTC+6 (Алматы) как запасной вариант, если загрузка часового пояса не удалась
		localLoc = time.FixedZone("UTC+6", 6*60*60)
	}
	localTime := baseTime.In(localLoc)

	requestBody := &GetJuicyScoreRequest{
		ApplicationID: params.ApplicationID,
		ClientID:      params.ClientID,
		Amount:        strconv.Itoa(params.Amount),
		SessionID:     params.SessionID,
		TimeZone:      params.TimeZone,
		TimeUTC3:      utc3Time.Format(timeFormat),
		TimeLocal:     localTime.Format(timeFormat),
		IP:            params.IP,
		UserAgent:     params.UserAgent,
		Tenor:         strconv.Itoa(params.Tenor),
	}

	countryCode, err := getCountryCode(params.PhoneNumber)
	if err != nil {
		return nil, err
	}

	firstSixDigits, err := getFirstSixDigits(params.PhoneNumber)
	if err != nil {
		return nil, err
	}

	requestBody.PhoneCountry = countryCode
	requestBody.Phone = firstSixDigits

	requestBody.AccountID = c.accountID

	requestBody.Channel = ChannelPhoneApp
	requestBody.Version = Version

	requestBody.CountryCodeBilling = CountryCodeKZ
	requestBody.ResponseContentType = ResponseContentTypeJSON

	return requestBody, nil
}
