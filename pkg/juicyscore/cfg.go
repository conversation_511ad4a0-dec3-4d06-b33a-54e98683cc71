package juicyscore

import (
	"git.redmadrobot.com/backend-go/rmr-pkg/core/cfg"
	"git.redmadrobot.com/backend-go/rmr-pkg/core/cfg/viperx"
	"github.com/spf13/viper"
)

const (
	CfgJuicyHost      cfg.Key = "JUICY_SCORE_HOST"
	CfgJuicyAPIKey    cfg.Key = "JUICY_SCORE_API_KEY" //nolint:gosec
	CfgJuicyAccountID cfg.Key = "JUICY_SCORE_ACCOUNT_ID"
	CfgJuicyUseMock   cfg.Key = "JUICY_SCORE_USE_MOCK"

	defaultJuicyHost      = "https://api.juicyscore.com"
	defaultJuicyAPIKey    = ""
	defaultJuicyAccountID = ""
	defaultJuicyUseMock   = false
)

type Config struct {
	// Host - адрес сервиса JuicyScore
	Host string
	// APIKey - ключ API для сервиса JuicyScore
	APIKey string
	// AccountID - идентификатор аккаунта для сервиса JuicyScore
	AccountID string
	// UseMock - использовать моковый сервер JuicyScore
	UseMock bool
}

func CfgFromViper(loader *viper.Viper, keyMapping ...cfg.KeyMap) *Config {
	loader.SetDefault(CfgJuicyHost.String(), defaultJuicyHost)
	loader.SetDefault(CfgJuicyAPIKey.String(), defaultJuicyAPIKey)
	loader.SetDefault(CfgJuicyAccountID.String(), defaultJuicyAccountID)
	loader.SetDefault(CfgJuicyUseMock.String(), defaultJuicyUseMock)

	return &Config{
		Host:      viperx.Get(loader, CfgJuicyHost.Map(keyMapping...), defaultJuicyHost),
		APIKey:    viperx.Get(loader, CfgJuicyAPIKey.Map(keyMapping...), defaultJuicyAPIKey),
		AccountID: viperx.Get(loader, CfgJuicyAccountID.Map(keyMapping...), defaultJuicyAccountID),
		UseMock:   viperx.Get(loader, CfgJuicyUseMock.Map(keyMapping...), defaultJuicyUseMock),
	}
}
