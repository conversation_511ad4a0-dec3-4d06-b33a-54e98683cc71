package seon

import (
	"git.redmadrobot.com/backend-go/rmr-pkg/core/cfg"
	"git.redmadrobot.com/backend-go/rmr-pkg/core/cfg/viperx"
	"github.com/spf13/viper"
)

const (
	CfgSeonBaseURL cfg.Key = "SEON_BASE_URL"
	CfgSeonAPIKey  cfg.Key = "SEON_API_KEY" //nolint:gosec
	CfgSeonUseMock cfg.Key = "SEON_USE_MOCK"

	defaultSeonBaseURL = "https://api.seon.io"
	defaultSeonAPIKey  = ""
	defaultSeonUseMock = false
)

type Config struct {
	// BaseURL базовый URL для seon
	BaseURL string
	// APIKey ключ для seon
	APIKey string
	// UseMock использовать ли моке
	UseMock bool
}

func CfgFromViper(loader *viper.Viper, keyMapping ...cfg.KeyMap) *Config {
	loader.SetDefault(CfgSeonBaseURL.String(), defaultSeonBaseURL)
	loader.SetDefault(CfgSeonAPIKey.String(), defaultSeonAPIKey)
	loader.SetDefault(CfgSeonUseMock.String(), false)

	return &Config{
		BaseURL: viperx.Get(loader, CfgSeonBaseURL.Map(keyMapping...), defaultSeonBaseURL),
		APIKey:  viperx.Get(loader, CfgSeonAPIKey.Map(keyMapping...), defaultSeonAPIKey),
		UseMock: viperx.Get(loader, CfgSeonUseMock.Map(keyMapping...), false),
	}
}
