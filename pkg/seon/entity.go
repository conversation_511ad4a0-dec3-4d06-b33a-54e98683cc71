package seon

import (
	"fmt"
	"strings"
)

type (
	FraudAPIResponse struct {
		Success      bool  `json:"success"`
		<PERSON>rror        <PERSON>r `json:"error"`
		Data         Data  `json:"data"`
		RequestLogID int64
	}

	Data struct {
		ID              string         `json:"id,omitempty"`
		State           string         `json:"state,omitempty"`
		FraudScore      float64        `json:"fraud_score,omitempty"`
		IPDetails       *IPDetails     `json:"ip_details,omitempty"`
		EmailDetails    *EmailDetails  `json:"email_details,omitempty"`
		BinDetails      *BinDetails    `json:"bin_details,omitempty"`
		PhoneDetails    *PhoneDetails  `json:"phone_details,omitempty"`
		Version         string         `json:"version,omitempty"`
		AppliedRules    []*AppliedRule `json:"applied_rules,omitempty"`
		DeviceDetails   *DeviceDetails `json:"device_details,omitempty"`
		CalculationTime int64          `json:"calculation_time,omitempty"`
		SeonID          int64          `json:"seon_id,omitempty"`
	}

	IPDetails struct {
		IP             string         `json:"ip,omitempty"`
		Score          float64        `json:"score,omitempty"`
		Country        string         `json:"country,omitempty"`
		StateProv      string         `json:"state_prov,omitempty"`
		City           string         `json:"city,omitempty"`
		TimezoneOffset string         `json:"timezone_offset,omitempty"`
		IspName        string         `json:"isp_name,omitempty"`
		Latitude       float64        `json:"latitude,omitempty"`
		Longitude      float64        `json:"longitude,omitempty"`
		Type           string         `json:"type,omitempty"`
		OpenPorts      []string       `json:"open_ports,omitempty"`
		Tor            bool           `json:"tor,omitempty"`
		Harmful        bool           `json:"harmful,omitempty"`
		Vpn            bool           `json:"vpn,omitempty"`
		WebProxy       bool           `json:"web_proxy,omitempty"`
		PublicProxy    bool           `json:"public_proxy,omitempty"`
		SpamNumber     int64          `json:"spam_number,omitempty"`
		SpamURLs       []string       `json:"spam_urls,omitempty"`
		AppliedRules   []*AppliedRule `json:"applied_rules,omitempty"`
		History        *History       `json:"history,omitempty"`
		Flags          []*Flag        `json:"flags,omitempty"`
		ID             string         `json:"id,omitempty"`
	}

	History struct {
		Hints       int64 `json:"hints,omitempty"`
		CustomHints int64 `json:"custom_hints,omitempty"`
		FirstSeen   int64 `json:"first_seen,omitempty"`
		LastSeen    int64 `json:"last_seen,omitempty"`
	}

	Flag struct {
		Note     string `json:"note,omitempty"`
		Date     int64  `json:"date,omitempty"`
		Industry string `json:"industry,omitempty"`
	}

	BinDetails struct {
		CardBin        string `json:"card_bin,omitempty"`
		BinBank        string `json:"bin_bank,omitempty"`
		BinCard        string `json:"bin_card,omitempty"`
		BinType        string `json:"bin_type,omitempty"`
		BinLevel       string `json:"bin_level,omitempty"`
		BinCountry     string `json:"bin_country,omitempty"`
		BinCountryCode string `json:"bin_country_code,omitempty"`
		BinWebsite     string `json:"bin_website,omitempty"`
		BinPhone       string `json:"bin_phone,omitempty"`
		BinValid       bool   `json:"bin_valid,omitempty"`
		CardIssuer     string `json:"card_issuer,omitempty"`
	}

	DeviceDetails struct {
		Type                      *string  `json:"type,omitempty"`
		Source                    *string  `json:"source,omitempty"`
		SessionID                 *string  `json:"session_id,omitempty"`
		Adblock                   *bool    `json:"adblock,omitempty"`
		AudioHash                 *string  `json:"audio_hash,omitempty"`
		BatteryCharging           *bool    `json:"battery_charging,omitempty"`
		BatteryLevel              *int32   `json:"battery_level,omitempty"`
		BrowserHash               *string  `json:"browser_hash,omitempty"`
		Browser                   *string  `json:"browser,omitempty"`
		BrowserVersion            *string  `json:"browser_version,omitempty"`
		CanvasHash                *string  `json:"canvas_hash,omitempty"`
		CookieEnabled             *string  `json:"cookie_enabled,omitempty"`
		CookieHash                *string  `json:"cookie_hash,omitempty"`
		DeviceHash                *string  `json:"device_hash,omitempty"`
		DeviceMemory              *int32   `json:"device_memory,omitempty"`
		DeviceType                *string  `json:"device_type,omitempty"`
		DNSIP                     *string  `json:"dns_ip,omitempty"`
		DNSIPCountry              *string  `json:"dns_ip_country,omitempty"`
		DNSIPIsp                  *string  `json:"dns_ip_isp,omitempty"`
		DoNotTrack                *bool    `json:"do_not_track,omitempty"`
		FlashEnabled              *bool    `json:"flash_enabled,omitempty"`
		FontCount                 *int32   `json:"font_count,omitempty"`
		FontHash                  *string  `json:"font_hash,omitempty"`
		FontList                  []string `json:"font_list,omitempty"`
		HardwareConcurrency       *int32   `json:"hardware_concurrency,omitempty"`
		JavaEnabled               *bool    `json:"java_enabled,omitempty"`
		DeviceIPAddress           *string  `json:"device_ip_address,omitempty"`
		DeviceIPCountry           *string  `json:"device_ip_country,omitempty"`
		DeviceIPIsp               *string  `json:"device_ip_isp,omitempty"`
		AcceptLanguage            []string `json:"accept_language,omitempty"`
		Os                        *string  `json:"os,omitempty"`
		Platform                  *string  `json:"platform,omitempty"`
		PluginCount               *int32   `json:"plugin_count,omitempty"`
		PluginHash                *string  `json:"plugin_hash,omitempty"`
		PluginList                []string `json:"plugin_list,omitempty"`
		Private                   *bool    `json:"private,omitempty"`
		RegionLanguage            *string  `json:"region_language,omitempty"`
		RegionTimezone            *string  `json:"region_timezone,omitempty"`
		ScreenAvailableResolution *string  `json:"screen_available_resolution,omitempty"`
		ScreenColorDepth          *int32   `json:"screen_color_depth,omitempty"`
		ScreenPixelRatio          *int32   `json:"screen_pixel_ratio,omitempty"`
		ScreenResolution          *string  `json:"screen_resolution,omitempty"`
		TouchSupport              *bool    `json:"touch_support,omitempty"`
		UserAgent                 *string  `json:"user_agent,omitempty"`
		WebglHash                 *string  `json:"webgl_hash,omitempty"`
		WebglVendor               *string  `json:"webgl_vendor,omitempty"`
		WebrtcActivated           *bool    `json:"webrtc_activated,omitempty"`
		WebrtcCount               *int32   `json:"webrtc_count,omitempty"`
		WebrtcIps                 []string `json:"webrtc_ips,omitempty"`
		WindowSize                *string  `json:"window_size,omitempty"`
	}

	AppliedRule struct {
		ID        string  `json:"id"`
		Name      string  `json:"name"`
		Operation string  `json:"operation"`
		Score     float64 `json:"score"`
	}

	EmailDetails struct {
		Email          string         `json:"email,omitempty"`
		Score          float64        `json:"score,omitempty"`
		Deliverable    bool           `json:"deliverable,omitempty"`
		DomainDetails  *DomainDetails `json:"domain_details,omitempty"`
		AccountDetails *EmailAccounts `json:"account_details,omitempty"`
		BreachDetails  *BreachDetails `json:"breach_details,omitempty"`
		AppliedRules   []*AppliedRule `json:"applied_rules,omitempty"`
		History        *History       `json:"history,omitempty"`
		Flags          []*Flag        `json:"flags,omitempty"`
		ID             *string        `json:"id,omitempty"`
	}

	EmailAccounts struct {
		Apple        *CommonEmailAccountDetails `json:"apple,omitempty"`
		Ebay         *CommonEmailAccountDetails `json:"ebay,omitempty"`
		Facebook     *Facebook                  `json:"facebook,omitempty"`
		Flickr       *Flickr                    `json:"flickr,omitempty"`
		Foursquare   *Foursquare                `json:"foursquare,omitempty"`
		Github       *Github                    `json:"github,omitempty"`
		Google       *Google                    `json:"google,omitempty"`
		Gravatar     *Gravatar                  `json:"gravatar,omitempty"`
		Instagram    *CommonEmailAccountDetails `json:"instagram,omitempty"`
		Lastfm       *CommonEmailAccountDetails `json:"lastfm,omitempty"`
		Linkedin     *Linkedin                  `json:"linkedin,omitempty"`
		Microsoft    *CommonEmailAccountDetails `json:"microsoft,omitempty"`
		Myspace      *CommonEmailAccountDetails `json:"myspace,omitempty"`
		Pinterest    *CommonEmailAccountDetails `json:"pinterest,omitempty"`
		Skype        *Skype                     `json:"skype,omitempty"`
		Spotify      *CommonEmailAccountDetails `json:"spotify,omitempty"`
		Tumblr       *CommonEmailAccountDetails `json:"tumblr,omitempty"`
		Twitter      *CommonEmailAccountDetails `json:"twitter,omitempty"`
		Vimeo        *CommonEmailAccountDetails `json:"vimeo,omitempty"`
		Weibo        *CommonEmailAccountDetails `json:"weibo,omitempty"`
		Yahoo        *CommonEmailAccountDetails `json:"yahoo,omitempty"`
		Discord      *CommonEmailAccountDetails `json:"discord,omitempty"`
		Ok           *Ok                        `json:"ok,omitempty"`
		Kakao        *CommonEmailAccountDetails `json:"kakao,omitempty"`
		Booking      *CommonEmailAccountDetails `json:"booking,omitempty"`
		Airbnb       *Airbnb                    `json:"airbnb,omitempty"`
		Amazon       *CommonEmailAccountDetails `json:"amazon,omitempty"`
		Qzone        *CommonEmailAccountDetails `json:"qzone,omitempty"`
		Adobe        *CommonEmailAccountDetails `json:"adobe,omitempty"`
		Mailru       *CommonEmailAccountDetails `json:"mailru,omitempty"`
		Wordpress    *CommonEmailAccountDetails `json:"wordpress,omitempty"`
		Imgur        *CommonEmailAccountDetails `json:"imgur,omitempty"`
		Disneyplus   *CommonEmailAccountDetails `json:"disneyplus,omitempty"`
		Netflix      *CommonEmailAccountDetails `json:"netflix,omitempty"`
		Jdid         *CommonEmailAccountDetails `json:"jdid,omitempty"`
		Flipkart     *CommonEmailAccountDetails `json:"flipkart,omitempty"`
		Bukalapak    *CommonEmailAccountDetails `json:"bukalapak,omitempty"`
		Archiveorg   *CommonEmailAccountDetails `json:"archiveorg,omitempty"`
		Lazada       *CommonEmailAccountDetails `json:"lazada,omitempty"`
		Zoho         *CommonEmailAccountDetails `json:"zoho,omitempty"`
		Samsung      *CommonEmailAccountDetails `json:"samsung,omitempty"`
		Evernote     *CommonEmailAccountDetails `json:"evernote,omitempty"`
		Envato       *CommonEmailAccountDetails `json:"envato,omitempty"`
		Patreon      *CommonEmailAccountDetails `json:"patreon,omitempty"`
		Tokopedia    *CommonEmailAccountDetails `json:"tokopedia,omitempty"`
		Rambler      *CommonEmailAccountDetails `json:"rambler,omitempty"`
		Quora        *CommonEmailAccountDetails `json:"quora,omitempty"`
		Atlassian    *CommonEmailAccountDetails `json:"atlassian,omitempty"`
		Aboutme      *CommonEmailAccountDetails `json:"aboutme,omitempty"`
		Altbalaji    *CommonEmailAccountDetails `json:"altbalaji,omitempty"`
		Bitmoji      *CommonEmailAccountDetails `json:"bitmoji,omitempty"`
		Bodybuilding *CommonEmailAccountDetails `json:"bodybuilding,omitempty"`
		Codecademy   *CommonEmailAccountDetails `json:"codecademy,omitempty"`
		Deliveroo    *CommonEmailAccountDetails `json:"deliveroo,omitempty"`
		Diigo        *CommonEmailAccountDetails `json:"diigo,omitempty"`
		Duolingo     *Duolingo                  `json:"duolingo,omitempty"`
		Eventbrite   *CommonEmailAccountDetails `json:"eventbrite,omitempty"`
		Firefox      *CommonEmailAccountDetails `json:"firefox,omitempty"`
		Freelancer   *CommonEmailAccountDetails `json:"freelancer,omitempty"`
		Gaana        *CommonEmailAccountDetails `json:"gaana,omitempty"`
		Giphy        *CommonEmailAccountDetails `json:"giphy,omitempty"`
		Hubspot      *CommonEmailAccountDetails `json:"hubspot,omitempty"`
		Kommo        *CommonEmailAccountDetails `json:"kommo,omitempty"`
		Komoot       *CommonEmailAccountDetails `json:"komoot,omitempty"`
		Nike         *CommonEmailAccountDetails `json:"nike,omitempty"`
		Plurk        *CommonEmailAccountDetails `json:"plurk,omitempty"`
		Rappi        *CommonEmailAccountDetails `json:"rappi,omitempty"`
		Replit       *CommonEmailAccountDetails `json:"replit,omitempty"`
		Seoclerks    *CommonEmailAccountDetails `json:"seoclerks,omitempty"`
		Snapchat     *CommonEmailAccountDetails `json:"snapchat,omitempty"`
		Snapdeal     *CommonEmailAccountDetails `json:"snapdeal,omitempty"`
		Soundcloud   *CommonEmailAccountDetails `json:"soundcloud,omitempty"`
		Starz        *CommonEmailAccountDetails `json:"starz,omitempty"`
		Strava       *CommonEmailAccountDetails `json:"strava,omitempty"`
		Taringa      *Taringa                   `json:"taringa,omitempty"`
		Tiki         *CommonEmailAccountDetails `json:"tiki,omitempty"`
		Treehouse    *CommonEmailAccountDetails `json:"treehouse,omitempty"`
		Venmo        *CommonEmailAccountDetails `json:"venmo,omitempty"`
		Vivino       *CommonEmailAccountDetails `json:"vivino,omitempty"`
		Vkontakte    *CommonEmailAccountDetails `json:"vkontakte,omitempty"`
		Wattpad      *CommonEmailAccountDetails `json:"wattpad,omitempty"`
		Xing         *CommonEmailAccountDetails `json:"xing,omitempty"`
		Yandex       *CommonEmailAccountDetails `json:"yandex,omitempty"`
		AdultSites   *CommonEmailAccountDetails `json:"adult_sites,omitempty"`
	}

	CommonEmailAccountDetails struct {
		Registered bool `json:"registered,omitempty"`
	}

	Facebook struct {
		Registered bool    `json:"registered,omitempty"`
		Name       *string `json:"name,omitempty"`
		Photo      *string `json:"photo,omitempty"`
		URL        *string `json:"url,omitempty"`
	}

	Flickr struct {
		Registered  bool    `json:"registered,omitempty"`
		Description *string `json:"description,omitempty"`
		Followers   *int32  `json:"followers,omitempty"`
		Location    *string `json:"location,omitempty"`
		Occupation  *string `json:"occupation,omitempty"`
		Photo       *string `json:"photo,omitempty"`
		Username    *string `json:"username,omitempty"`
	}

	Foursquare struct {
		Registered bool    `json:"registered,omitempty"`
		Bio        *string `json:"bio,omitempty"`
		Photo      *string `json:"photo,omitempty"`
		ProfileURL *string `json:"profile_url,omitempty"`
	}

	Github struct {
		Registered bool    `json:"registered,omitempty"`
		Photo      *string `json:"photo,omitempty"`
		FullName   *string `json:"full_name,omitempty"`
		Username   *string `json:"username,omitempty"`
		Location   *string `json:"location,omitempty"`
		Company    *string `json:"company,omitempty"`
		Website    *string `json:"website,omitempty"`
		Bio        *string `json:"bio,omitempty"`
		Followers  *int32  `json:"followers,omitempty"`
		Following  *int32  `json:"following,omitempty"`
		Twitter    *string `json:"twitter,omitempty"`
		ProfileURL *string `json:"profile_url,omitempty"`
	}

	Google struct {
		Registered       bool            `json:"registered,omitempty"`
		Photo            *string         `json:"photo,omitempty"`
		Activity         *GoogleActivity `json:"activity,omitempty"`
		IsEnterpriseUser *bool           `json:"is_enterprise_user,omitempty"`
		LastUpdated      *int64          `json:"last_updated,omitempty"`
	}

	GoogleActivity struct {
		Answers        *int32 `json:"answers,omitempty"`
		Edits          *int32 `json:"edits,omitempty"`
		FactsChecked   *int32 `json:"facts_checked,omitempty"`
		Photos         *int32 `json:"photos,omitempty"`
		PlacesAdded    *int32 `json:"places_added,omitempty"`
		PublishedLists *int32 `json:"published_lists,omitempty"`
		QAndA          *int32 `json:"q_and_a,omitempty"`
		Ratings        *int32 `json:"ratings,omitempty"`
		Reviews        *int32 `json:"reviews,omitempty"`
		RoadsAdded     *int32 `json:"roads_added,omitempty"`
		Videos         *int32 `json:"videos,omitempty"`
	}

	Gravatar struct {
		Registered bool    `json:"registered,omitempty"`
		Location   *string `json:"location,omitempty"`
		Name       *string `json:"name,omitempty"`
		ProfileURL *string `json:"profile_url,omitempty"`
		Username   *string `json:"username,omitempty"`
	}

	Linkedin struct {
		Registered      bool    `json:"registered,omitempty"`
		URL             *string `json:"url,omitempty"`
		Name            *string `json:"name,omitempty"`
		Company         *string `json:"company,omitempty"`
		Title           *string `json:"title,omitempty"`
		Location        *string `json:"location,omitempty"`
		Website         *string `json:"website,omitempty"`
		Twitter         *string `json:"twitter,omitempty"`
		Photo           *string `json:"photo,omitempty"`
		ConnectionCount *int32  `json:"connection_count,omitempty"`
	}

	Skype struct {
		Registered  bool    `json:"registered,omitempty"`
		Country     *string `json:"country,omitempty"`
		CountryCode *string `json:"country_code,omitempty"`
		Gender      *int32  `json:"gender,omitempty"`
		Name        *string `json:"name,omitempty"`
		ID          *string `json:"id,omitempty"`
		Handle      *string `json:"handle,omitempty"`
		Bio         *string `json:"bio,omitempty"`
		Age         *int32  `json:"age,omitempty"`
		Language    *string `json:"language,omitempty"`
		State       *string `json:"state,omitempty"`
		Photo       *string `json:"photo,omitempty"`
		ContactType *string `json:"contact_type,omitempty"`
		City        *string `json:"city,omitempty"`
	}

	Ok struct {
		Registered bool    `json:"registered,omitempty"`
		City       *string `json:"city,omitempty"`
		Age        *int32  `json:"age,omitempty"`
		DateJoined *int64  `json:"date_joined,omitempty"`
		FullName   *string `json:"full_name,omitempty"`
	}

	Airbnb struct {
		Registered       bool    `json:"registered,omitempty"`
		About            *string `json:"about,omitempty"`
		CreatedAt        *string `json:"created_at,omitempty"`
		FirstName        *string `json:"first_name,omitempty"`
		IdentityVerified *string `json:"identity_verified,omitempty"`
		Location         *string `json:"location,omitempty"`
		Image            *string `json:"image,omitempty"`
		RevieweeCount    *string `json:"reviewee_count,omitempty"`
		Trips            *int32  `json:"trips,omitempty"`
		Work             *string `json:"work,omitempty"`
	}

	Duolingo struct {
		Registered        bool     `json:"registered,omitempty"`
		FullName          *string  `json:"full_name,omitempty"`
		Photo             *string  `json:"photo,omitempty"`
		RegisteredAt      *string  `json:"registered_at,omitempty"`
		Country           *string  `json:"country,omitempty"`
		Username          *string  `json:"username,omitempty"`
		HasRecentActivity *bool    `json:"has_recent_activity,omitempty"`
		LearningLanguage  *string  `json:"learning_language,omitempty"`
		FromLanguage      *string  `json:"from_language,omitempty"`
		EmailVerified     *bool    `json:"email_verified,omitempty"`
		Courses           []string `json:"courses,omitempty"`
	}

	Taringa struct {
		Registered bool    `json:"registered,omitempty"`
		Username   *string `json:"username,omitempty"`
		Photo      *string `json:"photo,omitempty"`
	}

	BreachDetails struct {
		Breaches             []*Breach `json:"breaches,omitempty"`
		HaveibeenpwnedListed *bool     `json:"haveibeenpwned_listed,omitempty"`
		NumberOfBreaches     *int32    `json:"number_of_breaches,omitempty"`
		FirstBreach          *string   `json:"first_breach,omitempty"`
	}

	Breach struct {
		Name   *string `json:"name,omitempty"`
		Domain *string `json:"domain,omitempty"`
		Date   *string `json:"date,omitempty"`
	}

	DomainDetails struct {
		Domain        *string `protobuf:"bytes,1,opt,name=domain,proto3,oneof" json:"domain,omitempty"`
		Tld           *string `protobuf:"bytes,2,opt,name=tld,proto3,oneof" json:"tld,omitempty"`
		Created       *string `protobuf:"bytes,3,opt,name=created,proto3" json:"created,omitempty"`
		Updated       *string `protobuf:"bytes,4,opt,name=updated,proto3" json:"updated,omitempty"`
		Expires       *string `protobuf:"bytes,5,opt,name=expires,proto3" json:"expires,omitempty"`
		Registered    *bool   `protobuf:"varint,6,opt,name=registered,proto3,oneof" json:"registered,omitempty"`
		RegistrarName *string `protobuf:"bytes,7,opt,name=registrar_name,json=registrarName,proto3,oneof" json:"registrar_name,omitempty"`
		RegisteredTo  *string `protobuf:"bytes,8,opt,name=registered_to,json=registeredTo,proto3,oneof" json:"registered_to,omitempty"`
		Disposable    *bool   `protobuf:"varint,9,opt,name=disposable,proto3,oneof" json:"disposable,omitempty"`
		Free          *bool   `protobuf:"varint,10,opt,name=free,proto3,oneof" json:"free,omitempty"`
		Custom        *bool   `protobuf:"varint,11,opt,name=custom,proto3,oneof" json:"custom,omitempty"`
		DmarcEnforced *bool   `protobuf:"varint,12,opt,name=dmarc_enforced,json=dmarcEnforced,proto3,oneof" json:"dmarc_enforced,omitempty"`
		SpfStrict     *bool   `protobuf:"varint,13,opt,name=spf_strict,json=spfStrict,proto3,oneof" json:"spf_strict,omitempty"`
		ValidMx       *bool   `protobuf:"varint,14,opt,name=valid_mx,json=validMx,proto3,oneof" json:"valid_mx,omitempty"`
		AcceptAll     *bool   `protobuf:"varint,15,opt,name=accept_all,json=acceptAll,proto3,oneof" json:"accept_all,omitempty"`
		SuspiciousTld *bool   `protobuf:"varint,16,opt,name=suspicious_tld,json=suspiciousTld,proto3,oneof" json:"suspicious_tld,omitempty"`
		WebsiteExists *bool   `protobuf:"varint,17,opt,name=website_exists,json=websiteExists,proto3,oneof" json:"website_exists,omitempty"`
	}

	PhoneDetails struct {
		Number         int64          `json:"number,omitempty"`
		Valid          bool           `json:"valid,omitempty"`
		Disposable     bool           `json:"disposable,omitempty"`
		Type           string         `json:"type,omitempty"`
		Country        string         `json:"country,omitempty"`
		Carrier        string         `json:"carrier,omitempty"`
		Score          float64        `json:"score,omitempty"`
		AccountDetails *PhoneAccounts `json:"account_details,omitempty"`
		AppliedRules   []*AppliedRule `json:"applied_rules,omitempty"`
		History        *History       `json:"history,omitempty"`
		Flags          []*Flag        `json:"flags,omitempty"`
		ID             string         `json:"id,omitempty"`
	}

	PhoneAccounts struct {
		Facebook  *PhoneAccountDetails  `json:"facebook,omitempty"`
		Google    *GooglePhoneAccount   `json:"google,omitempty"`
		Twitter   *PhoneAccountDetails  `json:"twitter,omitempty"`
		Instagram *PhoneAccountDetails  `json:"instagram,omitempty"`
		Yahoo     *PhoneAccountDetails  `json:"yahoo,omitempty"`
		Microsoft *PhoneAccountDetails  `json:"microsoft,omitempty"`
		Snapchat  *PhoneAccountDetails  `json:"snapchat,omitempty"`
		Skype     *SkypePhoneAccount    `json:"skype,omitempty"`
		Whatsapp  *WhatsappPhoneAccount `json:"whatsapp,omitempty"`
		Telegram  *Telegram             `json:"telegram,omitempty"`
		Viber     *Viber                `json:"viber,omitempty"`
		Kakao     *PhoneAccountDetails  `json:"kakao,omitempty"`
		Ok        *OkPhoneAccount       `json:"ok,omitempty"`
		Zalo      *Zalo                 `json:"zalo,omitempty"`
		Line      *Line                 `json:"line,omitempty"`
		Flipkart  *PhoneAccountDetails  `json:"flipkart,omitempty"`
		Bukalapak *PhoneAccountDetails  `json:"bukalapak,omitempty"`
		Jdid      *PhoneAccountDetails  `json:"jdid,omitempty"`
		Altbalaji *PhoneAccountDetails  `json:"altbalaji,omitempty"`
		Shopclues *PhoneAccountDetails  `json:"shopclues,omitempty"`
		Snapdeal  *PhoneAccountDetails  `json:"snapdeal,omitempty"`
		Tiki      *PhoneAccountDetails  `json:"tiki,omitempty"`
		Vkontakte *PhoneAccountDetails  `json:"vkontakte,omitempty"`
		Weibo     *PhoneAccountDetails  `json:"weibo,omitempty"`
	}

	PhoneAccountDetails struct {
		Registered bool `json:"registered,omitempty"`
	}

	GooglePhoneAccount struct {
		Registered bool    `json:"registered,omitempty"`
		AccountID  *string `json:"account_id,omitempty"`
		FullName   *string `json:"full_name,omitempty"`
	}

	SkypePhoneAccount struct {
		Registered bool    `json:"registered,omitempty"`
		Photo      *string `json:"photo,omitempty"`
		Age        *int32  `json:"age,omitempty"`
		Bio        *string `json:"bio,omitempty"`
		City       *string `json:"city,omitempty"`
		Country    *string `json:"country,omitempty"`
		Gender     *int32  `json:"gender,omitempty"`
		Handle     *string `json:"handle,omitempty"`
		ID         *string `json:"id,omitempty"`
		Language   *string `json:"language,omitempty"`
		Name       *string `json:"name,omitempty"`
		State      *string `json:"state,omitempty"`
	}

	WhatsappPhoneAccount struct {
		Registered bool    `json:"registered,omitempty"`
		About      *string `json:"about,omitempty"`
		LastActive *int64  `json:"last_active,omitempty"`
		LastSeen   *int64  `json:"last_seen,omitempty"`
		Photo      *string `json:"photo,omitempty"`
	}

	Telegram struct {
		Registered bool    `json:"registered,omitempty"`
		LastSeen   *int64  `json:"last_seen,omitempty"`
		Photo      *string `json:"photo,omitempty"`
	}

	Viber struct {
		Registered bool    `json:"registered,omitempty"`
		Name       *string `json:"name,omitempty"`
		LastSeen   *int64  `json:"last_seen,omitempty"`
		Photo      *string `json:"photo,omitempty"`
	}

	OkPhoneAccount struct {
		Registered bool   `json:"registered,omitempty"`
		Age        *int32 `json:"age,omitempty"`
	}

	Zalo struct {
		Registered  bool    `json:"registered,omitempty"`
		DateOfBirth *string `json:"date_of_birth,omitempty"`
		Name        *string `json:"name,omitempty"`
		UID         *string `json:"uid,omitempty"`
	}

	Line struct {
		Registered bool    `json:"registered,omitempty"`
		Name       *string `json:"name,omitempty"`
		Photo      *string `json:"photo,omitempty"`
	}

	Error struct {
		Code    string `json:"code"`
		Message string `json:"message"`
	}
)

// FraudAPIReq представляет структуру тела запроса к API для проверки на мошенничество.
type FraudAPIReq struct {
	Config      FraudAPIReqConfig `json:"config"`       // Конфигурация для проверки на мошенничество
	Email       string            `json:"email"`        // Адрес электронной почты пользователя
	PhoneNumber string            `json:"phone_number"` // Номер телефона пользователя
	UserCountry string            `json:"user_country"` // Код страны пользователя (например, "KZ")
}

// FraudAPIReqConfig содержит вложенные настройки конфигурации для проверки на мошенничество.
type FraudAPIReqConfig struct {
	Email               ReqConfigDetail `json:"email"`     // Конфигурация, специфичная для электронной почты
	Phone               ReqConfigDetail `json:"phone"`     // Конфигурация, специфичная для телефона
	EmailAPI            bool            `json:"email_api"` // Флаг для включения проверки через email API (в виде строки "true"/"false")
	PhoneAPI            bool            `json:"phone_api"` // Флаг для включения проверки через phone API (в виде строки "true"/"false")
	DeviceFingerprint   bool            `json:"device_fingerprint"`
	IgnoreVelocityRules bool            `json:"ignore_velocity_rules"`
	ResponseFields      string          `json:"response_fields"`
}

// ReqConfigDetail содержит детали конфигурации для проверок email или телефона.
type ReqConfigDetail struct {
	Version string `json:"version"` // Используемая версия API, например, "v2", "v1"
	TimeOut int    `json:"timeout"`
}

type ParamsGetFraudData struct {
	Email       string `json:"email"`        // Адрес электронной почты пользователя
	Phone       string `json:"phone"`        // Номер телефона пользователя
	UserCountry string `json:"user_country"` // Код страны пользователя (например, "KZ")
	ExternalID  *string
}

// Validate проверяет корректность данных запроса
func (r *FraudAPIReq) Validate() error {
	if r.Email != "" {
		if !strings.Contains(r.Email, "@") || !strings.Contains(r.Email, ".") {
			return fmt.Errorf("некорректный формат email")
		}
	}

	if r.PhoneNumber == "" {
		return fmt.Errorf("номер телефона не может быть пустым")
	}

	if r.UserCountry != "" && len(r.UserCountry) != 2 {
		return fmt.Errorf("код страны должен состоять из 2 символов")
	}

	return nil
}
