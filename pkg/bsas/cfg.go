package bsas

const (
	createOrderEndpoint    = "/api/process/svc/bsas/order.json"
	bidXMLEndpoint         = "/api/process/svc/bsas/bidXML.json"
	otcXMLEndpoint         = "/api/process/svc/bsas/otcXML.json"
	getOrderResultEndpoint = "/api/process/svc/bsas/orderResult.json"
	getSellToBMISEndpoint  = "/api/process/svc/bsas/stbXML.json"

	createOrderMethodName          = "CreateOrder"
	getBSASOrderResultMethodName   = "GetBSASOrderResult"
	getBSASOtcReportInfoMethodName = "GetBSASOtcReportInfo"
	getBSASSystemMappingMethodName = "GetSystemMapping"
	getSellToBMISMethodName        = "GetSellToBMIS"
)
