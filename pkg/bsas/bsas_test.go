package bsas

import (
	"context"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"git.redmadrobot.com/zaman/backend/zaman/pkg/bsas/entity"
)

const tokenType = "Bearer"
const accessToken = "awdawiceicmor"
const expiresIn = 15200
const message = "message"

type mockServerCase struct {
	StatusCode  int
	Resp        map[string]any
	URI         string
	Method      string
	ContentType string
	ReqString   *string
	ReqBody     map[string]any
	RespBytes   []byte
}

func newMockBSASProviderAuthServer(t *testing.T, m mockServerCase) *httptest.Server {
	handler := http.NewServeMux()
	handler.HandleFunc(
		m.URI, func(w http.ResponseWriter, r *http.Request) {
			assert.Equal(t, m.Method, r.Method)
			w.WriteHeader(m.StatusCode)
			if m.Resp != nil {
				err := json.NewEncoder(w).Encode(m.Resp)
				require.NoError(t, err)
			}
		},
	)
	mockServer := httptest.NewServer(handler)
	return mockServer
}

func getTestBSASClient(url string, client *http.Client) *BsasProviderImpl {
	return &BsasProviderImpl{
		HTTPClient:   client,
		BaseURL:      url,
		ClientID:     "ClientId",
		ClientSecret: "ClientSecret",
		Tokens: &entity.Token{
			TokenType:   tokenType,
			AccessToken: accessToken,
			ExpiresIn:   expiresIn,
			Message:     message,
		},
	}
}

func TestBSASProvider_Auth_Success(t *testing.T) {
	m := mockServerCase{
		StatusCode: http.StatusOK,
		Resp: map[string]any{
			"token_type":   tokenType,
			"access_token": accessToken,
			"expires_in":   expiresIn,
			"message":      message,
		},
		URI:    authEndpoint,
		Method: http.MethodPost,
	}

	mockServer := newMockBSASProviderAuthServer(t, m)
	defer mockServer.Close()
	client := getTestBSASClient(mockServer.URL, mockServer.Client())

	ctx := context.Background()
	err := client.Auth(ctx)
	require.NoError(t, err)

	assert.Equal(t, accessToken, client.Tokens.AccessToken)
	assert.Equal(t, tokenType, client.Tokens.TokenType)
	assert.Equal(t, expiresIn, client.Tokens.ExpiresIn)
	assert.Equal(t, message, client.Tokens.Message)
}
