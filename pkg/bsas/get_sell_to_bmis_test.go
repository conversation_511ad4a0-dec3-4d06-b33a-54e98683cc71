package bsas

import (
	"context"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"git.redmadrobot.com/zaman/backend/zaman/pkg/bsas/entity"
)

func TestBSASProvider_GetSellToBMIS_Success(t *testing.T) {
	m := mockServerCase{
		StatusCode: http.StatusOK,
		Resp: map[string]any{
			"ECERTNO":              "ACP26FEB21-0000001-000",
			"SELLER":               "BankXYZ",
			"BUYER":                "CustomerABC",
			"TOTALVALUE":           "100000.00",
			"CURRENCY":             "MYR",
			"PRICE":                "50.00",
			"PRICE_MYR_EQUIVALENT": "50.00",
			"SELLINGTIMEDATE":      "********100033",
			"VALUEDATE":            "********",
			"PNAME":                "Product Name",
			"PVOLUME":              "2000",
			"LINE": []map[string]any{
				{
					"SUPPLIER": "Supplier1",
					"VOLUME":   "1000",
				},
				{
					"SUPPLIER": "Supplier2",
					"VOLUME":   "1000",
				},
			},
		},
		URI:    getSellToBMISEndpoint,
		Method: http.MethodPost,
	}

	mockServer := newMockBSASProviderAuthServer(t, m)
	defer mockServer.Close()
	client := getTestBSASClient(mockServer.URL, mockServer.Client())

	ctx := context.Background()

	var (
		eCertNo         = "ACP26FEB21-0000001-000"
		memberShortName = "BankXYZ"
	)

	res, err := client.GetSellToBMIS(ctx, &entity.STBRequest{
		Input: entity.STBInput{
			ECertNo:         eCertNo,
			MemberShortName: memberShortName,
		},
	})
	require.NoError(t, err)
	require.NotNil(t, res)

	assert.Equal(t, eCertNo, res.ECertNo)
	assert.Equal(t, "BankXYZ", res.Seller)
	assert.Equal(t, "CustomerABC", res.Buyer)
	assert.Equal(t, "100000.00", res.TotalValue)
	assert.Equal(t, "MYR", res.Currency)
	assert.Equal(t, "50.00", res.Price)
	assert.Equal(t, "50.00", res.PriceMYREquivalent)
	assert.Equal(t, "********100033", res.SellingTimeDate)
	assert.Equal(t, "********", res.ValueDate)
	assert.Equal(t, "Product Name", res.ProductName)
	assert.Equal(t, "2000", res.ProductVolume)
	assert.Len(t, res.Line, 2)
	assert.Equal(t, "Supplier1", res.Line[0].Supplier)
	assert.Equal(t, "1000", res.Line[0].Volume)
	assert.Equal(t, "Supplier2", res.Line[1].Supplier)
	assert.Equal(t, "1000", res.Line[1].Volume)

	assert.Equal(t, accessToken, client.Tokens.AccessToken)
	assert.Equal(t, tokenType, client.Tokens.TokenType)
	assert.Equal(t, expiresIn, client.Tokens.ExpiresIn)
	assert.Equal(t, message, client.Tokens.Message)
}

func TestBSASProvider_GetSellToBMIS_NilRequest(t *testing.T) {
	mockServer := newMockBSASProviderAuthServer(t, mockServerCase{
		URI:        authEndpoint,
		Method:     http.MethodPost,
		StatusCode: http.StatusOK,
	})
	defer mockServer.Close()
	client := getTestBSASClient(mockServer.URL, mockServer.Client())

	ctx := context.Background()

	_, err := client.GetSellToBMIS(ctx, nil)

	require.Error(t, err)
	assert.Contains(t, err.Error(), "nil order result request")
}

func TestBSASProvider_GetSellToBMIS_AuthFailure(t *testing.T) {
	m := mockServerCase{
		StatusCode: http.StatusUnauthorized,
		Resp: map[string]any{
			"error":             "invalid_token",
			"error_description": "The access token has expired",
		},
		URI:    authEndpoint,
		Method: http.MethodPost,
	}

	mockServer := newMockBSASProviderAuthServer(t, m)
	defer mockServer.Close()
	client := getTestBSASClient(mockServer.URL, mockServer.Client())
	client.Tokens = nil

	ctx := context.Background()

	_, err := client.GetSellToBMIS(ctx, &entity.STBRequest{
		Input: entity.STBInput{
			ECertNo:         "ACP26FEB21-0000001-000",
			MemberShortName: "BankXYZ",
		},
	})

	require.Error(t, err)
	assert.Contains(t, err.Error(), "error authenticating")
}

func TestBSASProvider_GetSellToBMIS_HTTPError(t *testing.T) {
	authMock := mockServerCase{
		StatusCode: http.StatusOK,
		Resp: map[string]any{
			"access_token": accessToken,
			"token_type":   tokenType,
			"expires_in":   expiresIn,
			"message":      message,
		},
		URI:    authEndpoint,
		Method: http.MethodPost,
	}

	mockServer := newMockBSASProviderAuthServer(t, authMock)
	defer mockServer.Close()
	client := getTestBSASClient("http://invalid-url.local", mockServer.Client())
	client.Tokens = &entity.Token{
		AccessToken: accessToken,
		TokenType:   tokenType,
		ExpiresIn:   expiresIn,
		Message:     message,
	}

	ctx := context.Background()

	resp, err := client.GetSellToBMIS(ctx, &entity.STBRequest{
		Input: entity.STBInput{
			ECertNo:         "ACP26FEB21-0000001-000",
			MemberShortName: "BankXYZ",
		},
	})

	require.Error(t, err)
	require.NotNil(t, resp)
	assert.Equal(t, ErrBsasServerNotResponding, err)
	require.NotNil(t, resp.BsasErr)
	assert.Equal(t, "failed to do request for GetSellToBMIS", resp.BsasErr.Text)
}

func TestBSASProvider_GetSellToBMIS_NonOKStatusCode(t *testing.T) {
	m := mockServerCase{
		StatusCode: http.StatusBadRequest,
		Resp: map[string]any{
			"header": map[string]any{
				"errorCode": "E001",
				"errorMsg":  "Invalid certificate number",
			},
		},
		URI:    getSellToBMISEndpoint,
		Method: http.MethodPost,
	}

	mockServer := newMockBSASProviderAuthServer(t, m)
	defer mockServer.Close()
	client := getTestBSASClient(mockServer.URL, mockServer.Client())

	ctx := context.Background()

	resp, err := client.GetSellToBMIS(ctx, &entity.STBRequest{
		Input: entity.STBInput{
			ECertNo:         "INVALID-CERT",
			MemberShortName: "BankXYZ",
		},
	})

	require.Error(t, err)
	assert.Equal(t, ErrInternalServerError, err)
	assert.Equal(t, "E001", resp.BsasErr.Text)
	assert.Equal(t, "Invalid certificate number", resp.BsasErr.Description)
	assert.Equal(t, http.StatusBadRequest, resp.BsasErr.HTTPCode)
}

func TestBSASProvider_GetSellToBMIS_InvalidJSONResponse(t *testing.T) {
	invalidJSONResponse := `{"ECERTNO": "ACP26FEB21-0000001-000", "SELLER": "BankXYZ", "invalid json`

	authMock := mockServerCase{
		StatusCode: http.StatusOK,
		Resp: map[string]any{
			"access_token": accessToken,
			"token_type":   tokenType,
			"expires_in":   expiresIn,
			"message":      message,
		},
		URI:    authEndpoint,
		Method: http.MethodPost,
	}
	mockAuthServer := newMockBSASProviderAuthServer(t, authMock)
	defer mockAuthServer.Close()

	mockApiServer := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		if r.URL.Path == getSellToBMISEndpoint {
			w.Header().Set("Content-Type", "application/json")
			w.WriteHeader(http.StatusOK)
			_, err := w.Write([]byte(invalidJSONResponse))
			require.NoError(t, err)
		}
	}))
	defer mockApiServer.Close()

	client := getTestBSASClient(mockApiServer.URL, mockApiServer.Client())
	client.Tokens = &entity.Token{
		AccessToken: accessToken,
		TokenType:   tokenType,
		ExpiresIn:   expiresIn,
		Message:     message,
	}

	ctx := context.Background()

	_, err := client.GetSellToBMIS(ctx, &entity.STBRequest{
		Input: entity.STBInput{
			ECertNo:         "ACP26FEB21-0000001-000",
			MemberShortName: "BankXYZ",
		},
	})

	require.Error(t, err)
	assert.Contains(t, err.Error(), "failed to decode response")
}

func TestBSASProvider_GetSellToBMIS_EmptyResponse(t *testing.T) {
	m := mockServerCase{
		StatusCode: http.StatusOK,
		Resp:       map[string]any{},
		URI:        getSellToBMISEndpoint,
		Method:     http.MethodPost,
	}

	mockServer := newMockBSASProviderAuthServer(t, m)
	defer mockServer.Close()
	client := getTestBSASClient(mockServer.URL, mockServer.Client())

	ctx := context.Background()

	resp, err := client.GetSellToBMIS(ctx, &entity.STBRequest{
		Input: entity.STBInput{
			ECertNo:         "ACP26FEB21-0000001-000",
			MemberShortName: "BankXYZ",
		},
	})

	require.NoError(t, err)
	assert.Equal(t, "", resp.ECertNo)
	assert.Equal(t, "", resp.Seller)
	assert.Equal(t, "", resp.Buyer)
	assert.Equal(t, "", resp.TotalValue)
	assert.Nil(t, resp.BsasErr)
}
