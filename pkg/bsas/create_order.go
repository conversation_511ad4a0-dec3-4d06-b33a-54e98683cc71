package bsas

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
	"strconv"

	"git.redmadrobot.com/backend-go/rmr-pkg/core/logs"
	"git.redmadrobot.com/zaman/backend/zaman/pkg/bsas/entity"
)

// CreateOrder метод ввода и обработки новых заказов в BSAS
func (p *BsasProviderImpl) CreateOrder(ctx context.Context, req *entity.OrderRequest) (*entity.OrderResponse, error) {
	if req == nil {
		err := errors.New("nil order request")
		p.logBsasResult(ctx, createOrderMethodName, nil, nil, err)
		return nil, errors.New("nil order request")
	}

	token, err := p.GetValidToken(ctx)
	if err != nil {
		p.logBsasResult(ctx, createOrderMethodName, req, nil, err)
		return nil, err
	}

	// Формируем URL для запроса
	targetURL, err := p.buildBSASURL(ctx, createOrderEndpoint, createOrderMethodName)
	if err != nil {
		p.logBsasResult(ctx, createOrderMethodName, req, nil, err)
		return nil, err
	}

	// Выполняем HTTP-запрос
	resp, err := ExecuteBSASRequest(ctx, p.HTTPClient, targetURL, token, req)
	if err != nil {
		// Создаем объект ответа с информацией об ошибке
		var respBody entity.OrderResponse
		statusCode := http.StatusBadGateway
		if resp != nil {
			statusCode = resp.StatusCode
		}

		respBody.BsasErr = &entity.BsasErrResp{
			Text:        fmt.Sprintf("failed to do request for %s", createOrderMethodName),
			Description: err.Error(),
			HTTPCode:    statusCode,
		}
		p.logBsasResult(ctx, createOrderMethodName, req, respBody, err)
		// Возвращаем объект ответа с пользовательской ошибкой
		return &respBody, ErrBsasServerNotResponding
	}

	// Обрабатываем ответ
	return p.handleCreateOrderResponse(ctx, resp, createOrderMethodName)
}

// handleCreateOrderResponse обрабатывает ответ от BSAS API и возвращает результат или ошибку
func (p *BsasProviderImpl) handleCreateOrderResponse(
	ctx context.Context,
	resp *http.Response,
	methodName string,
) (*entity.OrderResponse, error) {
	defer func(Body io.ReadCloser) {
		err := Body.Close()
		if err != nil {
			logs.FromContext(ctx).Error().Msgf("failed to close response body: %v", err)
		}
	}(resp.Body)

	// Проверяем статус ответа
	var respBody entity.OrderResponse
	if resp.StatusCode != http.StatusOK {
		errResp, err := p.handleBsasRespErrStatusCode(ctx, resp)
		if err != nil {
			p.logBsasResult(ctx, methodName, nil, nil, err)
			return nil, err
		}

		respBody.BsasErr = errResp
		p.logBsasResult(ctx, methodName, nil, errResp, ErrInternalServerError)
		return &respBody, ErrInternalServerError
	}
	response, err := p.decodeCreateOrderResponse(ctx, resp.Body)
	if err != nil {
		decodeErr := fmt.Errorf("failed to decode response for %s request: %w", methodName, err)

		errResp, bsasParseErr := p.handleBsasRespErrStatusCode(ctx, resp)
		if bsasParseErr != nil {
			p.logBsasResult(ctx, methodName, nil, nil, errors.Join(decodeErr, bsasParseErr))
			return nil, errors.Join(decodeErr, bsasParseErr)
		}
		// Если статус 200, но не смогли распарсить, то создаем ошибку
		// Данная ошибка нужна для тех поддержки
		requestDetailedErr := makeBsasDetailedError(
			methodName,
			errResp.Text,
			errResp.Description,
			resp.StatusCode)

		p.logBsasResult(ctx, methodName, nil, errResp, errors.Join(decodeErr, requestDetailedErr))
		return nil, errors.Join(decodeErr, requestDetailedErr)
	}

	p.logBsasResult(ctx, methodName, nil, response, nil)
	return response, nil
}

// decodeCreateOrderResponse декодирует ответ от BSAS API с поддержкой разных типов поля errorCode
func (p *BsasProviderImpl) decodeCreateOrderResponse(ctx context.Context, body io.ReadCloser) (*entity.OrderResponse, error) {
	defer body.Close()
	bodyBytes, err := io.ReadAll(body)
	if err != nil {
		p.logBsasResult(ctx, "decodeCreateOrderResponse", nil, string(bodyBytes), err)
		return nil, fmt.Errorf("failed to read response body: %w", err)
	}

	var respBody entity.OrderResponse
	if err = json.Unmarshal(bodyBytes, &respBody); err == nil {
		return &respBody, nil
	}

	var intRespBody entity.OrderHeaderErrorCodeIntResponse
	if err = json.Unmarshal(bodyBytes, &intRespBody); err != nil {
		p.logBsasResult(ctx, "decodeCreateOrderResponse", string(bodyBytes), nil, err)
		return nil, fmt.Errorf("failed to decode response: %w", err)
	}

	result := &entity.OrderResponse{
		Body:    intRespBody.Body,
		BsasErr: intRespBody.BsasErr,
	}
	result.Header.MemberShortName = intRespBody.Header.MemberShortName
	result.Header.UUID = intRespBody.Header.UUID
	result.Header.ErrorMsg = intRespBody.Header.ErrorMsg
	if intRespBody.Header.ErrorCode != nil {
		errorCodeStr := strconv.Itoa(*intRespBody.Header.ErrorCode)
		result.Header.ErrorCode = &errorCodeStr
	}

	p.logBsasResult(ctx, "decodeCreateOrderResponse", nil, result, nil)
	return result, nil
}
