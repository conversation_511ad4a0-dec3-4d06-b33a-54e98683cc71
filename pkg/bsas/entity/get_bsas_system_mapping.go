package entity

// BidXMLRequest представляет запрос к API BSAS для получения сведений о сопоставлении информации
type BidXMLRequest struct {
	Input BidXMLRequestInput `json:"input"`
}

// BidXMLRequestInput содержит параметры запроса к API BSAS
type BidXMLRequestInput struct {
	ECertNo         string `json:"ecertno"`         // Номер электронного сертификата
	MemberShortName string `json:"membershortname"` // Короткое имя участника
}

// BidXMLResponse представляет ответ от API BSAS с информацией о сопоставлении
type BidXMLResponse struct {
	SuccessYN        string       `json:"SUCCESSYN"`            // null : Успех N : Неудача
	Msg              string       `json:"MSG"`                  // Причина отклонения
	ECertNo          string       `json:"ECERTNO"`              // Номер электронного сертификата
	Buyer            string       `json:"BUYER"`                // Имя покупателя
	Owner            string       `json:"OWNER"`                // Имя владельца
	BidNo            string       `json:"BIDNO"`                // Номер заявки заказа
	TotalValue       string       `json:"TOTALVALUE"`           // Итоговая сумма
	Currency         string       `json:"CURRENCY"`             // Валюта
	Price            string       `json:"PRICE"`                // Цена
	PriceMYREquiv    string       `json:"PRICE_MYR_EQUIVALENT"` // Цена продукта, эквивалентная MYR
	PurchaseTimeDate string       `json:"PURCHASETIMEDATE"`     // Дата и время покупки
	ValueDate        string       `json:"VALUEDATE"`            // Дата валютирования
	PName            string       `json:"PNAME"`                // Наименование продукта
	PVolume          string       `json:"PVOLUME"`              // Соответствующий объем
	Line             []BidXMLLine `json:"LINE"`                 // Строки поставщиков
	BsasErr          *BsasErrResp
}

// BidXMLLine представляет информацию о поставщике в ответе API BSAS
type BidXMLLine struct {
	Supplier string `json:"SUPPLIER"` // Имя поставщика
	Volume   string `json:"VOLUME"`   // Объем
}
