package entity

type (
	OrderRequest struct {
		Header  HeaderRequest `json:"header"`
		Request []Request     `json:"request"`
	}

	HeaderRequest struct {
		MemberShortName string `json:"memberShortName"`
		UUID            string `json:"uuid"`
	}

	Request struct {
		SerialNumber     string `json:"serialNumber"`
		BidOption        string `json:"bidOption"`
		OtcOption        string `json:"otcOption"`
		StbOption        string `json:"stbOption"`
		ProductCode      string `json:"productCode"`
		PurchaseType     string `json:"purchaseType"`
		ClientName       string `json:"clientName"`
		Currency         string `json:"currency"`
		BidValue         string `json:"bidValue"`
		ValueDate        string `json:"valueDate"`
		Tenor            string `json:"tenor"`
		OtcCounterParty  string `json:"otcCounterParty"`
		OtcMurabaha      string `json:"otcMurabaha"`
		OtcMurabahaValue string `json:"otcMurabahaValue"`
		ECertNo          string `json:"eCertNo"`
	}

	OrderResponse struct {
		Header  HeaderResp `json:"header"`
		Body    []Body     `json:"body"`
		BsasErr *BsasErrResp
	}

	Body struct {
		SerialNumber  int    `json:"serialNumber"`
		StatusCode    int    `json:"statusCode"`
		StatusMessage string `json:"statusMessage"`
	}

	HeaderResp struct {
		MemberShortName string  `json:"memberShortName"`
		UUID            string  `json:"uuid"`
		ErrorCode       *string `json:"errorCode"`
		ErrorMsg        *string `json:"errorMsg"`
	}

	OrderHeaderErrorCodeIntResponse struct {
		Header  HeaderErrorCodeIntResp `json:"header"`
		Body    []Body                 `json:"body"`
		BsasErr *BsasErrResp
	}

	HeaderErrorCodeIntResp struct {
		MemberShortName string  `json:"memberShortName"`
		UUID            string  `json:"uuid"`
		ErrorCode       *int    `json:"errorCode"`
		ErrorMsg        *string `json:"errorMsg"`
	}
)
