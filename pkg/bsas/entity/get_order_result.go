package entity

type (
	// GetOrderResultRequest представляет запрос к Order Result  для получения результата обработки заказа в BSAS
	GetOrderResultRequest struct {
		Header  HeaderRequest          `json:"header"`
		Request OrderResultItemRequest `json:"request"`
	}

	// OrderResultItemRequest содержит параметры запроса к BSAS Order Result
	OrderResultItemRequest struct {
		SerialNumber  *string `json:"serialNumber"`
		ForceYN       string  `json:"forceYN"`
		MaxWaitTime   *string `json:"maxWaitTime"`
		WaitAllDoneYN *string `json:"waitAllDoneYN"`
	}

	GetOrderResultResponse struct {
		Header  HeaderResp            `json:"header"`
		Status  StatusResp            `json:"status"`
		Body    []OrderResultBodyResp `json:"body"`
		BsasErr *BsasErrResp
	}

	GetOrderResultHeaderErrorCodeIntResponse struct {
		Header  HeaderErrorCodeIntResp `json:"header"`
		Status  StatusResp             `json:"status"`
		Body    []OrderResultBodyResp  `json:"body"`
		BsasErr *BsasErrResp
	}

	StatusResp struct {
		TotalOrderCount int `json:"totalOrderCount"` // Итоговый номер процесса заказа
		ProcessingCount int `json:"processingCount"` // Количество заказов в процессе
	}

	OrderResultBodyResp struct {
		SerialNumber     int    `json:"serialNumber"` // Запись количества заказов
		BidOption        string `json:"bidOption"`    // BID заказ – купить товар
		OtcOption        string `json:"otcOption"`    // Внебиржевой заказ – передача права собственности на товар клиенту
		StbOption        string `json:"stbOption"`    // Заказ STB – Продать товар клиенту
		ProductCode      string `json:"productCode"`  // Код продукта
		PurchaseType     string `json:"purchaseType"` // Тип покупки
		ClientName       string `json:"clientName"`   // ФИО клиента на латинском
		Currency         string `json:"currency"`     // Валюта
		BidValue         string `json:"bidValue"`     // Сумма покупки/продажи
		ValueDate        string `json:"valueDate"`    // Дата запроса покупки/продажи Передает дату по Малазии
		Tenor            string `json:"tenor"`        // Значение тенора
		OtcCounterParty  string `json:"otcCounterParty"`
		OtcMurabaha      string `json:"otcMurabaha"`
		OtcMurabahaValue string `json:"otcMurabahaValue"`
		EcertNo          string `json:"ecertNo"`
		BidErrNo         string `json:"bidErrNo"`     // Номер кода ошибки для BID
		BidMsg           string `json:"bidMsg"`       // Сообщение для BID
		OtcErrNo         string `json:"otcErrNo"`     // Номер кода ошибки для OTC
		OtcMsg           string `json:"otcMsg"`       // Сообщение для OTC
		StbErrNo         string `json:"stbErrNo"`     // Номер кода ошибки для STB
		StbMsg           string `json:"stbMsg"`       // Сообщение для СТБ
		RegTime          string `json:"regTime"`      // Время регистрации (hhmmssmss)
		OrderTime        string `json:"orderTime"`    // Время заказа (hhmmssmss)
		ResultTime       string `json:"resultTime"`   // Время генерации данных результата (hhmmssmss)
		PurchaseTime     string `json:"purchaseTime"` // Время покупки (hhmmssmss)
		ReportTime       string `json:"reportTime"`   // Время внебиржевой отчетности (hhmmssmss)
		SellingTime      string `json:"sellingTime"`  // Время СТБ (hhmmssmss)
		Unit             string `json:"unit"`         // Единица
		Price            string `json:"price"`        // Цена
	}
)
