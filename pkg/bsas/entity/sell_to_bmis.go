package entity

type (
	// STBRequest представляет структуру запроса к API BSAS для получения данных Sell-to-BMIS (STB).
	STBRequest struct {
		Input STBInput `json:"input"`
	}

	// STBInput содержит параметры запроса: номер сертификата и короткое имя участника.
	STBInput struct {
		ECertNo         string `json:"ecertno"`
		MemberShortName string `json:"membershortname"`
	}

	// STBResponse представляет структуру ответа API на STB-запрос.
	STBResponse struct {
		SuccessYN          string    `json:"SUCCESSYN"` // null : Успех N : Неудача
		Msg                string    `json:"MSG"`       // Причина отклонения
		ECertNo            string    `json:"ECERTNO"`
		Seller             string    `json:"SELLER"`
		Buyer              string    `json:"BUYER"`
		TotalValue         string    `json:"TOTALVALUE"`
		Currency           string    `json:"CURRENCY"`
		Price              string    `json:"PRICE"`
		PriceMYREquivalent string    `json:"PRICE_MYR_EQUIVALENT"`
		SellingTimeDate    string    `json:"SELLINGTIMEDATE"`
		ValueDate          string    `json:"VALUEDATE"`
		ProductName        string    `json:"PNAME"`
		ProductVolume      string    `json:"PVOLUME"`
		Line               []STBLine `json:"LINE"`
		BsasErr            *BsasErrResp
	}
	// STBLine представляет строку в детализации сделки.
	STBLine struct {
		Supplier string `json:"SUPPLIER"`
		Volume   string `json:"VOLUME"`
	}
)
