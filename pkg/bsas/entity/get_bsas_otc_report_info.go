package entity

type OtcXMLRequest struct {
	Input BidOTCRequestInput `json:"input"`
}

type BidOTCRequestInput struct {
	ECertNo         string `json:"ecertno"`         // Номер электронного сертификата
	MemberShortName string `json:"membershortname"` // Короткое имя участника
}

type BidOTCXMLResponse struct {
	SuccessYN         string          `json:"SUCCESSYN"`            // null : Успех N : Неудача
	Msg               string          `json:"MSG"`                  // Причина отклонения
	ECertNo           string          `json:"ECERTNO"`              // Номер электронного сертификата
	Buyer             string          `json:"BUYER"`                // Имя покупателя
	MuraBahavalue     string          `json:"MURABAHAVALUE"`        // Стоимость Мурабаха
	TotalValue        string          `json:"TOTALVALUE"`           // Итоговая сумма
	Currency          string          `json:"CURRENCY"`             // Валюта
	Price             string          `json:"PRICE"`                // Цена
	PriceMYREquiv     string          `json:"PRICE_MYR_EQUIVALENT"` // Цена продукта, эквивалентная MYR
	ReportingTimeDate string          `json:"REPORTINGTIMEDATE"`    // Дата и время отчета
	ValueDate         string          `json:"VALUEDATE"`            // Дата валютирования
	PName             string          `json:"PNAME"`                // Наименование продукта
	PVolume           string          `json:"PVOLUME"`              // Соответствующий объем
	Seller            string          `json:"SELLER"`               // Имя продавца
	Line              []BidOTCXMLLine `json:"LINE"`                 // Строки поставщиков
	BsasErr           *BsasErrResp
}

type BidOTCXMLLine struct {
	Supplier string `json:"SUPPLIER"` // Имя поставщика
	Volume   string `json:"VOLUME"`   // Объем
}
