package entity

type Token struct {
	TokenType   string `json:"token_type"`
	AccessToken string `json:"access_token"`
	ExpiresIn   int    `json:"expires_in"`
	Message     string `json:"message"`
}

type TokenError struct {
	TokenType   string `json:"token_type"`
	AccessToken string `json:"access_token"`
	ExpiresIn   string `json:"expires_in"`
	Message     string `json:"message"`
}

type AuthRequest struct {
	GrantType    string `json:"grant_type"`
	ClientID     string `json:"client_id"`
	ClientSecret string `json:"client_secret"`
}

type (
	BsasErrResp struct {
		Text        string `json:"Text"`
		Description string `json:"Description"`
		HTTPCode    int    `json:"HTTPCode"`
	}

	EnvelopeWithHeader struct {
		Header struct {
			ErrorCode *string `json:"errorCode"`
			ErrorMsg  *string `json:"errorMsg"`
		} `json:"header"`
		SuccessYN *string `json:"SUCCESSYN"` // null : Успех N : Неудача
		Msg       *string `json:"MSG"`       // Причина отклонения
	}
)

type LogInfo struct {
	Request    interface{}
	Response   interface{}
	Error      error
	MethodName string
}
