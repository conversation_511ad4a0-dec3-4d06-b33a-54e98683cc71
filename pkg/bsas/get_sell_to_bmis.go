package bsas

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"

	"git.redmadrobot.com/backend-go/rmr-pkg/core/logs"
	"git.redmadrobot.com/zaman/backend/zaman/pkg/bsas/entity"
)

// GetSellToBMIS метод извлечения данных о Sell-to-BMIS (STB)
func (p *BsasProviderImpl) GetSellToBMIS(ctx context.Context, req *entity.STBRequest) (*entity.STBResponse, error) {
	if req == nil {
		err := errors.New("nil order result request")
		p.logBsasResult(ctx, getSellToBMISMethodName, nil, nil, err)
		return nil, err
	}

	token, err := p.GetValidToken(ctx)
	if err != nil {
		p.logBsasResult(ctx, getSellToBMISMethodName, req, nil, err)
		return nil, err
	}

	// Формируем URL для запроса
	targetURL, err := p.buildBSASURL(ctx, getSellToBMISEndpoint, getSellToBMISMethodName)
	if err != nil {
		p.logBsasResult(ctx, getSellToBMISMethodName, req, nil, err)
		return nil, err
	}

	// Выполняем HTTP-запрос
	resp, err := ExecuteBSASRequest(ctx, p.HTTPClient, targetURL, token, req)
	if err != nil {
		// Создаем объект ответа с информацией об ошибке
		var respBody entity.STBResponse
		statusCode := http.StatusBadGateway
		if resp != nil {
			statusCode = resp.StatusCode
		}
		respBody.BsasErr = &entity.BsasErrResp{
			Text:        fmt.Sprintf("failed to do request for %s", getSellToBMISMethodName),
			Description: err.Error(),
			HTTPCode:    statusCode,
		}
		p.logBsasResult(ctx, getSellToBMISMethodName, req, respBody, err)
		// Возвращаем объект ответа с пользовательской ошибкой
		return &respBody, ErrBsasServerNotResponding
	}

	// Обрабатываем ответ
	return p.handleGetSellToBMISResponse(ctx, resp, getSellToBMISMethodName)
}

// handleGetSellToBMISResponse обрабатывает ответ от BSAS API и возвращает результат или ошибку
func (p *BsasProviderImpl) handleGetSellToBMISResponse(
	ctx context.Context,
	resp *http.Response,
	methodName string,
) (*entity.STBResponse, error) {
	defer func(Body io.ReadCloser) {
		err := Body.Close()
		if err != nil {
			logs.FromContext(ctx).Error().Msgf("failed to close response body: %v", err)
		}
	}(resp.Body)

	// Проверяем статус ответа
	var respBody entity.STBResponse
	if resp.StatusCode != http.StatusOK {
		errResp, err := p.handleBsasRespErrStatusCode(ctx, resp)
		if err != nil {
			p.logBsasResult(ctx, methodName, nil, nil, err)
			return nil, err
		}

		respBody.BsasErr = errResp
		p.logBsasResult(ctx, methodName, nil, errResp, ErrInternalServerError)
		return &respBody, ErrInternalServerError
	}

	if err := json.NewDecoder(resp.Body).Decode(&respBody); err != nil {
		decodeErr := fmt.Errorf("failed to decode response for %s request: %w", methodName, err)

		errResp, bsasParseErr := p.handleBsasRespErrStatusCode(ctx, resp)
		if bsasParseErr != nil {
			p.logBsasResult(ctx, methodName, nil, nil, errors.Join(decodeErr, bsasParseErr))
			return nil, errors.Join(decodeErr, bsasParseErr)
		}
		// Если статус 200, но не смогли распарсить, то создаем ошибку
		// Данная ошибка нужна для тех поддержки
		requestDetailedErr := makeBsasDetailedError(
			methodName,
			errResp.Text,
			errResp.Description,
			resp.StatusCode)
		p.logBsasResult(ctx, methodName, nil, errResp, errors.Join(decodeErr, requestDetailedErr))
		return nil, errors.Join(decodeErr, requestDetailedErr)
	}

	p.logBsasResult(ctx, methodName, nil, respBody, nil)
	return &respBody, nil
}
