package bsas

import (
	"context"
	"fmt"
	"git.redmadrobot.com/zaman/backend/zaman/pkg/bsas/entity"
	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"
)

func TestBSASProvider_CreateOrder_Success(t *testing.T) {
	m := mockServerCase{
		StatusCode: http.StatusOK,
		Resp: map[string]any{
			"header": map[string]any{
				"memberShortName": "BANKXYZ",
				"uuid":            "550e8400-e29b-41d4-a716-************",
				"errorCode":       "",
				"errorMsg":        "",
			},
			"body": []any{
				map[string]any{
					"serialNumber":  1,
					"statusCode":    0,
					"statusMessage": "",
				},
				map[string]any{
					"serialNumber":  2,
					"statusCode":    0,
					"statusMessage": "",
				},
			},
		},
		URI:    createOrderEndpoint,
		Method: http.MethodPost,
	}

	mockServer := newMockBSASProviderAuthServer(t, m)
	defer mockServer.Close()
	client := getTestBSASClient(mockServer.URL, mockServer.Client())

	ctx := context.Background()
	_, err := client.CreateOrder(ctx, &entity.OrderRequest{
		Header: entity.HeaderRequest{
			MemberShortName: "zaman",
			UUID:            uuid.NewString(),
		},
		Request: []entity.Request{
			{
				SerialNumber:     fmt.Sprint(1),
				BidOption:        "Y",
				OtcOption:        "N",
				StbOption:        "N",
				ProductCode:      "AL-MSIA- 23",
				PurchaseType:     "P",
				ClientName:       "Иван Иваныч Иванов",
				Currency:         "RUB",
				BidValue:         "12345.00",
				ValueDate:        time.Now().Format("20060102"),
				Tenor:            "00035",
				OtcCounterParty:  "",
				OtcMurabaha:      "",
				OtcMurabahaValue: "",
				ECertNo:          "",
			},
		},
	})
	require.NoError(t, err)

	assert.Equal(t, accessToken, client.Tokens.AccessToken)
	assert.Equal(t, tokenType, client.Tokens.TokenType)
	assert.Equal(t, expiresIn, client.Tokens.ExpiresIn)
	assert.Equal(t, message, client.Tokens.Message)
}

// TestBSASProvider_CreateOrder_Error проверяет обработку ошибок при создании заказа
func TestBSASProvider_CreateOrder_Error(t *testing.T) {
	// Тест для случая, когда сервер возвращает ошибку
	t.Run("Server returns error", func(t *testing.T) {
		// Создаем мок-сервер, который возвращает ошибку
		errorServer := httptest.NewServer(
			http.HandlerFunc(
				func(w http.ResponseWriter, r *http.Request) {
					// Проверяем метод запроса
					assert.Equal(t, http.MethodPost, r.Method)

					// Проверяем путь запроса
					assert.Contains(t, r.URL.Path, createOrderEndpoint)

					// Устанавливаем статус ответа
					w.WriteHeader(http.StatusInternalServerError)

					// Отправляем ответ с ошибкой
					w.Write([]byte(`{
						"header": {
							"memberShortName": "",
							"uuid": "",
							"errorCode": "ERR-001",
							"errorMsg": "Ошибка при создании заказа"
						}
					}`))
				},
			),
		)
		defer errorServer.Close()

		// Создаем клиент с мок-сервером
		client := getTestBSASClient(errorServer.URL, errorServer.Client())

		// Вызываем метод CreateOrder
		ctx := context.Background()
		resp, err := client.CreateOrder(ctx, &entity.OrderRequest{
			Header: entity.HeaderRequest{
				MemberShortName: "zaman",
				UUID:            uuid.NewString(),
			},
			Request: []entity.Request{
				{
					SerialNumber: fmt.Sprint(1),
					BidOption:    "Y",
					OtcOption:    "N",
					StbOption:    "N",
					ProductCode:  "AL-MSIA- 23",
					PurchaseType: "P",
					ClientName:   "Тестовый Клиент",
					Currency:     "RUB",
					BidValue:     "12345.00",
					ValueDate:    time.Now().Format("20060102"),
					Tenor:        "00035",
				},
			},
		})

		// Проверяем наличие ошибки
		require.Error(t, err)
		assert.Contains(t, err.Error(), ErrInternalServerError.Error())

		// Проверяем, что в ответе есть информация об ошибке
		require.NotNil(t, resp)
		require.NotNil(t, resp.BsasErr)
		assert.Equal(t, "ERR-001", resp.BsasErr.Text)
		assert.Equal(t, "Ошибка при создании заказа", resp.BsasErr.Description)
	})

	// Тест для случая, когда сервер недоступен
	t.Run("Server not responding", func(t *testing.T) {
		// Создаем клиент с недоступным URL
		client := getTestBSASClient("http://localhost:12345", http.DefaultClient)

		// Вызываем метод CreateOrder
		ctx := context.Background()
		resp, err := client.CreateOrder(ctx, &entity.OrderRequest{
			Header: entity.HeaderRequest{
				MemberShortName: "zaman",
				UUID:            uuid.NewString(),
			},
			Request: []entity.Request{
				{
					SerialNumber: fmt.Sprint(1),
					BidOption:    "Y",
					OtcOption:    "N",
					StbOption:    "N",
					ProductCode:  "AL-MSIA- 23",
					PurchaseType: "P",
					ClientName:   "Тестовый Клиент",
					Currency:     "RUB",
					BidValue:     "12345.00",
					ValueDate:    time.Now().Format("20060102"),
					Tenor:        "00035",
				},
			},
		})

		// Проверяем наличие ошибки
		require.Error(t, err)

		// Проверяем, что в ответе есть информация об ошибке
		require.NotNil(t, resp)
		require.NotNil(t, resp.BsasErr)
		assert.Equal(t, http.StatusBadGateway, resp.BsasErr.HTTPCode)
		assert.Equal(t, "failed to do request for CreateOrder", resp.BsasErr.Text)
	})
}
