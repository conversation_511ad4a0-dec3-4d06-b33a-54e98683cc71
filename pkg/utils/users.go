package utils

import (
	"context"
	"strings"
	"time"

	"google.golang.org/grpc/metadata"

	"git.redmadrobot.com/backend-go/rmr-pkg/httpx/mw"

	"git.redmadrobot.com/backend-go/rmr-pkg/core/errs"
)

const (
	defaultDatetimeLayout = "2006-01-02"
)

func GetAgeFromBirthString(birthDateStr string) (int, error) {
	birthDate, err := time.Parse(defaultDatetimeLayout, birthDateStr)
	if err != nil {
		return 0, errs.Wrapf(err, "user birthday date parse err")
	}

	now := time.Now()
	age := now.Year() - birthDate.Year()

	// Проверяем что год рождения високосный
	isLeapYear := isLeapYear(birthDate.Year())

	// Получаем день в году и если год високосный (на день больше) делаем доп проверки
	birthdayYearDay := birthDate.YearDay()
	if isLeapYear {
		// Если родились позже февраля месяца в високосном году
		if birthDate.Month() > time.February {
			birthdayYearDay--
		}
	}

	if now.YearDay() < birthdayYearDay {
		age--
	}

	return age, nil
}

func ConvertDateStringToTime(dateStr string) (time.Time, error) {
	birthDate, err := time.Parse(defaultDatetimeLayout, dateStr)
	if err != nil {
		return time.Time{}, errs.Wrapf(err, "user birthday date parse err")
	}

	return birthDate, nil
}

// isLeapYear - Метод для проверки является ли год високосным
func isLeapYear(year int) bool {
	return (year%4 == 0 && year%100 != 0) || (year%400 == 0)
}

func isReviewerFromHTTPContext(ctx context.Context) bool {
	userInfo, ok := ctx.Value(mw.UserInfoKey).(map[string]interface{})
	if !ok {
		return false
	}

	rolesVal, exists := userInfo["roles"]
	if !exists {
		return false
	}

	roles, ok := rolesVal.(string)
	if !ok {
		return false
	}

	rolesList := strings.Split(roles, ",")
	for _, role := range rolesList {
		if strings.EqualFold(strings.TrimSpace(role), "reviewer") {
			return true
		}
	}

	return false
}

// Для получения ролей, полученных из авторизационного токена
func isReviewerFromGRPCContext(ctx context.Context) bool {
	md, ok := metadata.FromIncomingContext(ctx)
	if ok {
		// Проверяем роли
		rolesVal := md.Get("roles")
		if len(rolesVal) > 0 {
			roles := strings.Split(rolesVal[0], ",")
			for _, role := range roles {
				if strings.EqualFold(strings.TrimSpace(role), "reviewer") {
					return true
				}
			}
		}
	}

	return false
}

// Для запросов, где устанавливается явно через metadata.Pairs("specialUser", "review")
func isSpecialUserReviewFromGRPCContext(ctx context.Context) bool {
	md, ok := metadata.FromIncomingContext(ctx)
	if ok {
		// Устанавливаем роль reviewer если соответствующие данные есть в метаданных
		specUser := md.Get("specialUser")
		if len(specUser) > 0 {
			if specUser[0] == "review" {
				return true
			}
		}
	}
	return false
}

// IsReviewer return true if current user is special user "reviewer"
func IsReviewer(ctx context.Context) bool {
	return isReviewerFromHTTPContext(ctx) ||
		isReviewerFromGRPCContext(ctx) ||
		isSpecialUserReviewFromGRPCContext(ctx)
}
