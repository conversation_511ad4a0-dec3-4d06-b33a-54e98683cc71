package utils

import "time"

type (
	RawData[ReqT any, RespT any, PayloadT any] struct {
		Request   ReqT      `json:"request"`
		Response  *RespT    `json:"response,omitempty"`
		StartTime int64     `json:"startTime"`
		Timestamp int64     `json:"timestamp"`
		Error     *string   `json:"error,omitempty"`
		UserID    string    `json:"user_id"`
		RequestID string    `json:"request_id"`
		Payload   *PayloadT `json:"payload,omitempty"`
	}
)

func NewRawData[ReqT any, RespT any, PayloadT any](request ReqT) *RawData[ReqT, RespT, PayloadT] {
	return &RawData[ReqT, RespT, PayloadT]{
		Request:   request,
		StartTime: time.Now().Unix(),
	}
}

func (data *RawData[ReqT, RespT, PayloadT]) WithUserID(userID string) *RawData[ReqT, RespT, PayloadT] {
	data.UserID = userID
	return data
}

func (data *RawData[ReqT, RespT, PayloadT]) WithRequestID(requestID string) *RawData[ReqT, RespT, PayloadT] {
	data.RequestID = requestID
	return data
}

func (data *RawData[ReqT, RespT, PayloadT]) WithPayload(payload *PayloadT) *RawData[ReqT, RespT, PayloadT] {
	data.Payload = payload
	return data
}

func (data *RawData[ReqT, RespT, PayloadT]) WithError(err error) *RawData[ReqT, RespT, PayloadT] {
	if err != nil {
		errMsg := err.Error()
		data.Error = &errMsg
	}
	return data
}

func (data *RawData[ReqT, RespT, PayloadT]) WithTimeStamp() *RawData[ReqT, RespT, PayloadT] {
	data.Timestamp = time.Now().Unix()
	return data
}

func (data *RawData[ReqT, RespT, PayloadT]) WithResponse(resp *RespT) *RawData[ReqT, RespT, PayloadT] {
	data.Response = resp
	return data
}
