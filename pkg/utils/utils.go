package utils

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"math"
	"reflect"
	"runtime"
	"strings"
	"time"

	"github.com/go-chi/chi/v5/middleware"
	"github.com/google/uuid"
	"github.com/rs/zerolog"

	"git.redmadrobot.com/backend-go/rmr-pkg/core/logs"
	"git.redmadrobot.com/backend-go/rmr-pkg/httpx/mw"
	"git.redmadrobot.com/backend-go/rmr-pkg/providers/kafka"
	"git.redmadrobot.com/backend-go/rmr-pkg/tools/acceptlanguage"

	usersErrs "git.redmadrobot.com/zaman/backend/zaman/errs/users"
	"git.redmadrobot.com/zaman/backend/zaman/pkg/helpers/locale"
)

// Значение для идентификации источника запроса, возвращается из GetOrigin
const (
	UserOriginMobile = "mobile"
	UserOriginSme    = "sme"
	UserOriginTest   = "test"
)

// ReqSourceKey Ключ значения контекста для хранения источника (например MobileApp или SmeApp)
const ReqSourceKey = "reqSource"

// Значение для идентификации источника запроса, находится в контексте в качестве ключа ReqSourceKey:
const (
	MobileApp = "MOBILE"
	RetailApp = MobileApp
	SmeApp    = "SME"
	TestApp   = "TEST"
)

const (
	defaultLocale = locale.Kk
	kzTimeZone    = "Asia/Almaty"

	UserInfoKey = "userInfo"

	UserIDHeaderKey         = "user_id"
	SessionIDHeaderKey      = "session_id"
	PhoneNumberHeaderKey    = "phone_number"
	AcceptLanguageHeaderKey = "acceptLanguage"

	IPPrefix = "ИП"
)

var KzTimeZone *time.Location

func init() {
	var err error
	KzTimeZone, err = time.LoadLocation(kzTimeZone)
	if err != nil {
		panic(err)
	}
}

func GetRequestSourceByOrigin(origin string) string {
	return mapOriginToReqSource(origin)
}

// mapOriginToReqSource преобразует значение origin в соответствующее значение reqSource
func mapOriginToReqSource(origin string) string {
	switch origin {
	case UserOriginMobile:
		return MobileApp
	case UserOriginSme:
		return SmeApp
	case UserOriginTest:
		return TestApp
	default:
		return MobileApp // По умолчанию используем MobileApp
	}
}

func GetCurrentKzTime() (time.Time, error) {
	// Загружаем часовой пояс Казахстана
	loc, err := time.LoadLocation(kzTimeZone)
	if err != nil {
		return time.Now(), err
	}

	// Получаем текущее время в Казахстане
	return time.Now().In(loc), nil
}

func BuildAddress(components ...*string) string {
	var parts []string
	for _, c := range components {
		if c != nil {
			parts = append(parts, *c)
		}
	}
	return strings.Join(parts, ", ")
}

func MakeRawData[ReqT, RespT, PayloadT any](req ReqT, resp *RespT, err error, userID string, requestID string, payload *PayloadT) RawData[ReqT, RespT, PayloadT] {
	var errString *string
	if err != nil {
		errMsg := err.Error()
		errString = &errMsg
	}

	return RawData[ReqT, RespT, PayloadT]{
		Request:   req,
		Response:  resp,
		Timestamp: time.Now().Unix(),
		Error:     errString,
		UserID:    userID,
		RequestID: requestID,
		Payload:   payload,
	}
}

type BridgeKafkaMessage[T any] struct {
	Payload T `json:"payload"`
}

func ExtractRequestID(ctx context.Context) string {
	var requestID string

	switch ctx.Value(middleware.RequestIDKey).(type) {
	case nil:
		logs.FromContext(ctx).Error().Msgf("requestID is nil")
		requestID = uuid.NewString()
	case string:
		requestID = ctx.Value(middleware.RequestIDKey).(string)
	}

	return requestID
}

func SetRequestID(ctx context.Context, requestID string) context.Context {
	value := ctx.Value(middleware.RequestIDKey)
	if value == nil {
		return context.WithValue(ctx, middleware.RequestIDKey, requestID)
	}
	return ctx
}

func SetSubRequestID(ctx context.Context, requestID string) context.Context {
	value := ctx.Value(logs.SubRequestIDKey)
	if value == nil {
		return context.WithValue(ctx, logs.SubRequestIDKey, requestID)
	}
	return ctx
}

func MapToStruct(data map[string]interface{}, out interface{}) error {
	v := reflect.ValueOf(out)
	if v.Kind() != reflect.Ptr || v.IsNil() {
		return errors.New("out must be a non-nil pointer to a struct")
	}

	jsonData, err := json.Marshal(data)
	if err != nil {
		return err
	}

	return json.Unmarshal(jsonData, out)
}

func StructToMap(data interface{}) (map[string]interface{}, error) {
	if data == nil {
		return make(map[string]interface{}), nil
	}

	b, err := json.Marshal(data)
	if err != nil {
		return nil, err
	}

	m := make(map[string]interface{})
	if err := json.Unmarshal(b, &m); err != nil {
		return nil, err
	}
	return m, nil
}

func IsToday(t time.Time) bool {
	now := time.Now()

	return t.Year() == now.Year() &&
		t.Month() == now.Month() &&
		t.Day() == now.Day()
}

func StructToMapSlice(data interface{}) ([]map[string]interface{}, error) {
	if data == nil {
		return nil, nil
	}

	b, err := json.Marshal(data)
	if err != nil {
		return nil, err
	}

	var result []map[string]interface{}
	if err := json.Unmarshal(b, &result); err != nil {
		return nil, err
	}
	return result, nil
}

func MapToStructSlice(data []map[string]interface{}, out interface{}) error {
	v := reflect.ValueOf(out)
	if v.Kind() != reflect.Ptr || v.IsNil() {
		return errors.New("out must be a non-nil pointer to a slice")
	}
	if v.Elem().Kind() != reflect.Slice {
		return errors.New("out must be a pointer to a slice")
	}

	jsonData, err := json.Marshal(data)
	if err != nil {
		return err
	}

	return json.Unmarshal(jsonData, out)
}

func SetOrigin(ctx context.Context, reqSource string) context.Context {
	return context.WithValue(ctx, ReqSourceKey, reqSource) //nolint:staticcheck
}

func GetBeginningOfDayKz() (*time.Time, error) {
	kzTime, err := GetCurrentKzTime()
	if err != nil {
		return nil, err
	}

	beginningOfDay := time.Date(kzTime.Year(), kzTime.Month(), kzTime.Day(), 0, 0, 0, 0, kzTime.Location())
	return &beginningOfDay, nil
}

func JoinStringsWithSpaces(elems ...string) string {
	joined := strings.Join(elems, " ")
	return strings.TrimSpace(joined)
}

func GetOrigin(ctx context.Context) (string, error) {
	gateway := ctx.Value(ReqSourceKey)
	if gateway == nil {
		return "", usersErrs.UsersErrs().OriginNotFoundError()
	}
	gatewayStr, ok := gateway.(string)
	if !ok {
		return "", usersErrs.UsersErrs().OriginNotFoundError()
	}

	switch gatewayStr {
	case MobileApp:
		return UserOriginMobile, nil
	case SmeApp:
		return UserOriginSme, nil
	case TestApp:
		return UserOriginTest, nil
	default:
		return "", usersErrs.UsersErrs().OriginNotFoundError()
	}
}

func PutMobileOrigin(ctx context.Context) context.Context {
	return context.WithValue(ctx, ReqSourceKey, MobileApp) //nolint:staticcheck
}

func PutSmeOrigin(ctx context.Context) context.Context {
	return context.WithValue(ctx, ReqSourceKey, SmeApp) //nolint:staticcheck
}

// PutInfoFromMsgHeadersIntoCtx извлекает информацию пользователя из заголовков Kafka-сообщения и помещает её в контекст.
// Функция извлекает идентификатор пользователя (user_id), идентификатор сессии (session_id),
// номер телефона (phone_number), источник запроса (reqSource) и локаль пользователя (acceptLanguage)
// из заголовков сообщения. Если идентификаторы имеют неверный формат UUID, функция логирует предупреждение.
// Также функция устанавливает идентификатор запроса (requestID) в контексте.
// Возвращает новый контекст с добавленной информацией.
func PutInfoFromMsgHeadersIntoCtx(ctx context.Context, msg kafka.Message) context.Context {
	var userInfo UserInfoCtx
	userID := msg.Header(UserIDHeaderKey)

	parsedUserID, err := uuid.Parse(userID)
	if err == nil {
		userInfo.UserID = parsedUserID
	} else {
		logs.FromContext(ctx).Warn().Msg("failed to parse userID from kafka msg header")
	}

	sessionID := msg.Header(SessionIDHeaderKey)
	parsedSessionID, err := uuid.Parse(sessionID)
	if err == nil {
		userInfo.SessionID = parsedSessionID
	} else {
		logs.FromContext(ctx).Warn().Msg("failed to parse sessionID from kafka msg header")
	}

	phoneNumber := msg.Header(PhoneNumberHeaderKey)
	userInfo.Phone = phoneNumber

	reqSource := msg.Header(ReqSourceKey)
	userInfo.Origin = reqSource

	userLocale := msg.Header(AcceptLanguageHeaderKey)
	userInfo.Locale = locale.Locale(userLocale)
	ctx = PutUserDataIntoContext(ctx, &userInfo)

	requestID := msg.RequestID()
	ctx = SetSubRequestID(ctx, requestID)

	return ctx
}

func GetEventsSublogerWithOptions(ctx context.Context) *zerolog.Logger {
	l := logs.SubLogger(logs.FromContext(ctx), logs.WithSubRequestID(ctx))
	return &l
}

func ExtractUserPlatformFromContext(ctx context.Context) string {
	userPlatform, ok := ctx.Value(mw.CtxKeyUserPlatform).(string)
	if ok {
		return userPlatform
	}

	return ""
}

func ExtractUserIPFromContext(ctx context.Context) string {
	userIP, ok := ctx.Value(mw.CtxKeyRealIP).(string)
	if ok {
		return userIP
	}

	return ""
}

func ExtractUserAgentFromContext(ctx context.Context) string {
	userAgent, ok := ctx.Value(mw.CtxKeyUserAgent).(string)
	if ok {
		return userAgent
	}

	return ""
}

// ConvertBoolToPtr - конвертирует bool в указатель на bool
func ConvertBoolToPtr(b bool) *bool {
	return &b
}

func ConvertPtrToBool(b *bool) bool {
	if b == nil {
		return false
	}

	return *b
}

func ParseStringToDate(layouts []string, s string) (time.Time, error) {
	for _, layout := range layouts {
		t, err := time.Parse(layout, s)
		if err == nil {
			return t, nil
		}
	}

	return time.Time{}, errors.New("failed to parse date")
}

// CopyContextValues копирует все значения из исходного контекста в целевой контекст
// Полезно когда errgroup.WithContext не наследует значения, только отмену/таймаут
func CopyContextValues(source, target context.Context) context.Context {
	// Копируем все известные ключи из исходного контекста

	// Копируем userInfo если есть
	if userInfo := source.Value(UserInfoKey); userInfo != nil {
		target = context.WithValue(target, UserInfoKey, userInfo) //nolint:staticcheck
	}

	// Копируем reqSource (origin) если есть
	if reqSource := source.Value(ReqSourceKey); reqSource != nil {
		target = context.WithValue(target, ReqSourceKey, reqSource) //nolint:staticcheck
	}

	// Копируем acceptLanguage если есть
	if acceptLang := source.Value(acceptlanguage.AcceptLanguageKey); acceptLang != nil {
		target = context.WithValue(target, acceptlanguage.AcceptLanguageKey, acceptLang)
	}

	// Копируем logs ключи если есть
	if subReqID := source.Value(logs.SubRequestIDKey); subReqID != nil {
		target = context.WithValue(target, logs.SubRequestIDKey, subReqID)
	}

	// Копируем middleware ключи если есть
	if requestID := source.Value(middleware.RequestIDKey); requestID != nil {
		target = context.WithValue(target, middleware.RequestIDKey, requestID)
	}

	// Копируем httpx middleware ключи если есть
	if userAgent := source.Value(mw.CtxKeyUserAgent); userAgent != nil {
		target = context.WithValue(target, mw.CtxKeyUserAgent, userAgent)
	}

	if realIP := source.Value(mw.CtxKeyRealIP); realIP != nil {
		target = context.WithValue(target, mw.CtxKeyRealIP, realIP)
	}

	if userPlatform := source.Value(mw.CtxKeyUserPlatform); userPlatform != nil {
		target = context.WithValue(target, mw.CtxKeyUserPlatform, userPlatform)
	}

	return target
}

// LoggerWithOperationID - добавляет переданный operationID в логгер
// Если operationID совпадает с именем функции, то добавляется только operationID
// Иначе добавляется operationID и имя функции
func LoggerWithOperationID(baseLogger zerolog.Logger, operationID string) zerolog.Logger {
	pc, _, _, _ := runtime.Caller(1) //nolint:dogsled
	fullFuncName := runtime.FuncForPC(pc).Name()
	parts := strings.Split(fullFuncName, ".")
	methodName := parts[len(parts)-1]

	var opID string

	if methodName == operationID {
		opID = operationID
	} else {
		opID = operationID + "." + methodName
	}

	return baseLogger.With().
		Str("operation_id", opID).
		Logger()
}

func AbsSecondsBetween(t1, t2 time.Time) int32 {
	duration := t1.Sub(t2).Seconds()
	return int32(math.Abs(duration))
}

// FormatIPName форматирует наименование ИП, добавляя "ИП" перед именем, если его нет
func FormatIPName(name string) string {
	if strings.HasPrefix(name, IPPrefix+" ") {
		return name
	}
	return fmt.Sprintf("%s %s", IPPrefix, name)
}

func ConvertStringToPtr(s string) *string {
	return &s
}
