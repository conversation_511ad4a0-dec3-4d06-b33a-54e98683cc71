package utils

import (
	"testing"
	"time"

	"github.com/stretchr/testify/require"
)

func TestGetDayOnlyTimeFromColvirDateString(t *testing.T) {
	cases := []struct {
		name string
		date string
	}{
		{
			name: "date time format colvir",
			date: "2025-04-11T00:00:00",
		},
		{
			name: "date time format colvir with time",
			date: "2025-04-11T12:23:35",
		},
		{
			name: "only date format colvir",
			date: "2025-04-11",
		},
	}

	expTime, err := time.Parse(dayOnlyFormat, "2025-04-11")
	require.NoError(t, err)

	for _, c := range cases {
		t.Run(c.name, func(t *testing.T) {
			resTime, err := GetDayOnlyTimeFromColvirDateString(c.date)
			require.NoError(t, err)

			require.Equal(t, expTime, resTime)
		})
	}
}

func TestParseDate(t *testing.T) {
	t.<PERSON>l()

	tests := []struct {
		name        string
		input       string
		expected    time.Time
		shouldParse bool
	}{
		// Тесты с миллисекундами
		{
			name:        "Parse date with milliseconds and dots",
			input:       "2024.03.14 15:30:45.123",
			expected:    time.Date(2024, 3, 14, 15, 30, 45, 123000000, time.UTC),
			shouldParse: true,
		},
		{
			name:        "Parse date with milliseconds and dashes",
			input:       "14-03-2024 15:30:45.123",
			expected:    time.Date(2024, 3, 14, 15, 30, 45, 123000000, time.UTC),
			shouldParse: true,
		},

		// Тесты с часовым поясом (ISO 8601)
		{
			name:        "Parse ISO 8601 with timezone",
			input:       "2024-03-14T15:30:45-07:00",
			expected:    time.Date(2024, 3, 14, 15, 30, 45, 0, time.FixedZone("", -7*3600)),
			shouldParse: true,
		},
		{
			name:        "Parse ISO 8601 with Z",
			input:       "2024-03-14T15:30:45Z",
			expected:    time.Date(2024, 3, 14, 15, 30, 45, 0, time.UTC),
			shouldParse: true,
		},

		// Тесты с временем
		{
			name:        "Parse date with time and dots",
			input:       "2024.03.14 15:30:45",
			expected:    time.Date(2024, 3, 14, 15, 30, 45, 0, time.UTC),
			shouldParse: true,
		},
		{
			name:        "Parse date with time and dashes",
			input:       "14-03-2024 15:30:45",
			expected:    time.Date(2024, 3, 14, 15, 30, 45, 0, time.UTC),
			shouldParse: true,
		},
		{
			name:        "Parse date with time and slashes",
			input:       "14/03/2024 15:30:45",
			expected:    time.Date(2024, 3, 14, 15, 30, 45, 0, time.UTC),
			shouldParse: true,
		},

		// Тесты только с датой
		{
			name:        "Parse date with dots",
			input:       "2024.03.14",
			expected:    time.Date(2024, 3, 14, 0, 0, 0, 0, time.UTC),
			shouldParse: true,
		},
		{
			name:        "Parse date with dashes",
			input:       "2024-03-14",
			expected:    time.Date(2024, 3, 14, 0, 0, 0, 0, time.UTC),
			shouldParse: true,
		},
		{
			name:        "Parse date with slashes",
			input:       "2024/03/14",
			expected:    time.Date(2024, 3, 14, 0, 0, 0, 0, time.UTC),
			shouldParse: true,
		},
		{
			name:        "Parse date without separators",
			input:       "20240314",
			expected:    time.Date(2024, 3, 14, 0, 0, 0, 0, time.UTC),
			shouldParse: true,
		},

		// Тесты с DD.MM.YYYY форматом
		{
			name:        "Parse DD.MM.YYYY with dots",
			input:       "14.03.2024",
			expected:    time.Date(2024, 3, 14, 0, 0, 0, 0, time.UTC),
			shouldParse: true,
		},
		{
			name:        "Parse DD.MM.YYYY with dashes",
			input:       "14-03-2024",
			expected:    time.Date(2024, 3, 14, 0, 0, 0, 0, time.UTC),
			shouldParse: true,
		},
		{
			name:        "Parse DD.MM.YYYY with slashes",
			input:       "14/03/2024",
			expected:    time.Date(2024, 3, 14, 0, 0, 0, 0, time.UTC),
			shouldParse: true,
		},

		// Тесты с US форматом (MM/DD/YYYY)
		{
			name:        "Parse US format with dots",
			input:       "03.14.2024",
			expected:    time.Date(2024, 3, 14, 0, 0, 0, 0, time.UTC),
			shouldParse: true,
		},
		{
			name:        "Parse US format with dashes",
			input:       "03-14-2024",
			expected:    time.Date(2024, 3, 14, 0, 0, 0, 0, time.UTC),
			shouldParse: true,
		},
		{
			name:        "Parse US format with slashes",
			input:       "03/14/2024",
			expected:    time.Date(2024, 3, 14, 0, 0, 0, 0, time.UTC),
			shouldParse: true,
		},

		// Негативные тесты
		{
			name:        "Empty string",
			input:       "",
			expected:    time.Time{},
			shouldParse: false,
		},
		{
			name:        "Invalid date format",
			input:       "2024/13/45", // Несуществующая дата
			expected:    time.Time{},
			shouldParse: false,
		},
		{
			name:        "Invalid date string",
			input:       "not a date",
			expected:    time.Time{},
			shouldParse: false,
		},
		{
			name:        "Invalid date components",
			input:       "2024.03.14.15", // Лишний компонент
			expected:    time.Time{},
			shouldParse: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, ok := ParseDate(tt.input)
			if ok != tt.shouldParse {
				t.Errorf("ParseDate(%q) parse success = %v, want %v", tt.input, ok, tt.shouldParse)
				return
			}
			if ok && !result.Equal(tt.expected) {
				t.Errorf("ParseDate(%q) = %v, want %v", tt.input, result, tt.expected)
			}
		})
	}
}
