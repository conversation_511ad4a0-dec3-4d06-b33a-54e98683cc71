package fdate

import (
	"fmt"
	"time"
)

var (
	mountNameKz = map[time.Month]string{
		time.January:   "қаңтар",
		time.February:  "ақпан",
		time.March:     "наурыз",
		time.April:     "сәуір",
		time.May:       "мамыр",
		time.June:      "маусым",
		time.July:      "шілде",
		time.August:    "тамыз",
		time.September: "қыркүйек",
		time.October:   "қазан",
		time.November:  "қараша",
		time.December:  "желтоқсан",
	}

	mountNameRu = map[time.Month]string{
		time.January:   "января",
		time.February:  "февраля",
		time.March:     "марта",
		time.April:     "апреля",
		time.May:       "мая",
		time.June:      "июня",
		time.July:      "июля",
		time.August:    "августа",
		time.September: "сентября",
		time.October:   "октября",
		time.November:  "ноября",
		time.December:  "декабря",
	}
)

// FormatDateToGost - форматирование даты по стандарту ГОСТ Р 7.0.97-2016
func FormatDateToGost(t time.Time, lang string) string {
	mountName := mountNameRu[t.Month()]
	yearFirstLetter := "г"
	if lang == "kk" {
		mountName = mountNameKz[t.Month()]
		yearFirstLetter = "ж"
	}

	return fmt.Sprintf("%d %s %d %s.", t.Day(), mountName, t.Year(), yearFirstLetter)
}
