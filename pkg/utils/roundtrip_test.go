package utils

import (
	"context"
	"encoding/base64"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/google/uuid"
)

func TestUserIDTransport(t *testing.T) {
	tests := []struct {
		name               string
		enableUserIDHeader bool
		wantUserIDHeader   bool
	}{
		{
			name:               "with user_id header enabled",
			enableUserIDHeader: true,
			wantUserIDHeader:   true,
		},
		{
			name:               "with user_id header disabled",
			enableUserIDHeader: false,
			wantUserIDHeader:   false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create a test server that checks the headers
			server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
				hasUserID := r.Header.Get("user_id") != ""
				if hasUserID != tt.wantUserIDHeader {
					t.Errorf("user_id header presence = %v, want %v", hasUserID, tt.wantUserIDHeader)
				}
				w.<PERSON><PERSON><PERSON>(http.StatusOK)
			}))
			defer server.Close()

			// Create client with our transport
			client := CreateHTTPClientWithUserIDTransport(nil, tt.enableUserIDHeader)
			ctx, cancel := context.WithTimeout(context.Background(), 2+time.Second)
			defer cancel()

			userID := uuid.New()
			ctx = PutUserDataIntoContext(ctx, &UserInfoCtx{
				UserID: userID,
			})

			// Make a request
			req, err := http.NewRequestWithContext(ctx, "GET", server.URL, nil)
			if err != nil {
				t.Fatalf("Failed to create request: %v", err)
			}

			resp, err := client.Do(req)
			if err != nil {
				t.Fatalf("Failed to do request: %v", err)
			}
			defer resp.Body.Close()

			if resp.StatusCode != http.StatusOK {
				t.Errorf("Response status = %v, want %v", resp.StatusCode, http.StatusOK)
			}
		})
	}
}

func TestNewUserIDTransport(t *testing.T) {
	transport := NewUserIDTransport(http.DefaultTransport, true)

	if transport.Base != http.DefaultTransport {
		t.Error("Expected Base to be http.DefaultTransport")
	}

	if !transport.EnableUserIDHeader {
		t.Error("Expected EnableUserIDHeader to be true")
	}
}

func TestCreateTLSTransport(t *testing.T) {
	// Реальный валидный тестовый сертификат
	testCertPEM := `-----BEGIN CERTIFICATE-----
MIIDkTCCAnmgAwIBAgIUF9pvDZafDR09BzbJoVG0nmyDFGgwDQYJKoZIhvcNAQEL
BQAwWDELMAkGA1UEBhMCVVMxEjAQBgNVBAgMCVRlc3RTdGF0ZTERMA8GA1UEBwwI
VGVzdENpdHkxEDAOBgNVBAoMB1Rlc3RPcmcxEDAOBgNVBAMMB1Rlc3QgQ0EwHhcN
MjUwNTI5MTQ0MDE4WhcNMjYwNTI5MTQ0MDE4WjBYMQswCQYDVQQGEwJVUzESMBAG
A1UECAwJVGVzdFN0YXRlMREwDwYDVQQHDAhUZXN0Q2l0eTEQMA4GA1UECgwHVGVz
dE9yZzEQMA4GA1UEAwwHVGVzdCBDQTCCASIwDQYJKoZIhvcNAQEBBQADggEPADCC
AQoCggEBALZFaPWuAcgRsTNgzo+sUEYiBMvwi4LNSmPwLK9XHyoSUDAsLDznV2C/
h+juBd65aMGvul93S1Sc5/+7hcpvosd5D0bv5S9IfZes2H1xCQ8Qzuwm6ZN8GM0d
EAR3Id5G6A829hNmYVraYZi8kvX11KLLTOQQwaH6oGWmIuilzAnwB89ydwc+my81
8591DGhd0VPQW7BDZ4DL6/G0o89ZbqCmZ6CyFn30TaMTQM9zrH/7en7AGNuaXooH
zoQ9gtLeQj35mk0/bxNhtc9xnKUm9Jg2aP+10zMoUv5O3Z+XhPWBl8JxG8GRMBti
NZ34i764rPb39tRmdEkq3GeO8C9lQJsCAwEAAaNTMFEwHQYDVR0OBBYEFLg1poFU
dTpHbsqcUcEC0OsMblChMB8GA1UdIwQYMBaAFLg1poFUdTpHbsqcUcEC0OsMblCh
MA8GA1UdEwEB/wQFMAMBAf8wDQYJKoZIhvcNAQELBQADggEBAFL1pqcKWAnTU3I1
G328aSFjg64ATq8lH8/LlDaX74JMo3ZBy8uxLVJOpt3KrvpLsI1+U+KburEJijME
zo4BjTRa7vZJtv/Ytqf4SdVObZKdwpS+Nq1rjE3xjeSO3wzWVDNi2uQBcFZXIqf3
h9ddtPcnJBACQaeJVJaPtPzuVFJnsxXfeHuMjLfyLJfIZjvICDIRIKnp9bUShOT2
EUeBipd8HMOl2RasMszDctvu2qDU6h2+fP1k3JWRpQbe5lsGNZhTAqzh5bNoM1ER
VNMebJk5zyhVCJyD8LeMX2qhJrN7j2cN93/AMD9PVH6AicMCWPhYT2S41fJTXZpg
+zZ4QhI=
-----END CERTIFICATE-----`

	testCertBase64 := base64.StdEncoding.EncodeToString([]byte(testCertPEM))

	tests := []struct {
		name             string
		customCertBase64 string
		expectInsecure   bool
		description      string
	}{
		{
			name:             "with valid custom certificate",
			customCertBase64: testCertBase64,
			expectInsecure:   false, // валидный сертификат должен работать без insecure
			description:      "валидный сертификат должен быть добавлен в пул",
		},
		{
			name:             "with invalid base64 certificate",
			customCertBase64: "invalid-base64!!!",
			expectInsecure:   true, // должен fallback на insecure
			description:      "некорректный base64 должен привести к insecure",
		},
		{
			name:             "with empty certificate",
			customCertBase64: "",
			expectInsecure:   true, // автоматически insecure
			description:      "пустой сертификат должен привести к insecure",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			transport := CreateTLSTransport(tt.customCertBase64)

			// Проверяем что это http.Transport
			httpTransport, ok := transport.(*http.Transport)
			if !ok {
				t.Fatal("Expected *http.Transport")
			}

			// Проверяем TLS конфигурацию
			if httpTransport.TLSClientConfig == nil {
				if tt.customCertBase64 != "" {
					t.Error("Expected TLS config to be set when certificate provided")
				}
				return
			}

			tlsConfig := httpTransport.TLSClientConfig
			if tlsConfig.InsecureSkipVerify != tt.expectInsecure {
				t.Errorf("InsecureSkipVerify = %v, want %v (%s)",
					tlsConfig.InsecureSkipVerify, tt.expectInsecure, tt.description)
			}

			// Логируем для отладки
			t.Logf("Test: %s, InsecureSkipVerify: %v, RootCAs: %v",
				tt.name, tlsConfig.InsecureSkipVerify, tlsConfig.RootCAs != nil)

			// Если ожидаем кастомный сертификат, проверяем что RootCAs установлен
			if tt.customCertBase64 != "" && !tt.expectInsecure {
				if tlsConfig.RootCAs == nil {
					t.Error("Expected RootCAs to be set when using custom certificate")
				}
			}
		})
	}
}

func TestCreateTLSTransportIntegration(t *testing.T) {
	// Интеграционный тест с реальным HTTPS сервером
	server := httptest.NewTLSServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		w.Write([]byte("OK"))
	}))
	defer server.Close()

	tests := []struct {
		name          string
		certBase64    string
		expectSuccess bool
	}{
		{
			name:          "empty cert should use insecure mode and work",
			certBase64:    "",
			expectSuccess: true, // insecure mode должен работать с self-signed
		},
		{
			name:          "invalid cert should fallback to insecure and work",
			certBase64:    "invalid-cert",
			expectSuccess: true, // fallback на insecure
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			transport := CreateTLSTransport(tt.certBase64)
			client := &http.Client{
				Transport: transport,
				Timeout:   5 * time.Second,
			}

			resp, err := client.Get(server.URL)

			if tt.expectSuccess {
				if err != nil {
					t.Errorf("Expected success but got error: %v", err)
				} else {
					resp.Body.Close()
				}
			} else {
				if err == nil {
					resp.Body.Close()
					t.Error("Expected error but request succeeded")
				}
			}
		})
	}
}

func TestCreateHTTPClientWithCertAndUserID(t *testing.T) {
	// Тест deprecated функции для обратной совместимости
	testCertPEM := `-----BEGIN CERTIFICATE-----
MIIDkTCCAnmgAwIBAgIUF9pvDZafDR09BzbJoVG0nmyDFGgwDQYJKoZIhvcNAQEL
BQAwWDELMAkGA1UEBhMCVVMxEjAQBgNVBAgMCVRlc3RTdGF0ZTERMA8GA1UEBwwI
VGVzdENpdHkxEDAOBgNVBAoMB1Rlc3RPcmcxEDAOBgNVBAMMB1Rlc3QgQ0EwHhcN
MjUwNTI5MTQ0MDE4WhcNMjYwNTI5MTQ0MDE4WjBYMQswCQYDVQQGEwJVUzESMBAG
A1UECAwJVGVzdFN0YXRlMREwDwYDVQQHDAhUZXN0Q2l0eTEQMA4GA1UECgwHVGVz
dE9yZzEQMA4GA1UEAwwHVGVzdCBDQTCCASIwDQYJKoZIhvcNAQEBBQADggEPADCC
AQoCggEBALZFaPWuAcgRsTNgzo+sUEYiBMvwi4LNSmPwLK9XHyoSUDAsLDznV2C/
h+juBd65aMGvul93S1Sc5/+7hcpvosd5D0bv5S9IfZes2H1xCQ8Qzuwm6ZN8GM0d
EAR3Id5G6A829hNmYVraYZi8kvX11KLLTOQQwaH6oGWmIuilzAnwB89ydwc+my81
8591DGhd0VPQW7BDZ4DL6/G0o89ZbqCmZ6CyFn30TaMTQM9zrH/7en7AGNuaXooH
zoQ9gtLeQj35mk0/bxNhtc9xnKUm9Jg2aP+10zMoUv5O3Z+XhPWBl8JxG8GRMBti
NZ34i764rPb39tRmdEkq3GeO8C9lQJsCAwEAAaNTMFEwHQYDVR0OBBYEFLg1poFU
dTpHbsqcUcEC0OsMblChMB8GA1UdIwQYMBaAFLg1poFUdTpHbsqcUcEC0OsMblCh
MA8GA1UdEwEB/wQFMAMBAf8wDQYJKoZIhvcNAQELBQADggEBAFL1pqcKWAnTU3I1
G328aSFjg64ATq8lH8/LlDaX74JMo3ZBy8uxLVJOpt3KrvpLsI1+U+KburEJijME
zo4BjTRa7vZJtv/Ytqf4SdVObZKdwpS+Nq1rjE3xjeSO3wzWVDNi2uQBcFZXIqf3
h9ddtPcnJBACQaeJVJaPtPzuVFJnsxXfeHuMjLfyLJfIZjvICDIRIKnp9bUShOT2
EUeBipd8HMOl2RasMszDctvu2qDU6h2+fP1k3JWRpQbe5lsGNZhTAqzh5bNoM1ER
VNMebJk5zyhVCJyD8LeMX2qhJrN7j2cN93/AMD9PVH6AicMCWPhYT2S41fJTXZpg
+zZ4QhI=
-----END CERTIFICATE-----`

	testCertBase64 := base64.StdEncoding.EncodeToString([]byte(testCertPEM))

	// Создаем тестовый сервер
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		hasUserID := r.Header.Get("user_id") != ""
		if !hasUserID {
			t.Error("Expected user_id header")
		}
		w.WriteHeader(http.StatusOK)
	}))
	defer server.Close()

	// Тестируем deprecated функцию
	client := CreateHTTPClientWithCertAndUserID(testCertBase64, true)

	// Проверяем что клиент создался
	if client == nil {
		t.Fatal("Expected client to be created")
	}

	// Проверяем что transport настроен правильно
	userIDTransport, ok := client.Transport.(*UserIDTransport)
	if !ok {
		t.Fatal("Expected UserIDTransport")
	}

	if !userIDTransport.EnableUserIDHeader {
		t.Error("Expected EnableUserIDHeader to be true")
	}

	// Проверяем что базовый transport - это наш TLS transport
	httpTransport, ok := userIDTransport.Base.(*http.Transport)
	if !ok {
		t.Fatal("Expected base transport to be *http.Transport")
	}

	if httpTransport.TLSClientConfig == nil {
		t.Error("Expected TLS config to be set")
	}
}

func TestChainedTransportFunctionality(t *testing.T) {
	// Тест цепочки функций: TLS + UserID
	testCertPEM := `-----BEGIN CERTIFICATE-----
MIIDkTCCAnmgAwIBAgIUF9pvDZafDR09BzbJoVG0nmyDFGgwDQYJKoZIhvcNAQEL
BQAwWDELMAkGA1UEBhMCVVMxEjAQBgNVBAgMCVRlc3RTdGF0ZTERMA8GA1UEBwwI
VGVzdENpdHkxEDAOBgNVBAoMB1Rlc3RPcmcxEDAOBgNVBAMMB1Rlc3QgQ0EwHhcN
MjUwNTI5MTQ0MDE4WhcNMjYwNTI5MTQ0MDE4WjBYMQswCQYDVQQGEwJVUzESMBAG
A1UECAwJVGVzdFN0YXRlMREwDwYDVQQHDAhUZXN0Q2l0eTEQMA4GA1UECgwHVGVz
dE9yZzEQMA4GA1UEAwwHVGVzdCBDQTCCASIwDQYJKoZIhvcNAQEBBQADggEPADCC
AQoCggEBALZFaPWuAcgRsTNgzo+sUEYiBMvwi4LNSmPwLK9XHyoSUDAsLDznV2C/
h+juBd65aMGvul93S1Sc5/+7hcpvosd5D0bv5S9IfZes2H1xCQ8Qzuwm6ZN8GM0d
EAR3Id5G6A829hNmYVraYZi8kvX11KLLTOQQwaH6oGWmIuilzAnwB89ydwc+my81
8591DGhd0VPQW7BDZ4DL6/G0o89ZbqCmZ6CyFn30TaMTQM9zrH/7en7AGNuaXooH
zoQ9gtLeQj35mk0/bxNhtc9xnKUm9Jg2aP+10zMoUv5O3Z+XhPWBl8JxG8GRMBti
NZ34i764rPb39tRmdEkq3GeO8C9lQJsCAwEAAaNTMFEwHQYDVR0OBBYEFLg1poFU
dTpHbsqcUcEC0OsMblChMB8GA1UdIwQYMBaAFLg1poFUdTpHbsqcUcEC0OsMblCh
MA8GA1UdEwEB/wQFMAMBAf8wDQYJKoZIhvcNAQELBQADggEBAFL1pqcKWAnTU3I1
G328aSFjg64ATq8lH8/LlDaX74JMo3ZBy8uxLVJOpt3KrvpLsI1+U+KburEJijME
zo4BjTRa7vZJtv/Ytqf4SdVObZKdwpS+Nq1rjE3xjeSO3wzWVDNi2uQBcFZXIqf3
h9ddtPcnJBACQaeJVJaPtPzuVFJnsxXfeHuMjLfyLJfIZjvICDIRIKnp9bUShOT2
EUeBipd8HMOl2RasMszDctvu2qDU6h2+fP1k3JWRpQbe5lsGNZhTAqzh5bNoM1ER
VNMebJk5zyhVCJyD8LeMX2qhJrN7j2cN93/AMD9PVH6AicMCWPhYT2S41fJTXZpg
+zZ4QhI=
-----END CERTIFICATE-----`

	testCertBase64 := base64.StdEncoding.EncodeToString([]byte(testCertPEM))

	// Создаем тестовый сервер который проверяет headers
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		userID := r.Header.Get("user_id")
		if userID == "" {
			t.Error("Expected user_id header to be set")
		}
		w.WriteHeader(http.StatusOK)
		w.Write([]byte("OK"))
	}))
	defer server.Close()

	// Тестируем цепочку функций
	tlsTransport := CreateTLSTransport(testCertBase64)
	client := CreateHTTPClientWithUserIDTransport(tlsTransport, true)

	// Создаем контекст с user data
	ctx := context.Background()
	userID := uuid.New()
	ctx = PutUserDataIntoContext(ctx, &UserInfoCtx{
		UserID: userID,
	})

	// Выполняем запрос
	req, err := http.NewRequestWithContext(ctx, "GET", server.URL, nil)
	if err != nil {
		t.Fatalf("Failed to create request: %v", err)
	}

	resp, err := client.Do(req)
	if err != nil {
		t.Fatalf("Failed to do request: %v", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		t.Errorf("Expected status 200, got %d", resp.StatusCode)
	}
}
