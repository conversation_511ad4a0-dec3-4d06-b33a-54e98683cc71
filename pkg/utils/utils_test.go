package utils

import (
	"context"
	"testing"
	"time"

	"github.com/go-chi/chi/v5/middleware"
	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"

	"git.redmadrobot.com/backend-go/rmr-pkg/core/logs"
	"git.redmadrobot.com/backend-go/rmr-pkg/tools/acceptlanguage"

	"git.redmadrobot.com/zaman/backend/zaman/pkg/helpers/locale"
)

// MockMessage is a mock implementation for testing
type MockMessage struct {
	mock.Mock
}

func (m *MockMessage) Header(key string) string {
	args := m.Called(key)
	return args.String(0)
}

func (m *MockMessage) RequestID() string {
	args := m.Called()
	return args.String(0)
}

func testPutInfoFromMsgHeadersIntoCtx(ctx context.Context, msg *MockMessage) context.Context {
	var userInfo UserInfoCtx
	userID := msg.Header(UserIDHeaderKey)

	parsedUserID, err := uuid.Parse(userID)
	if err == nil {
		userInfo.UserID = parsedUserID
	} else {
		logs.FromContext(ctx).Warn().Msg("failed to parse userID from kafka msg header")
	}

	sessionID := msg.Header(SessionIDHeaderKey)
	parsedSessionID, err := uuid.Parse(sessionID)
	if err == nil {
		userInfo.SessionID = parsedSessionID
	} else {
		logs.FromContext(ctx).Warn().Msg("failed to parse sessionID from kafka msg header")
	}

	phoneNumber := msg.Header(PhoneNumberHeaderKey)
	userInfo.Phone = phoneNumber

	reqSource := msg.Header(ReqSourceKey)
	userInfo.Origin = reqSource

	userLocale := msg.Header(AcceptLanguageHeaderKey)
	userInfo.Locale = locale.Locale(userLocale)
	ctx = PutUserDataIntoContext(ctx, &userInfo)

	requestID := msg.RequestID()
	ctx = SetRequestID(ctx, requestID)

	return ctx
}

func TestPutInfoFromMsgHeadersIntoCtx(t *testing.T) {
	t.Run("with valid message headers", func(t *testing.T) {
		// Arrange
		ctx := context.Background()
		mockMsg := new(MockMessage)

		userID := uuid.New().String()
		sessionID := uuid.New().String()
		phone := "+77771234567"
		reqSource := MobileApp
		userLocale := locale.Ru.String()
		requestID := "test-request-id"

		mockMsg.On("Header", UserIDHeaderKey).Return(userID)
		mockMsg.On("Header", SessionIDHeaderKey).Return(sessionID)
		mockMsg.On("Header", PhoneNumberHeaderKey).Return(phone)
		mockMsg.On("Header", ReqSourceKey).Return(reqSource)
		mockMsg.On("Header", AcceptLanguageHeaderKey).Return(userLocale)
		mockMsg.On("RequestID").Return(requestID)

		// Act
		resultCtx := testPutInfoFromMsgHeadersIntoCtx(ctx, mockMsg)

		// Assert
		mockMsg.AssertExpectations(t)

		// Check if userInfo was added to context
		userInfo, ok := resultCtx.Value(UserInfoKey).(map[string]interface{})
		assert.True(t, ok, "userInfo should be a map[string]interface{}")
		assert.Equal(t, userID, userInfo["user_id"], "user_id should match")
		assert.Equal(t, sessionID, userInfo["session_id"], "session_id should match")
		assert.Equal(t, phone, userInfo["phone_number"], "phone_number should match")

		// Check if requestID was set in context
		ctxRequestID := resultCtx.Value(middleware.RequestIDKey)
		assert.Equal(t, requestID, ctxRequestID, "RequestID should match")

		// Check origin
		origin, err := GetOrigin(resultCtx)
		assert.NoError(t, err, "GetOrigin should not return an error")
		assert.Equal(t, UserOriginMobile, origin, "Origin should match")

		// Check locale
		ctxLocale, err := GetLocaleFromContext(resultCtx, false, false)
		assert.NoError(t, err, "GetLocaleFromContext should not return an error")
		assert.Equal(t, locale.Ru, ctxLocale, "Locale should match")
	})

	t.Run("with invalid UUID formats", func(t *testing.T) {
		// Arrange
		ctx := context.Background()
		mockMsg := new(MockMessage)

		mockMsg.On("Header", UserIDHeaderKey).Return("invalid-uuid")
		mockMsg.On("Header", SessionIDHeaderKey).Return("invalid-uuid")
		mockMsg.On("Header", PhoneNumberHeaderKey).Return("+77771234567")
		mockMsg.On("Header", ReqSourceKey).Return(MobileApp)
		mockMsg.On("Header", AcceptLanguageHeaderKey).Return(locale.Ru.String())
		mockMsg.On("RequestID").Return("test-request-id")

		// Act
		resultCtx := testPutInfoFromMsgHeadersIntoCtx(ctx, mockMsg)

		// Assert
		mockMsg.AssertExpectations(t)

		// Check if userInfo was added to context with nil UUIDs
		userInfo, ok := resultCtx.Value(UserInfoKey).(map[string]interface{})
		assert.True(t, ok, "userInfo should be a map[string]interface{}")

		// The UUIDs should be empty strings since parsing failed
		assert.Equal(t, uuid.Nil.String(), userInfo["user_id"], "user_id should be nil UUID string")
		assert.Equal(t, uuid.Nil.String(), userInfo["session_id"], "session_id should be nil UUID string")
		assert.Equal(t, "+77771234567", userInfo["phone_number"], "phone_number should match")
	})

	t.Run("with missing headers", func(t *testing.T) {
		// Arrange
		ctx := context.Background()
		mockMsg := new(MockMessage)

		mockMsg.On("Header", UserIDHeaderKey).Return("")
		mockMsg.On("Header", SessionIDHeaderKey).Return("")
		mockMsg.On("Header", PhoneNumberHeaderKey).Return("")
		mockMsg.On("Header", ReqSourceKey).Return("")
		mockMsg.On("Header", AcceptLanguageHeaderKey).Return("")
		mockMsg.On("RequestID").Return("")

		// Act
		resultCtx := testPutInfoFromMsgHeadersIntoCtx(ctx, mockMsg)

		// Assert
		mockMsg.AssertExpectations(t)

		// Check if userInfo was added to context with default values
		userInfo, ok := resultCtx.Value(UserInfoKey).(map[string]interface{})
		assert.True(t, ok, "userInfo should be a map[string]interface{}")

		// The UUIDs should be nil UUIDs since parsing failed
		assert.Equal(t, uuid.Nil.String(), userInfo["user_id"], "user_id should be nil UUID string")
		assert.Equal(t, uuid.Nil.String(), userInfo["session_id"], "session_id should be nil UUID string")
		assert.Equal(t, "", userInfo["phone_number"], "phone_number should be empty")

		// Check if requestID was set in context
		ctxRequestID := resultCtx.Value(middleware.RequestIDKey)
		assert.Equal(t, "", ctxRequestID, "RequestID should be empty")
	})
}

func TestPutUserDataIntoContext(t *testing.T) {
	t.Run("with nil userData", func(t *testing.T) {
		// Arrange
		ctx := context.Background()

		// Act
		resultCtx := PutUserDataIntoContext(ctx, nil)

		// Assert
		assert.Equal(t, ctx, resultCtx, "Context should remain unchanged when userData is nil")
	})

	t.Run("with valid userData", func(t *testing.T) {
		// Arrange
		ctx := context.Background()
		userID := uuid.New()
		sessionID := uuid.New()
		userData := &UserInfoCtx{
			UserID:    userID,
			SessionID: sessionID,
			Phone:     "+77771234567",
			Origin:    UserOriginMobile,
			Locale:    locale.Ru,
		}

		// Act
		resultCtx := PutUserDataIntoContext(ctx, userData)

		// Assert
		userInfo, ok := resultCtx.Value(UserInfoKey).(map[string]interface{})
		assert.True(t, ok, "userInfo should be a map[string]interface{}")
		assert.Equal(t, userID.String(), userInfo["user_id"], "user_id should match")
		assert.Equal(t, sessionID.String(), userInfo["session_id"], "session_id should match")
		assert.Equal(t, "+77771234567", userInfo["phone_number"], "phone_number should match")

		// Check origin
		origin, err := GetOrigin(resultCtx)
		assert.NoError(t, err, "GetOrigin should not return an error")
		assert.Equal(t, UserOriginMobile, origin, "Origin should match")

		// Check locale
		userLocale, err := GetLocaleFromContext(resultCtx, false, false)
		assert.NoError(t, err, "GetLocaleFromContext should not return an error")
		assert.Equal(t, locale.Ru, userLocale, "Locale should match")
	})

	t.Run("with existing userInfo in context", func(t *testing.T) {
		// Arrange
		existingUserInfo := map[string]interface{}{
			"existing_key": "existing_value",
		}
		ctx := context.WithValue(context.Background(), UserInfoKey, existingUserInfo)

		userID := uuid.New()
		sessionID := uuid.New()
		userData := &UserInfoCtx{
			UserID:    userID,
			SessionID: sessionID,
			Phone:     "+77771234567",
			Origin:    UserOriginSme,
			Locale:    locale.Kk,
		}

		// Act
		resultCtx := PutUserDataIntoContext(ctx, userData)

		// Assert
		userInfo, ok := resultCtx.Value(UserInfoKey).(map[string]interface{})
		assert.True(t, ok, "userInfo should be a map[string]interface{}")
		assert.Equal(t, userID.String(), userInfo["user_id"], "user_id should match")
		assert.Equal(t, sessionID.String(), userInfo["session_id"], "session_id should match")
		assert.Equal(t, "+77771234567", userInfo["phone_number"], "phone_number should match")
		assert.Equal(t, "existing_value", userInfo["existing_key"], "existing key should be preserved")

		// Check origin
		origin, err := GetOrigin(resultCtx)
		assert.NoError(t, err, "GetOrigin should not return an error")
		assert.Equal(t, UserOriginSme, origin, "Origin should match")

		// Check locale
		userLocale, err := GetLocaleFromContext(resultCtx, false, false)
		assert.NoError(t, err, "GetLocaleFromContext should not return an error")
		assert.Equal(t, locale.Kk, userLocale, "Locale should match")
	})
}

func TestTryToExtractUserDataFromContext(t *testing.T) {
	t.Run("with valid context data", func(t *testing.T) {
		// Arrange
		userID := uuid.New()
		sessionID := uuid.New()
		phone := "+77771234567"

		userInfo := map[string]interface{}{
			"user_id":      userID.String(),
			"session_id":   sessionID.String(),
			"phone_number": phone,
		}

		ctx := context.WithValue(context.Background(), UserInfoKey, userInfo)
		ctx = SetOrigin(ctx, MobileApp)
		ctx = context.WithValue(ctx, acceptlanguage.AcceptLanguageKey, locale.Kk.String())

		// Act
		result := TryToExtractUserDataFromContext(ctx)

		// Assert
		assert.NotNil(t, result, "Result should not be nil")
		assert.Equal(t, userID, result.UserID, "UserID should match")
		assert.Equal(t, sessionID, result.SessionID, "SessionID should match")
		assert.Equal(t, phone, result.Phone, "Phone should match")
		assert.Equal(t, UserOriginMobile, result.Origin, "Origin should match")
		assert.Equal(t, locale.Kk, result.Locale, "Locale should match")
	})

	t.Run("with missing userInfo", func(t *testing.T) {
		// Arrange
		ctx := context.Background()

		// Act
		result := TryToExtractUserDataFromContext(ctx)

		// Assert
		assert.NotNil(t, result, "Result should not be nil even with missing userInfo")
		assert.Equal(t, uuid.Nil, result.UserID, "UserID should be nil UUID")
		assert.Equal(t, uuid.Nil, result.SessionID, "SessionID should be nil UUID")
		assert.Equal(t, "", result.Phone, "Phone should be empty")
	})

	t.Run("with invalid UUID formats", func(t *testing.T) {
		// Arrange
		userInfo := map[string]interface{}{
			"user_id":      "invalid-uuid",
			"session_id":   "another-invalid-uuid",
			"phone_number": "+77771234567",
		}

		ctx := context.WithValue(context.Background(), UserInfoKey, userInfo)
		ctx = SetOrigin(ctx, MobileApp)
		ctx = context.WithValue(ctx, acceptlanguage.AcceptLanguageKey, locale.Kk.String())

		// Act
		result := TryToExtractUserDataFromContext(ctx)

		// Assert
		assert.NotNil(t, result, "Result should not be nil")
		assert.Equal(t, uuid.Nil, result.UserID, "UserID should be nil UUID for invalid format")
		assert.Equal(t, uuid.Nil, result.SessionID, "SessionID should be nil UUID for invalid format")
		assert.Equal(t, "+77771234567", result.Phone, "Phone should match")
		assert.Equal(t, UserOriginMobile, result.Origin, "Origin should match")
		assert.Equal(t, locale.Kk, result.Locale, "Locale should match")
	})
}

func TestGetUserDataFromContext(t *testing.T) {
	t.Run("with valid context data", func(t *testing.T) {
		// Arrange
		userID := uuid.New()
		sessionID := uuid.New()
		phone := "+77771234567"

		userInfo := map[string]interface{}{
			"user_id":      userID.String(),
			"session_id":   sessionID.String(),
			"phone_number": phone,
		}

		ctx := context.WithValue(context.Background(), UserInfoKey, userInfo)
		ctx = SetOrigin(ctx, MobileApp)
		ctx = context.WithValue(ctx, acceptlanguage.AcceptLanguageKey, locale.Kk.String())

		// Act
		result, err := GetUserDataFromContext(ctx)

		// Assert
		assert.NoError(t, err, "Should not return an error with valid data")
		assert.NotNil(t, result, "Result should not be nil")
		assert.Equal(t, userID, result.UserID, "UserID should match")
		assert.Equal(t, sessionID, result.SessionID, "SessionID should match")
		assert.Equal(t, phone, result.Phone, "Phone should match")
		assert.Equal(t, UserOriginMobile, result.Origin, "Origin should match")
		assert.Equal(t, locale.Kk, result.Locale, "Locale should match")
	})

	t.Run("with missing userInfo", func(t *testing.T) {
		// Arrange
		ctx := context.Background()

		// Act
		result, err := GetUserDataFromContext(ctx)

		// Assert
		assert.Error(t, err, "Should return an error with missing userInfo")
		assert.Nil(t, result, "Result should be nil")
		assert.Contains(t, err.Error(), "userInfo is not found in context", "Error message should mention missing userInfo")
	})

	t.Run("with missing user_id", func(t *testing.T) {
		// Arrange
		userInfo := map[string]interface{}{
			"session_id":   uuid.New().String(),
			"phone_number": "+77771234567",
		}

		ctx := context.WithValue(context.Background(), UserInfoKey, userInfo)

		// Act
		result, err := GetUserDataFromContext(ctx)

		// Assert
		assert.Error(t, err, "Should return an error with missing user_id")
		assert.Nil(t, result, "Result should be nil")
		assert.Contains(t, err.Error(), "userInfo.user_id is not found in context", "Error message should mention missing user_id")
	})

	t.Run("with invalid user_id format", func(t *testing.T) {
		// Arrange
		userInfo := map[string]interface{}{
			"user_id":      "invalid-uuid",
			"session_id":   uuid.New().String(),
			"phone_number": "+77771234567",
		}

		ctx := context.WithValue(context.Background(), UserInfoKey, userInfo)

		// Act
		result, err := GetUserDataFromContext(ctx)

		// Assert
		assert.Error(t, err, "Should return an error with invalid user_id format")
		assert.Nil(t, result, "Result should be nil")
		assert.Contains(t, err.Error(), "is not a valid UUID", "Error message should mention invalid UUID format")
	})

	t.Run("with missing session_id", func(t *testing.T) {
		// Arrange
		userInfo := map[string]interface{}{
			"user_id":      uuid.New().String(),
			"phone_number": "+77771234567",
		}

		ctx := context.WithValue(context.Background(), UserInfoKey, userInfo)

		// Act
		result, err := GetUserDataFromContext(ctx)

		// Assert
		assert.Error(t, err, "Should return an error with missing session_id")
		assert.Nil(t, result, "Result should be nil")
		assert.Contains(t, err.Error(), "session_id is not found in context", "Error message should mention missing session_id")
	})

	t.Run("with invalid session_id format", func(t *testing.T) {
		// Arrange
		userInfo := map[string]interface{}{
			"user_id":      uuid.New().String(),
			"session_id":   "invalid-uuid",
			"phone_number": "+77771234567",
		}

		ctx := context.WithValue(context.Background(), UserInfoKey, userInfo)

		// Act
		result, err := GetUserDataFromContext(ctx)

		// Assert
		assert.Error(t, err, "Should return an error with invalid session_id format")
		assert.Nil(t, result, "Result should be nil")
		assert.Contains(t, err.Error(), "is not a valid UUID", "Error message should mention invalid UUID format")
	})

	t.Run("with missing phone_number", func(t *testing.T) {
		// Arrange
		userInfo := map[string]interface{}{
			"user_id":    uuid.New().String(),
			"session_id": uuid.New().String(),
		}

		ctx := context.WithValue(context.Background(), UserInfoKey, userInfo)

		// Act
		result, err := GetUserDataFromContext(ctx)

		// Assert
		assert.Error(t, err, "Should return an error with missing phone_number")
		assert.Nil(t, result, "Result should be nil")
	})
}

func TestGetUserLocaleFromContext(t *testing.T) {
	t.Run("with invalid locale context data", func(t *testing.T) {
		ctx := context.WithValue(context.Background(), acceptlanguage.AcceptLanguageKey, "aa")

		// Act
		result, err := GetLocaleFromContext(ctx, true, true)

		// Assert
		assert.NoError(t, err, "Should not return an error")
		assert.NotNil(t, result, "Result should not be nil")
		assert.Equal(t, locale.Kk, result, "Locale should match")
	})
}

func TestAbsSecondsBetween(t *testing.T) {
	tests := []struct {
		name     string
		t1       time.Time
		t2       time.Time
		expected int32
	}{
		{
			name:     "t1 after t2",
			t1:       time.Date(2024, 6, 1, 12, 0, 59, 0, time.UTC),
			t2:       time.Date(2024, 6, 1, 12, 0, 0, 0, time.UTC),
			expected: 59,
		},
		{
			name:     "t2 after t1",
			t1:       time.Date(2024, 6, 1, 12, 0, 0, 0, time.UTC),
			t2:       time.Date(2024, 6, 1, 12, 0, 59, 0, time.UTC),
			expected: 59,
		},
		{
			name:     "equal times",
			t1:       time.Date(2024, 6, 1, 12, 0, 0, 0, time.UTC),
			t2:       time.Date(2024, 6, 1, 12, 0, 0, 0, time.UTC),
			expected: 0,
		},
		{
			name:     "just under a second",
			t1:       time.Date(2024, 6, 1, 12, 0, 1, 0, time.UTC),
			t2:       time.Date(2024, 6, 1, 12, 0, 0, 999_000_000, time.UTC), // 0.001s difference
			expected: 0,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := AbsSecondsBetween(tt.t1, tt.t2)
			if got != tt.expected {
				t.Errorf("AbsSecondsBetween() = %v, want %v", got, tt.expected)
			}
		})
	}
}
