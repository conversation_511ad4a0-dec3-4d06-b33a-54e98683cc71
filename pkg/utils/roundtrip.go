package utils

import (
	"crypto/tls"
	"crypto/x509"
	"encoding/base64"
	"net/http"

	"git.redmadrobot.com/backend-go/rmr-pkg/core/logs"
)

// UserIDTransport is a custom http.RoundTripper that adds user_id header
type UserIDTransport struct {
	Base               http.RoundTripper
	EnableUserIDHeader bool
}

// RoundTrip implements the http.RoundTripper interface
func (t *UserIDTransport) RoundTrip(req *http.Request) (*http.Response, error) {
	// Only add user_id header if enabled
	if t.EnableUserIDHeader {
		logger := logs.FromContext(req.Context())
		logger.Info().Msgf("Adding user_id header to request")
		userID, err := GetUserIDFromContext(req.Context())
		if err != nil {
			logger.Info().Msgf("No user_id found in context")
			req.Header.Set("user_id", "default")
		} else {
			logger.Info().Msgf("Adding user_id header to request: %s", userID)
			req.Header.Set("user_id", userID)
		}
	}

	// Use the base transport, or default if not set
	transport := t.Base
	if transport == nil {
		transport = http.DefaultTransport
	}

	return transport.RoundTrip(req)
}

// NewUserIDTransport creates a new UserIDTransport with the given base transport and enablement flag
func NewUserIDTransport(base http.RoundTripper, enableUserIDHeader bool) *UserIDTransport {
	return &UserIDTransport{
		Base:               base,
		EnableUserIDHeader: enableUserIDHeader,
	}
}

// CreateHTTPClientWithUserIDTransport creates a new http.Client with UserIDTransport
func CreateHTTPClientWithUserIDTransport(base http.RoundTripper, enableUserIDHeader bool) *http.Client {
	originalTransport := base
	if originalTransport == nil {
		originalTransport = http.DefaultTransport
	}

	return &http.Client{
		Transport: NewUserIDTransport(
			originalTransport,
			enableUserIDHeader,
		),
	}
}

// CreateTLSTransport создает transport с настройками SSL сертификатов
func CreateTLSTransport(customCertBase64 string) http.RoundTripper {
	// Создаем базовый transport
	transport := http.DefaultTransport.(*http.Transport).Clone()

	// Настраиваем TLS с безопасными настройками
	tlsConfig := &tls.Config{
		MinVersion: tls.VersionTLS12, // Устанавливаем минимальную версию TLS 1.2
	}

	if customCertBase64 != "" {
		// Загружаем системный пул сертификатов (из ssl_cert_file и других системных источников)
		certPool, err := x509.SystemCertPool()
		if err != nil {
			// Если не удалось загрузить системный пул, создаем пустой
			certPool = x509.NewCertPool()
		}

		// Декодируем кастомный сертификат
		certBytes, err := base64.StdEncoding.DecodeString(customCertBase64)
		if err != nil {
			// Если не удалось декодировать сертификат, используем InsecureSkipVerify
			tlsConfig.InsecureSkipVerify = true // #nosec G402 - fallback для неправильного сертификата
		} else {
			// Добавляем кастомный сертификат к системному пулу
			if certPool.AppendCertsFromPEM(certBytes) {
				tlsConfig.RootCAs = certPool
			} else {
				// Если не удалось добавить сертификат в пул, используем InsecureSkipVerify
				tlsConfig.InsecureSkipVerify = true // #nosec G402 - fallback для неправильного сертификата
			}
		}
	} else {
		// Если сертификата нет, отключаем проверку SSL
		tlsConfig.InsecureSkipVerify = true // #nosec G402 - нет кастомного сертификата
	}

	transport.TLSClientConfig = tlsConfig
	return transport
}

// CreateHTTPClientWithCertAndUserID создает HTTP-клиент с поддержкой сертификатов и UserID транспорта
// Deprecated: используйте CreateTLSTransport и CreateHTTPClientWithUserIDTransport для большей гибкости
func CreateHTTPClientWithCertAndUserID(customCertBase64 string, enableUserIDHeader bool) *http.Client {
	tlsTransport := CreateTLSTransport(customCertBase64)
	return CreateHTTPClientWithUserIDTransport(tlsTransport, enableUserIDHeader)
}
