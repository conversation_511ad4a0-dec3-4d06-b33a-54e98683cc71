package utils

import (
	"bytes"
	"errors"
	"fmt"
	"reflect"

	"github.com/xuri/excelize/v2"
)

func convertToInterfaceSlice(x interface{}) ([]interface{}, error) {
	val := reflect.ValueOf(x)

	if val.Kind() != reflect.Slice {
		return nil, errors.New("input argument must be a slice type")
	}

	result := make([]interface{}, val.Len())
	for i := 0; i < val.Len(); i++ {
		result[i] = val.Index(i).Interface()
	}
	return result, nil
}

func CreateExcelFile(data interface{}) ([]byte, error) {
	dataAr, err := convertToInterfaceSlice(data)
	if err != nil {
		return nil, err
	}

	if len(dataAr) == 0 {
		return nil, nil
	}

	f := excelize.NewFile()
	defer func() {
		if err := f.Close(); err != nil {
			fmt.Println(err)
		}
	}()

	typ := reflect.TypeOf(dataAr[0])

	var columns []string
	for i := 0; i < typ.NumField(); i++ {
		field := typ.Field(i)

		excelTag := field.Tag.Get("excel")
		if excelTag != "" {
			columns = append(columns, excelTag)
		}
	}

	cell, err := excelize.CoordinatesToCellName(1, 1)
	if err != nil {
		return nil, err
	}

	index, err := f.NewSheet("Sheet1")
	if err != nil {
		return nil, err
	}

	err = f.SetSheetRow("Sheet1", cell, &columns)
	if err != nil {
		return nil, err
	}

	for i := range dataAr {
		typ := reflect.TypeOf(dataAr[i])
		val := reflect.ValueOf(dataAr[i])
		vals := make([]string, len(columns))

		for j := 0; j < typ.NumField(); j++ {
			field := typ.Field(j)
			valj := val.Field(j)

			excelTag := field.Tag.Get("excel")
			if excelTag != "" {
				switch field.Type.Kind() {
				case reflect.String:
					vals[j] = val.Field(j).String()
				case reflect.Int, reflect.Int32, reflect.Int64:
					vals[j] = fmt.Sprintf("%d", valj.Int())
				case reflect.Float32, reflect.Float64:
					vals[j] = fmt.Sprintf("%.2f", valj.Float())
				default:
					return nil, fmt.Errorf("unsupported type %s", field.Type.Kind())
				}
			}
		}

		cell, err := excelize.CoordinatesToCellName(1, i+2)
		if err != nil {
			return nil, err
		}

		err = f.SetSheetRow("Sheet1", cell, &vals)
		if err != nil {
			return nil, err
		}
	}

	f.SetActiveSheet(index)

	var buf []byte
	buffer := bytes.NewBuffer(buf)
	if _, err := f.WriteTo(buffer); err != nil {
		return nil, err
	}

	return buffer.Bytes(), nil
}
