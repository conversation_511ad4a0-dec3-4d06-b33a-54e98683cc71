package altscore

import (
	"git.redmadrobot.com/backend-go/rmr-pkg/core/cfg"
	"git.redmadrobot.com/backend-go/rmr-pkg/core/cfg/viperx"
	"github.com/spf13/viper"
)

const (
	CfgAltScoreBaseURL cfg.Key = "ALTSCORE_BASE_URL"

	CfgDefaultAltScoreBaseURL = ""
)

type (
	Config struct {
		// BaseURL - базовый URL сервиса AltScore
		BaseURL string `env:"ALTSCORE_BASE_URL"`
	}
)

func CfgFromViper(loader *viper.Viper, keyMapping ...cfg.KeyMap) *Config {
	loader.SetDefault(CfgAltScoreBaseURL.String(), CfgDefaultAltScoreBaseURL)

	return &Config{
		BaseURL: viperx.Get(loader, CfgAltScoreBaseURL.Map(keyMapping...), CfgDefaultAltScoreBaseURL),
	}
}
