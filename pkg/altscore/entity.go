package altscore

type (
	ParseBankStatementReq struct {
		AltScoreRequest
	}

	AltScoreRequest struct {
		Statement []byte `json:"file"`
		BankName  string `json:"bankName"`
	}

	ParseBankStatementResp struct {
		Response     AltScoreResponse
		ErrorMessage *ErrorResponse `json:"errorMessage,omitempty"`
	}

	AltScoreResponse struct {
		IsDiscrepancy bool          `json:"isDiscrepancy,omitempty"`
		UserDetail    UserDetail    `json:"userDetail,omitempty"`
		Transactions  []Transaction `json:"transactions,omitempty"`
	}

	ErrorResponse struct {
		ErrorMessage string `json:"message,omitempty"`
		StatusCode   int    `json:"statusCode"`
		Error        string `json:"error,omitempty"`
	}

	UserDetail struct {
		FullName      string             `json:"fullName"`
		IIN           *string            `json:"iin,omitempty"` // Только для HALYK
		AccountNumber string             `json:"accountNumber"`
		Currency      string             `json:"currency"`
		BankName      string             `json:"bankName"`
		CardNumber    string             `json:"cardNumber"`
		Period        Period             `json:"period"`
		Balance       Balance            `json:"balance"`
		Transaction   TransactionSummary `json:"transaction"`
		Limit         *Limit             `json:"limit,omitempty"` // Только для KASPI
	}

	Period struct {
		StartDate string `json:"startDate"`
		EndDate   string `json:"endDate"`
	}

	Balance struct {
		Before float64 `json:"before"`
		After  float64 `json:"after"`
	}
	TransactionSummary struct {
		*TransactionSummaryKaspi
		*TransactionSummaryHalyk
	}

	TransactionSummaryKaspi struct {
		// KASPI
		Replenishment *float64 `json:"replenishment,omitempty"`
		Transfers     *float64 `json:"transfers,omitempty"`
		Purchases     *float64 `json:"purchases,omitempty"`
		Withdrawals   *float64 `json:"withdrawals,omitempty"`
		Other         *float64 `json:"other,omitempty"`
	}
	TransactionSummaryHalyk struct {
		// HALYK
		TotalReceipt    *float64 `json:"totalReceipt,omitempty"`
		TotalExpense    *float64 `json:"totalExpense,omitempty"`
		TotalCommission *float64 `json:"totalCommission,omitempty"`
	}

	Limit struct {
		RemainingSalary float64 `json:"remainingSalary"`
		Transfers       float64 `json:"transfers"`
	}

	Transaction struct {
		Date          string   `json:"date"`
		CompletedDate *string  `json:"completedDate,omitempty"` // Только для HALYK
		Amount        float64  `json:"amount"`
		Commission    *float64 `json:"commission,omitempty"` // Только для HALYK
		Type          *string  `json:"type,omitempty"`       // Только для KASPI
		Detail        string   `json:"detail"`
	}

	Transactions []Transaction
)
