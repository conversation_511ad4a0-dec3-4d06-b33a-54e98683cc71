package altscore

import (
	"bytes"
	"context"
	"fmt"
	"net/http"

	"github.com/go-resty/resty/v2"
)

func (a *AltScoreProviderImpl) ParseBankStatement(
	ctx context.Context,
	req *ParseBankStatementReq,
) (*ParseBankStatementResp, error) {
	var (
		resp                ParseBankStatementResp
		parsedBankStatement AltScoreResponse
		errorResp           ErrorResponse
	)

	httpResp, err := a.HTTPClient.R().
		SetContext(ctx).
		SetFileReader("file", fileName, bytes.NewReader(req.Statement)).
		SetFormData(
			map[string]string{
				bankName: req.BankName,
			},
		).
		SetError(&errorResp).
		SetResult(&parsedBankStatement).
		Post(a.BaseURL + parseBankStatementEndpoint)

	if err = handleHTTPError(err, httpResp, &errorResp, &resp); err != nil {
		return &resp, err
	}

	resp.Response = parsedBankStatement

	return &resp, nil
}

func handleHTTPError(
	err error,
	httpResponse *resty.Response,
	errorResp *ErrorResponse,
	resp *ParseBankStatementResp,
) error {
	if err != nil {
		return fmt.Errorf("error sending request: %w", err)
	}

	if httpResponse.IsError() && errorResp.StatusCode >= http.StatusBadRequest {
		resp.ErrorMessage = errorResp
		return fmt.Errorf(
			"error sending request, error message: %v, status code: %d", errorResp.ErrorMessage, errorResp.StatusCode,
		)
	}

	return nil
}
