package otp

import (
	"context"
	"crypto/rand"
	"fmt"
	"math/big"
	"time"

	"google.golang.org/grpc/metadata"
)

const (
	// todo: вынести в конфиг
	otpTestCode     = "1111"
	otpReviewerCode = "0000"
	otpLength       = 4
	envDev          = "dev"
	envStage        = "stage"
)

type (
	CodeGeneratorImpl struct {
		env             string
		otpTestCode     string
		otpReviewerCode string
		otpLength       int
	}

	CodeGeneratorCfg struct {
		Env             string
		OtpTestCode     string
		OtpReviewerCode string
		OtpLength       int
	}

	CodeGenerator interface {
		Generate(ctx context.Context) string
	}
)

func NewCodeGenerator(cfg *CodeGeneratorCfg) CodeGenerator {
	return &CodeGeneratorImpl{
		env:             cfg.Env,
		otpTestCode:     cfg.OtpTestCode,
		otpReviewerCode: cfg.OtpReviewerCode,
		otpLength:       cfg.OtpLength,
	}
}

// Generate формирует символьный код OTP для проверки пользовательского действия
func (g *CodeGeneratorImpl) Generate(ctx context.Context) string {
	md, ok := metadata.FromIncomingContext(ctx)
	if ok {
		// Устанавливаем роль reviewer если соответствующие данные есть в метаданных
		specUser := md.Get("specialUser")
		if len(specUser) > 0 {
			if specUser[0] == "review" {
				return otpReviewerCode
			}
		}
	}
	if g.env == envDev || g.env == envStage {
		return g.otpTestCode
	}

	var code string
	for {
		// Generate a random number between 0 and 9999
		n, err := rand.Int(rand.Reader, big.NewInt(10000))
		if err != nil {
			// Fallback to the original implementation if crypto/rand fails
			return g.generateTimeBasedCode()
		}

		// Format as a 4-digit string with leading zeros
		code = fmt.Sprintf("%04d", n.Int64())

		// Ensure the code doesn't have all the same digits
		if !hasAllSameDigits(code) {
			break
		}
	}
	return code
}

// generateTimeBasedCode is the original implementation using time.Now().Nanosecond()
// Used as a fallback if crypto/rand fails
func (g *CodeGeneratorImpl) generateTimeBasedCode() string {
	var code string
	for {
		code = fmt.Sprint(time.Now().Nanosecond())[:g.otpLength]
		if !hasAllSameDigits(code) {
			break
		}
		// Ожидание другой наносекунды для кода
		time.Sleep(time.Nanosecond)
	}
	return code
}

func hasAllSameDigits(s string) bool {
	if len(s) <= 1 {
		return true
	}
	firstChar := s[0]
	for i := 1; i < len(s); i++ {
		if s[i] != firstChar {
			return false
		}
	}
	return true
}
