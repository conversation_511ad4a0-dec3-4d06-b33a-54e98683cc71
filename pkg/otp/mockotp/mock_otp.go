// Status generated by MockGen. DO NOT EDIT.
// Source: ./pkg/otp/reason.go

// Package mock_otp is a generated GoMock package.
package mockotp

import (
	"context"
	"reflect"
	"time"

	"git.redmadrobot.com/backend-go/rmr-pkg/core/memorystore"
	"git.redmadrobot.com/zaman/backend/zaman/pkg/otp"
	"github.com/golang/mock/gomock"
)

// MockRedis is a mock of Redis interface.
type MockRedis struct {
	ctrl     *gomock.Controller
	recorder *MockRedisMockRecorder
}

// MockRedisMockRecorder is the mock recorder for MockRedis.
type MockRedisMockRecorder struct {
	mock *MockRedis
}

// NewMockRedis creates a new mock instance.
func NewMockRedis(ctrl *gomock.Controller) *MockRedis {
	mock := &MockRedis{ctrl: ctrl}
	mock.recorder = &MockRedisMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockRedis) EXPECT() *MockRedisMockRecorder {
	return m.recorder
}

// Close mocks base method.
func (m *MockRedis) Close(ctx context.Context) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Close", ctx)
}

// Close indicates an expected call of Close.
func (mr *MockRedisMockRecorder) Close(ctx interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockRedis)(nil).Close), ctx)
}

// Delete mocks base method.
func (m *MockRedis) Delete(ctx context.Context, keys ...string) (int, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx}
	for _, a := range keys {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "Delete", varargs...)
	ret0, _ := ret[0].(int)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Delete indicates an expected call of Delete.
func (mr *MockRedisMockRecorder) Delete(ctx interface{}, keys ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx}, keys...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Delete", reflect.TypeOf((*MockRedis)(nil).Delete), varargs...)
}

// Get mocks base method.
func (m *MockRedis) Get(ctx context.Context, key string) (*memorystore.Value, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Get", ctx, key)
	ret0, _ := ret[0].(*memorystore.Value)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Get indicates an expected call of Get.
func (mr *MockRedisMockRecorder) Get(ctx, key interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Get", reflect.TypeOf((*MockRedis)(nil).Get), ctx, key)
}

// GetList mocks base method.
func (m *MockRedis) GetList(ctx context.Context, keys ...string) ([]*memorystore.Value, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx}
	for _, a := range keys {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetList", varargs...)
	ret0, _ := ret[0].([]*memorystore.Value)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetList indicates an expected call of GetList.
func (mr *MockRedisMockRecorder) GetList(ctx interface{}, keys ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx}, keys...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetList", reflect.TypeOf((*MockRedis)(nil).GetList), varargs...)
}

// Set mocks base method.
func (m *MockRedis) Set(ctx context.Context, key string, value any, expiration time.Duration) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Set", ctx, key, value, expiration)
	ret0, _ := ret[0].(error)
	return ret0
}

// Set indicates an expected call of Set.
func (mr *MockRedisMockRecorder) Set(ctx, key, value, expiration interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Set", reflect.TypeOf((*MockRedis)(nil).Set), ctx, key, value, expiration)
}

// MockProviderOtp is a mock of ProviderOtp interface.
type MockProviderOtp struct {
	ctrl     *gomock.Controller
	recorder *MockProviderOtpMockRecorder
}

// MockProviderOtpMockRecorder is the mock recorder for MockProviderOtp.
type MockProviderOtpMockRecorder struct {
	mock *MockProviderOtp
}

// NewMockProviderOtp creates a new mock instance.
func NewMockProviderOtp(ctrl *gomock.Controller) *MockProviderOtp {
	mock := &MockProviderOtp{ctrl: ctrl}
	mock.recorder = &MockProviderOtpMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockProviderOtp) EXPECT() *MockProviderOtpMockRecorder {
	return m.recorder
}

// CreateNewAttempt mocks base method.
func (m *MockProviderOtp) CreateNewAttempt(ctx context.Context, otpRequest *otp.Request) (*otp.Request, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateNewAttempt", ctx, otpRequest)
	ret0, _ := ret[0].(*otp.Request)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateNewAttempt indicates an expected call of CreateNewAttempt.
func (mr *MockProviderOtpMockRecorder) CreateNewAttempt(ctx, otpRequest interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateNewAttempt", reflect.TypeOf((*MockProviderOtp)(nil).CreateNewAttempt), ctx, otpRequest)
}

// CreateNewOtp mocks base method.
func (m *MockProviderOtp) CreateNewOtp(ctx context.Context, initiator, action string, payload []byte) (*otp.Request, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateNewOtp", ctx, initiator, action, payload)
	ret0, _ := ret[0].(*otp.Request)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateNewOtp indicates an expected call of CreateNewOtp.
func (mr *MockProviderOtpMockRecorder) CreateNewOtp(ctx, initiator, action, payload interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateNewOtp", reflect.TypeOf((*MockProviderOtp)(nil).CreateNewOtp), ctx, initiator, action, payload)
}

// GetOtpRequestByAction mocks base method.
func (m *MockProviderOtp) GetOtpRequestByAction(ctx context.Context, initiator, action string) (*otp.Request, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetOtpRequestByAction", ctx, initiator, action)
	ret0, _ := ret[0].(*otp.Request)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetOtpRequestByAction indicates an expected call of GetOtpRequestByAction.
func (mr *MockProviderOtpMockRecorder) GetOtpRequestByAction(ctx, initiator, action interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOtpRequestByAction", reflect.TypeOf((*MockProviderOtp)(nil).GetOtpRequestByAction), ctx, initiator, action)
}

// GetOtpRequestByAttemptID mocks base method.
func (m *MockProviderOtp) GetOtpRequestByAttemptID(ctx context.Context, attemptID string) (*otp.Request, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetOtpRequestByAttemptID", ctx, attemptID)
	ret0, _ := ret[0].(*otp.Request)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetOtpRequestByAttemptID indicates an expected call of GetOtpRequestByAttemptID.
func (mr *MockProviderOtpMockRecorder) GetOtpRequestByAttemptID(ctx, attemptID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOtpRequestByAttemptID", reflect.TypeOf((*MockProviderOtp)(nil).GetOtpRequestByAttemptID), ctx, attemptID)
}

// ValidateCode mocks base method.
func (m *MockProviderOtp) ValidateCode(ctx context.Context, otpRequest *otp.Request, code string) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ValidateCode", ctx, otpRequest, code)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ValidateCode indicates an expected call of ValidateCode.
func (mr *MockProviderOtpMockRecorder) ValidateCode(ctx, otpRequest, code interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ValidateCode", reflect.TypeOf((*MockProviderOtp)(nil).ValidateCode), ctx, otpRequest, code)
}
