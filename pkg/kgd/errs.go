package kgd

import "git.redmadrobot.com/backend-go/rmr-pkg/core/errs"

const (
	InvalidSchemaDesc      = "invalidSchema"
	AuthErr                = "kgdAuthErr"
	KgdInternalServerError = "KgdInternalServerError"
)

var (
	ErrInvalidSchema          = errs.Reasons(InvalidSchemaDesc, errs.TypeIllegalArgument)
	ErrAuth                   = errs.Reasons(AuthErr, errs.TypeForbidden)
	ErrKgdInternalServerError = errs.Reasons(KgdInternalServerError, errs.TypeInternal)
)
