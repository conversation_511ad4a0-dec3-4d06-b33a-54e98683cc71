package kgd

import (
	"bytes"
	"context"
	"encoding/xml"
	"fmt"
	"io"
	"net/http"

	"git.redmadrobot.com/backend-go/rmr-pkg/tools/crypto"

	"github.com/google/uuid"

	"git.redmadrobot.com/backend-go/rmr-pkg/core/errs"
	"git.redmadrobot.com/zaman/backend/zaman/pkg/kgd/entity"
)

const (
	serviceID = "ISNA_DEBTS"
)

type (
	KGDProvider interface {
		RequestDebts(ctx context.Context, personIIN string) (*entity.ResponseBody, error)
	}

	KGDProviderImpl struct {
		HTTPClient *http.Client
		Config     *Config
		CertString string
	}
)

func NewKGDProvider(cfg *Config) (KGDProvider, error) {
	certString, err := crypto.LoadCertificateFromBase64(cfg.SignatureBase64)
	if err != nil {
		return nil, fmt.Errorf("failed to load certificate: %w", err)
	}
	// todo: добавить таймаут в конфиг
	return &KGDProviderImpl{
		HTTPClient: &http.Client{
			Timeout: cfg.HTTPTimeout,
		},
		Config:     cfg,
		CertString: certString,
	}, nil
}

func (p *KGDProviderImpl) RequestDebts(ctx context.Context, personIIN string) (*entity.ResponseBody, error) {
	reqBody := p.newRequestBody(personIIN)
	reqBodyBytes, err := xml.Marshal(reqBody)
	if err != nil {
		return nil, errs.Wrapf(err, "failed to marshal request body")
	}

	req, err := http.NewRequestWithContext(
		ctx,
		"POST",
		fmt.Sprintf("%s/bip-sync-wss-gost/", p.Config.BaseURL),
		bytes.NewBuffer(reqBodyBytes),
	)
	if err != nil {
		return nil, errs.Wrapf(err, "failed to create new http request")
	}
	req.Header.Set("Content-Type", "application/xml")

	resp, err := p.HTTPClient.Do(req)
	if err != nil {
		return nil, errs.Wrapf(err, "failed to do http request")
	}
	defer resp.Body.Close()

	var response entity.ResponseBody
	if resp.StatusCode != http.StatusOK {
		errResp, err := handleKGDRespErrStatusCode(ctx, resp)
		if err != nil {
			return nil, err
		}

		response.ErrResp = map[string]string{"error": errResp}
		return &response, ErrKgdInternalServerError
	}

	if err = xml.NewDecoder(resp.Body).Decode(&response); err != nil {
		return nil, errs.Wrapf(err, "failed to decode http response body")
	}

	return &response, nil
}

func (p *KGDProviderImpl) newRequestBody(personIIN string) *entity.RequestDebtsReqBody {
	return &entity.RequestDebtsReqBody{
		Body: entity.RequestBodyContent{
			SendMessage: entity.RequestBodyContentMessage{
				Request: entity.MessageContent{
					RequestInfo: entity.DebtsRequestInfo{
						MessageID:     uuid.New().String(),
						CorrelationID: uuid.New().String(),
						ServiceID:     serviceID,
						MessageDate:   formDate(),
						Sender: entity.Sender{
							SenderID: p.Config.Username,
							Password: p.Config.Password,
						},
					},
					RequestData: entity.DebtsRequestData{
						Data: entity.DebtsRequestDataContent{
							Type: "requestData",
							Request: entity.DebtsDataRequest{
								MessageID:   uuid.New().String(),
								ChainID:     uuid.New().String(),
								SendTime:    formDate(),
								MessageType: "request",
								IinBin:      personIIN,
								Signature: entity.Signature{
									Ds:             "https://www.w3.org/2000/09/xmldsig#",
									SignatureValue: p.CertString,
									SignedInfo: entity.SignedInfo{
										CanonicalizationMethod: entity.CanonicalizationMethod{
											Algorithm: "http://www.w3.org/TR/2001/REC-xml-c14n-20010315",
										},
										SignatureMethod: entity.SignatureMethod{
											Algorithm: "http://www.w3.org/2001/04/xmldsig-more#gost34310-gost34311",
										},
										Reference: entity.Reference{
											URI: "#",
											Transforms: entity.Transforms{
												Text: "",
												Transform: []entity.Transform{
													{
														Algorithm: "http://www.w3.org/2000/09/xmldsig#enveloped-signature",
													},
													{
														Algorithm: "http://www.w3.org/TR/2001/REC-xml-c14n-20010315#WithComments",
													},
												},
											},
											DigestMethod: entity.DigestMethod{
												Algorithm: "http://www.w3.org/2001/04/xmldsig-more#gost34311",
											},
											DigestValue: "3B5i/2YDUrBtR3G6DPY9hUNXiEjMi3uKPCFKeQlZRIo=",
										},
									},
									KeyInfo: entity.KeyInfo{
										X509Data: entity.X509Data{
											X509Certificate: p.CertString,
										},
									},
								},
							},
						},
					},
				},
			},
		},
	}
}

func handleKGDRespErrStatusCode(_ context.Context, resp *http.Response) (string, error) {
	bodyBytes, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", fmt.Errorf("failed to read response body for create client request: %w", err)
	}

	return string(bodyBytes), nil
}
