package mock_kgd

import (
	"context"
	"slices"

	"git.redmadrobot.com/zaman/backend/zaman/pkg/kgd"
	"git.redmadrobot.com/zaman/backend/zaman/pkg/kgd/entity"
)

type MockKGDProvider struct{}

func NewMockProvider() kgd.KGDProvider {
	mockKGDProvider := MockKGDProvider{}
	return mockKGDProvider
}

func (m MockKGDProvider) RequestDebts(_ context.Context, personIIN string) (*entity.ResponseBody, error) {
	checkFailedIINs := []string{
		"777000000010",
		"760315300436",
		"110240013139",
		"961203351268",
	}

	switch {
	case slices.Contains(checkFailedIINs, personIIN):
		return userWithDebtsResp, nil
	default:
		return successResp, nil
	}
}
