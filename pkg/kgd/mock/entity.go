package mock_kgd

import (
	"encoding/xml"

	kgd "git.redmadrobot.com/zaman/backend/zaman/pkg/kgd/entity"
)

var (
	successResp = &kgd.ResponseBody{
		XMLName: xml.Name{},
		Text:    "",
		SOAPENV: "",
		Header:  "",
		Body: kgd.RespBody{
			Text: "",
			SendMessageResponse: kgd.SendMessageResponse{
				Text: "",
				Ns3:  "",
				Response: kgd.Response{
					Text: "",
					ResponseData: kgd.ResponseData{
						Text: "",
						ResponseData: kgd.ResponseDataInfo{
							Text: "",
							Xs:   "",
							Xsi:  "",
							Type: "",
							Response: kgd.ResponseInfo{
								Text:                        "",
								Ns2:                         "",
								Xmlns:                       "",
								MessageID:                   "",
								ChainID:                     "",
								SendTime:                    "",
								MessageType:                 "",
								ResponseCode:                0,
								IinBin:                      "",
								NameRu:                      "Test user",
								NameKk:                      "Test user",
								NameQq:                      "Test user",
								TotalArrear:                 0,
								TotalTaxArrear:              0,
								PensionContributionArrear:   0,
								SocialContributionArrear:    0,
								SocialHealthInsuranceArrear: 0,
								TaxOrgInfo:                  nil,
								Signature:                   kgd.Signature{},
							},
						},
					},
				},
			},
		},
	}

	userWithDebtsResp = &kgd.ResponseBody{
		XMLName: xml.Name{},
		Text:    "",
		SOAPENV: "",
		Header:  "",
		Body: kgd.RespBody{
			Text: "",
			SendMessageResponse: kgd.SendMessageResponse{
				Text: "",
				Ns3:  "",
				Response: kgd.Response{
					Text: "",
					ResponseData: kgd.ResponseData{
						Text: "",
						ResponseData: kgd.ResponseDataInfo{
							Text: "",
							Xs:   "",
							Xsi:  "",
							Type: "",
							Response: kgd.ResponseInfo{
								Text:                        "",
								Ns2:                         "",
								Xmlns:                       "",
								MessageID:                   "",
								ChainID:                     "",
								SendTime:                    "",
								MessageType:                 "",
								ResponseCode:                0,
								IinBin:                      "",
								NameRu:                      "Test user",
								NameKk:                      "Test user",
								NameQq:                      "Test user",
								TotalArrear:                 120000,
								TotalTaxArrear:              0,
								PensionContributionArrear:   0,
								SocialContributionArrear:    0,
								SocialHealthInsuranceArrear: 0,
								TaxOrgInfo:                  nil,
								Signature:                   kgd.Signature{},
							},
						},
					},
				},
			},
		},
	}
)
