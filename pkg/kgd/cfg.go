package kgd

import (
	"time"

	"github.com/spf13/viper"

	"git.redmadrobot.com/backend-go/rmr-pkg/core/cfg"
	"git.redmadrobot.com/backend-go/rmr-pkg/core/cfg/viperx"
)

const (
	CfgKGDBaseURL               cfg.Key = "BASE_URL"
	CfgKGDSignatureFileName     cfg.Key = "SIGNATURE_FILENAME"      // TODO: удалить, после тестирования нового формата сертификата
	CfgKGDSignatureFilePassword cfg.Key = "SIGNATURE_FILE_PASSWORD" // TODO: удалить, после тестирования нового формата сертификата
	CfgKGDSignatureBase64       cfg.Key = "SIGNATURE_BASE64"
	CfgKGDUsernameString        cfg.Key = "USERNAME"
	CfgKGDPasswordString        cfg.Key = "PASSWORD"
	CfgHTTPTimeout              cfg.Key = "HTTP_TIMEOUT"

	defaultKGDBaseURL               = ""
	defaultKGDSignatureFileName     = "" // TODO: удалить, после тестирования нового формата сертификата
	defaultKGDSignatureFilePassword = "" // TODO: удалить, после тестирования нового формата сертификата
	defaultKGDSignatureBase64       = ""
	defaultKGDUsernameString        = ""
	defaultKGDPasswordString        = ""
	defaultHTTPTimeout              = time.Second * 60 // 60 секунд таймаута по умолчанию для http запросов
)

type Config struct {
	// BaseURL базовый URL для kgd
	BaseURL string

	// Signature имя файла с подписью для kgd
	SignatureFileName string // TODO: удалить, после тестирования нового формата сертификата
	// SignatureFilePassword пароль для подписи файла kgd
	signatureFilePassword string // TODO: удалить, после тестирования нового формата сертификата

	// SignatureBase64 base64 строка с сертификатом для kgd
	SignatureBase64 string
	// Username имя пользователя для kgd
	Username string
	// Password пароль пользователя для kgd
	Password string

	// HTTPTimeout таймаут для http запросов
	HTTPTimeout time.Duration
}

func CfgFromViper(loader *viper.Viper, keyMapping ...cfg.KeyMap) *Config {
	loader.SetDefault(CfgKGDSignatureFileName.String(), defaultKGDSignatureFileName)         // TODO: удалить, после тестирования нового формата сертификата
	loader.SetDefault(CfgKGDSignatureFilePassword.String(), defaultKGDSignatureFilePassword) // TODO: удалить, после тестирования нового формата сертификата

	loader.SetDefault(CfgKGDSignatureBase64.String(), defaultKGDBaseURL)
	loader.SetDefault(CfgKGDBaseURL.String(), defaultKGDBaseURL)

	loader.SetDefault(CfgKGDSignatureFileName.String(), defaultKGDSignatureBase64)
	loader.SetDefault(CfgKGDUsernameString.String(), defaultKGDUsernameString)
	loader.SetDefault(CfgKGDPasswordString.String(), defaultKGDPasswordString)
	loader.SetDefault(CfgHTTPTimeout.String(), defaultHTTPTimeout)

	return &Config{
		BaseURL:               viperx.Get(loader, CfgKGDBaseURL.Map(keyMapping...), ""),
		SignatureFileName:     viperx.Get(loader, CfgKGDSignatureFileName.Map(keyMapping...), ""),
		signatureFilePassword: viperx.Get(loader, CfgKGDSignatureFilePassword.Map(keyMapping...), ""),
		Username:              viperx.Get(loader, CfgKGDUsernameString.Map(keyMapping...), ""),
		Password:              viperx.Get(loader, CfgKGDPasswordString.Map(keyMapping...), ""),
		HTTPTimeout:           viperx.Get(loader, CfgHTTPTimeout.Map(keyMapping...), defaultHTTPTimeout),
	}
}
