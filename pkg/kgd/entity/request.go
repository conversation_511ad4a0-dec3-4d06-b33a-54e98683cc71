package entity

import (
	"encoding/xml"
)

type (
	RequestBodyContent struct {
		Text        string                    `xml:",chardata"`
		SendMessage RequestBodyContentMessage `xml:"SendMessage"`
	}

	RequestBodyContentMessage struct {
		Text    string         `xml:",chardata"`
		Ns3     string         `xml:"ns3,attr"`
		Request MessageContent `xml:"request"`
	}

	MessageContent struct {
		Text        string           `xml:",chardata"`
		RequestInfo DebtsRequestInfo `xml:"requestInfo"`
		RequestData DebtsRequestData `xml:"requestData"`
	}

	DebtsRequestData struct {
		Text string                  `xml:",chardata"`
		Data DebtsRequestDataContent `xml:"data"`
	}

	DebtsRequestDataContent struct {
		Text    string           `xml:",chardata"`
		Xs      string           `xml:"xs,attr"`
		Xsi     string           `xml:"xsi,attr"`
		Type    string           `xml:"type,attr"`
		Request DebtsDataRequest `xml:"request"`
	}

	DebtsRequestInfo struct {
		Text          string `xml:",chardata"`
		MessageID     string `xml:"messageId"`
		CorrelationID string `xml:"correlationId"`
		ServiceID     string `xml:"serviceId"`
		MessageDate   string `xml:"messageDate"`
		Sender        Sender `xml:"sender"`
	}

	DebtsDataRequest struct {
		Text        string    `xml:",chardata"`
		Xmlns       string    `xml:"xmlns,attr"`
		Ns2         string    `xml:"ns2,attr"`
		MessageID   string    `xml:"messageId"`
		ChainID     string    `xml:"chainId"`
		SendTime    string    `xml:"sendTime"`
		MessageType string    `xml:"messageType"`
		IinBin      string    `xml:"iinBin"`
		Signature   Signature `xml:"Signature"`
	}

	Sender struct {
		Text     string `xml:",chardata"`
		SenderID string `xml:"senderId"`
		Password string `xml:"password"`
	}

	CanonicalizationMethod struct {
		Text      string `xml:",chardata"`
		Algorithm string `xml:"Algorithm,attr"`
	}

	SignatureMethod struct {
		Text      string `xml:",chardata"`
		Algorithm string `xml:"Algorithm,attr"`
	}

	Transforms struct {
		Text      string      `xml:",chardata"`
		Transform []Transform `xml:"Transform"`
	}

	Transform struct {
		Text      string `xml:",chardata"`
		Algorithm string `xml:"Algorithm,attr"`
	}

	DigestMethod struct {
		Text      string `xml:",chardata"`
		Algorithm string `xml:"Algorithm,attr"`
	}

	Reference struct {
		Text         string       `xml:",chardata"`
		URI          string       `xml:"URI,attr"`
		Transforms   Transforms   `xml:"Transforms"`
		DigestMethod DigestMethod `xml:"DigestMethod"`
		DigestValue  string       `xml:"DigestValue"`
	}

	X509Data struct {
		Text            string `xml:",chardata"`
		X509Certificate string `xml:"X509Certificate"`
	}

	KeyInfo struct {
		Text     string   `xml:",chardata"`
		X509Data X509Data `xml:"X509Data"`
	}

	SignedInfo struct {
		Text                   string                 `xml:",chardata"`
		CanonicalizationMethod CanonicalizationMethod `xml:"CanonicalizationMethod"`
		SignatureMethod        SignatureMethod        `xml:"SignatureMethod"`
		Reference              Reference              `xml:"Reference"`
	}

	Signature struct {
		Text           string     `xml:",chardata"`
		Ds             string     `xml:"ds,attr"`
		SignedInfo     SignedInfo `xml:"SignedInfo"`
		SignatureValue string     `xml:"SignatureValue"`
		KeyInfo        KeyInfo    `xml:"KeyInfo"`
	}

	RequestDebtsReqBody struct {
		XMLName xml.Name           `xml:"Envelope"`
		Text    string             `xml:",chardata"`
		SOAPENV string             `xml:"SOAP-ENV,attr"`
		Header  string             `xml:"HeaderRequest"`
		Body    RequestBodyContent `xml:"Body"`
	}
)
