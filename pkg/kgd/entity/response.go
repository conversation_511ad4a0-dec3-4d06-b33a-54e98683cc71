package entity

import "encoding/xml"

type (
	RespBody struct {
		Text                string              `xml:",chardata"`
		SendMessageResponse SendMessageResponse `xml:"SendMessageResponse"`
	}

	SendMessageResponse struct {
		Text     string   `xml:",chardata"`
		Ns3      string   `xml:"ns3,attr"`
		Response Response `xml:"response"`
	}

	Response struct {
		Text         string       `xml:",chardata"`
		ResponseData ResponseData `xml:"responseData"`
	}

	BccArrearsInfo struct {
		Text          string  `xml:",chardata"`
		Bcc           string  `xml:"bcc"`
		BccNameRu     string  `xml:"bccNameRu"`
		BccNameKz     string  `xml:"bccNameKz"`
		TaxArrear     float64 `xml:"taxArrear"`
		PoenaArrear   float64 `xml:"poenaArrear"`
		PercentArrear float64 `xml:"percentArrear"`
		FineArrear    float64 `xml:"fineArrear"`
		TotalArrear   float64 `xml:"totalArrear"`
	}

	TaxPayerInfo struct {
		Text           string           `xml:",chardata"`
		IinBin         string           `xml:"iinBin"`
		Rnn            string           `xml:"rnn"`
		NameRu         string           `xml:"nameRu"`
		NameKk         string           `xml:"nameKk"`
		NameQq         string           `xml:"nameQq"`
		BccArrearsInfo []BccArrearsInfo `xml:"bccArrearsInfo"`
		TaxArrear      float64          `xml:"taxArrear"`
		PoenaArrear    float64          `xml:"poenaArrear"`
		PercentArrear  float64          `xml:"percentArrear"`
		FineArrear     float64          `xml:"fineArrear"`
		TotalArrear    float64          `xml:"totalArrear"`
	}

	TaxOrgInfo struct {
		Text                        string       `xml:",chardata"`
		CharCode                    string       `xml:"charCode"`
		NameRu                      string       `xml:"nameRu"`
		NameKz                      string       `xml:"nameKz"`
		ReportAcrualDate            string       `xml:"reportAcrualDate"`
		TotalArrear                 float64      `xml:"totalArrear"`
		TotalTaxArrear              float64      `xml:"totalTaxArrear"`
		PensionContributionArrear   float64      `xml:"pensionContributionArrear"`
		SocialContributionArrear    float64      `xml:"socialContributionArrear"`
		SocialHealthInsuranceArrear float64      `xml:"socialHealthInsuranceArrear"`
		TaxPayerInfo                TaxPayerInfo `xml:"taxPayerInfo"`
	}

	ResponseInfo struct {
		Text                        string       `xml:",chardata"`
		Ns2                         string       `xml:"ns2,attr"`
		Xmlns                       string       `xml:"xmlns,attr"`
		MessageID                   string       `xml:"messageId"`
		ChainID                     string       `xml:"chainId"`
		SendTime                    string       `xml:"sendTime"`
		MessageType                 string       `xml:"messageType"`
		ResponseCode                int          `xml:"responseCode"`
		IinBin                      string       `xml:"iinBin"`
		NameRu                      string       `xml:"nameRu"`
		NameKk                      string       `xml:"nameKk"`
		NameQq                      string       `xml:"nameQq"`
		TotalArrear                 float64      `xml:"totalArrear"`
		TotalTaxArrear              float64      `xml:"totalTaxArrear"`
		PensionContributionArrear   float64      `xml:"pensionContributionArrear"`
		SocialContributionArrear    float64      `xml:"socialContributionArrear"`
		SocialHealthInsuranceArrear float64      `xml:"socialHealthInsuranceArrear"`
		TaxOrgInfo                  []TaxOrgInfo `xml:"taxOrgInfo"`
		Signature                   Signature    `xml:"Signature"`
	}

	ResponseDataInfo struct {
		Text     string       `xml:",chardata"`
		Xs       string       `xml:"xs,attr"`
		Xsi      string       `xml:"xsi,attr"`
		Type     string       `xml:"type,attr"`
		Response ResponseInfo `xml:"response"`
	}

	ResponseData struct {
		Text         string           `xml:",chardata"`
		ResponseData ResponseDataInfo `xml:"responseData"`
	}

	ResponseBody struct {
		XMLName xml.Name `xml:"Envelope"`
		Text    string   `xml:",chardata"`
		SOAPENV string   `xml:"SOAP-ENV,attr"`
		Header  string   `xml:"HeaderRequest"`
		Body    RespBody `xml:"DebtsRequestBody"`
		ErrResp map[string]string
	}
)
