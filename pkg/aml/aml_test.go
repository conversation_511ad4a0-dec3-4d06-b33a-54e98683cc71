package aml

import (
	"context"
	"encoding/xml"
	"fmt"
	"net/http"
	"net/http/httptest"
	"os"
	"reflect"
	"strings"
	"testing"

	"git.redmadrobot.com/backend-go/rmr-pkg/tools/conversion"
	"git.redmadrobot.com/backend-go/rmr-pkg/tools/serial"
	"git.redmadrobot.com/zaman/backend/zaman/pkg/aml/entity"
	"github.com/stretchr/testify/require"
)

func TestAMLImpl_CheckOnlineClientCard(t *testing.T) {
	resp := &entity.CheckOnlineClientCardResponse{
		XMLName: xml.Name{Local: "Envelope"},
		Body: entity.CheckOnlineClientCardResponseBody{
			Response: entity.CheckOnlineClientCardResponseBodyResponse{
				Result: entity.CheckOnlineClientCardResponseBodyResponseResult{
					Comment:    conversion.Ptr("Success comment"),
					Status:     0,
					UID:        "Success UID",
					User:       "Success USER",
					BsClientID: "Success BSCLIENTID",
				},
			},
		},
	}

	mockServer := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		if r.Header.Get("Content-Type") != "text/xml;charset=UTF-8" {
			http.Error(w, "invalid content type", http.StatusUnsupportedMediaType)
			return
		}

		w.WriteHeader(http.StatusOK)
		w.Header().Set("Content-Type", "text/xml;charset=UTF-8")
		err := serial.XMLEncode(w, resp)
		require.NoError(t, err)
	}))
	defer mockServer.Close()

	aml := &AMLProviderImpl{
		HTTPClient: &http.Client{},
		BaseURL:    mockServer.URL,
	}

	t.Run("CheckOnlineClientCard", func(t *testing.T) {
		got, err := aml.CheckOnlineClientCard(context.Background(), &entity.CheckOnlineClientCardRequestBodyCardRequest{})
		if err != nil {
			t.Errorf("CheckOnlineClientCard() error = %v", err)
			return
		}
		if !reflect.DeepEqual(got, resp) {
			t.Errorf("CheckOnlineClientCard() got = %v, want %v", got, resp)
		}
	})
}

func TestAMLImpl_CheckOnlineClientCard_Real(t *testing.T) {
	baseURL := os.Getenv("TEST_AML_BRIDGE_BASE_URL")
	if baseURL == "" {
		t.Skip("skip test")
	}
	// Обращу внимание что здесь ожидается не весь xml запрос, а только response часть, т.е. envelope и body не нужен
	xmlRequest := os.Getenv("TEST_AML_CHECK_ONLINE_CLIENT_CARD_XML_REQUEST")
	if xmlRequest == "" {
		t.Log("xml request is empty")
	}

	req, err := serial.XMLDecode[entity.CheckOnlineClientCardRequestBodyCardRequest](strings.NewReader(xmlRequest))
	if err != nil {
		t.Errorf("CheckOnlineClientCard() error = %v", err)
		return
	}

	aml := &AMLProviderImpl{
		HTTPClient: &http.Client{},
		BaseURL:    baseURL,
	}

	t.Run("CheckOnlineClientCard", func(t *testing.T) {
		got, err := aml.CheckOnlineClientCard(context.Background(), &req)
		if err != nil {
			t.Errorf("CheckOnlineClientCard() error = %v", err)
			return
		}
		fmt.Printf("%+v\n", got)
	})
}
