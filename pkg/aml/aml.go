package aml

import (
	"context"
	"net/http"

	amlbridge "git.redmadrobot.com/zaman/backend/zaman/config/services/aml-bridge"
	"git.redmadrobot.com/zaman/backend/zaman/pkg/aml/entity"
)

type (
	AMLProvider interface {
		CheckOnlineClientCard(ctx context.Context, params *entity.CheckOnlineClientCardRequestBodyCardRequest) (*entity.CheckOnlineClientCardResponse, error)
	}

	AMLProviderImpl struct {
		HTTPClient *http.Client
		BaseURL    string
		UserAml    string
	}
)

func NewAMLProvider(cfg *amlbridge.Config) AMLProvider {
	return &AMLProviderImpl{
		HTTPClient: &http.Client{},
		BaseURL:    cfg.App.BaseURL,
		UserAml:    cfg.App.UserAml,
	}
}
