package entity

import (
	"encoding/xml"
)

type (
	CheckOnlineClientCardRequestEnvelope struct {
		XMLName xml.Name                         `xml:"soap:Envelope"`
		Text    string                           `xml:",chardata"`
		Xsi     string                           `xml:"xmlns:xsi,attr"`
		Xsd     string                           `xml:"xmlns:xsd,attr"`
		Soap    string                           `xml:"xmlns:soap,attr"`
		Body    CheckOnlineClientCardRequestBody `xml:"soap:Body"`
	}

	CheckOnlineClientCardRequestBodyCardRequestSupervisorsSupervisor struct {
		Text       string `xml:",chardata"`
		BSCLIENTID string `xml:"BSCLIENTID"`
	}

	CheckOnlineClientCardRequestBodyCardRequestSupervisors struct {
		Text       string `xml:",chardata"`
		SUPERVISOR struct {
			Text       string `xml:",chardata"`
			BSCLIENTID string `xml:"BSCLIENTID"`
		} `xml:"SUPERVISOR"`
	}

	CheckOnlineClientCardRequestBodyCardRequest struct {
		Text             string  `xml:",chardata"`
		IssuedBid        string  `xml:"ISSUEDBID"`
		User             string  `xml:"USER"`
		UID              string  `xml:"UID"`
		BankClientCode   *string `xml:"BANKCLIENTCODE,omitempty"`
		OperationDate    string  `xml:"OPERATIONDATE"`
		ClientID         *string `xml:"CLIENTID,omitempty"`
		BsClientID       string  `xml:"BSCLIENTID"`
		IsNew            string  `xml:"IS_NEW"`
		UrName           *string `xml:"URNAME,omitempty"`
		Type             *string `xml:"TYPE,omitempty"`
		BankClient       *string `xml:"BANKCLIENT,omitempty"`
		ResCountryCode   *string `xml:"RESCOUNTRYCODE,omitempty"`
		Oked             *string `xml:"OKED,omitempty"`
		ForeignExtra     *string `xml:"FOREIGNEXTRA,omitempty"`
		AcBirthDate      *string `xml:"ACBIRTHDATE,omitempty"`
		AcBirthPlace     *string `xml:"ACBIRTHPLACE,omitempty"`
		AcRegCountry     *string `xml:"ACREGCOUNTRY,omitempty"`
		AcDocTypeCode    *string `xml:"ACDOCTYPECODE,omitempty"`
		AcDocSeries      *string `xml:"ACDOCSERIES,omitempty"`
		AcDocNumber      *string `xml:"ACDOCNUMBER,omitempty"`
		AcDocWhom        *string `xml:"ACDOCWHOM,omitempty"`
		AcDocIssueDate   *string `xml:"ACDOCISSUEDATE,omitempty"`
		RegCountryCode   *string `xml:"REGCOUNTRYCODE,omitempty"`
		RegCity          *string `xml:"REGCITY,omitempty"`
		SeatCountryCode  *string `xml:"SEATCOUNTRYCODE,omitempty"`
		SeatCity         *string `xml:"SEATCITY,omitempty"`
		Branch           *string `xml:"BRANCH,omitempty"`
		ClientCardClosed *string `xml:"CLIENTCARDCLOSED,omitempty"`
	}

	CheckOnlineClientCardRequestBodyCard struct {
		Text    string                                      `xml:",chardata"`
		Xmlns   string                                      `xml:"xmlns,attr"`
		Request CheckOnlineClientCardRequestBodyCardRequest `xml:"request"`
	}

	CheckOnlineClientCardRequestBody struct {
		Text                  string                               `xml:",chardata"`
		CheckOnlineClientCard CheckOnlineClientCardRequestBodyCard `xml:"CheckOnlineClientCard"`
	}

	CheckOnlineClientCardResponse struct {
		XMLName   xml.Name                          `xml:"Envelope"`
		Text      string                            `xml:",chardata"`
		Soap      string                            `xml:"xmlns:soap,attr,omitempty"`
		Xsi       string                            `xml:"xmlns:xsi,attr,omitempty"`
		Xsd       string                            `xml:"xmlns:xsd,attr,omitempty"`
		Body      CheckOnlineClientCardResponseBody `xml:"Body"`
		ErrorResp string                            `xml:"ErrorResp,omitempty"`
	}

	CheckOnlineClientCardResponseBody struct {
		Text     string                                    `xml:",chardata"`
		Response CheckOnlineClientCardResponseBodyResponse `xml:"CheckOnlineClientCardResponse"`
	}

	CheckOnlineClientCardResponseBodyResponse struct {
		Text   string                                          `xml:",chardata"`
		Result CheckOnlineClientCardResponseBodyResponseResult `xml:"CheckOnlineClientCardResult"`
	}

	CheckOnlineClientCardResponseBodyResponseResult struct {
		Text       string  `xml:",chardata"`
		Comment    *string `xml:"COMMENT,omitempty"`
		Status     int32   `xml:"STATUS"`
		UID        string  `xml:"UID"`
		User       string  `xml:"USER"`
		BsClientID string  `xml:"BSCLIENTID"`
	}
)

func NewCheckOnlineClientCardRequest(request *CheckOnlineClientCardRequestBodyCardRequest) *CheckOnlineClientCardRequestEnvelope {
	if request == nil {
		return nil
	}

	return &CheckOnlineClientCardRequestEnvelope{
		XMLName: xml.Name{},
		Text:    "",
		Xsi:     "http://www.w3.org/2001/XMLSchema-instance",
		Xsd:     "http://www.w3.org/2001/XMLSchema",
		Soap:    "http://schemas.xmlsoap.org/soap/envelope/",
		Body: CheckOnlineClientCardRequestBody{
			Text: "",
			CheckOnlineClientCard: CheckOnlineClientCardRequestBodyCard{
				Text:    "",
				Xmlns:   "http://p-s.kz/",
				Request: *request,
			},
		},
	}
}
