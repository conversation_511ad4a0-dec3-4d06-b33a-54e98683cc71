package aml

import (
	"bytes"
	"context"
	"fmt"
	"io"
	"net/http"

	"git.redmadrobot.com/backend-go/rmr-pkg/core/errs"
	"git.redmadrobot.com/backend-go/rmr-pkg/core/logs"
	"git.redmadrobot.com/backend-go/rmr-pkg/tools/serial"

	"git.redmadrobot.com/zaman/backend/zaman/pkg/aml/entity"
)

const (
	amlCheckOnlineClientCardSoapAction = "http://p-s.kz/CheckOnlineClientCard"
	amlCheckOnlineClientCardEndpoint   = "/AMLOnline/Operations.asmx?op=CheckOnlineClientCard"
)

func (p *AMLProviderImpl) CheckOnlineClientCard(ctx context.Context, params *entity.CheckOnlineClientCardRequestBodyCardRequest) (*entity.CheckOnlineClientCardResponse, error) {
	reqBody := entity.NewCheckOnlineClientCardRequest(params)
	if reqBody == nil {
		return nil, fmt.Errorf("empty request body")
	}

	var buf bytes.Buffer
	if err := serial.XMLEncode(&buf, reqBody); err != nil {
		return nil, errs.Wrapf(err, "failed to encode request body")
	}

	reqXML := buf.String()
	logs.FromContext(ctx).Debug().
		Str("aml_request_xml", reqXML).
		Msg("AML XML Request")

	req, err := http.NewRequestWithContext(
		ctx,
		http.MethodPost,
		p.getRequestCheckOnlineClientCardURL(),
		&buf,
	)
	if err != nil {
		return nil, errs.Wrapf(err, "failed to create new http request")
	}

	req.Header.Set("Content-Type", "text/xml;charset=UTF-8")
	req.Header.Set("SOAPAction", amlCheckOnlineClientCardSoapAction)

	resp, err := p.HTTPClient.Do(req)
	if err != nil {
		return nil, errs.Wrapf(err, "failed to do http request")
	}
	defer func(Body io.ReadCloser) {
		err := Body.Close()
		if err != nil {
			logs.FromContext(ctx).Error().Msgf("failed to close response body: %v", err)
		}
	}(resp.Body)

	// TODO: удалить после дебагинга
	bodyBytes, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, errs.Wrapf(err, "failed to read response body")
	}
	logs.FromContext(ctx).Debug().
		Str("aml_response_xml", string(bodyBytes)).
		Msg("AML XML Response")
	resp.Body = io.NopCloser(bytes.NewBuffer(bodyBytes))

	var decodedResp entity.CheckOnlineClientCardResponse
	if resp.StatusCode != http.StatusOK {
		errRespString, err := handleAmlRespErrStatusCode(ctx, resp)
		if err != nil {
			return nil, err
		}

		decodedResp.ErrorResp = fmt.Sprintf("erorr_resp: %s; status_code: %d", errRespString, resp.StatusCode)
		return &decodedResp, ErrInternalServerError
	}

	decodedResp, err = serial.XMLDecode[entity.CheckOnlineClientCardResponse](resp.Body)
	if err != nil {
		return nil, errs.Wrapf(err, "failed to decode http response body")
	}

	return &decodedResp, nil
}

func (p *AMLProviderImpl) getRequestCheckOnlineClientCardURL() string {
	return fmt.Sprintf("%s%s", p.BaseURL, amlCheckOnlineClientCardEndpoint)
}

func handleAmlRespErrStatusCode(_ context.Context, resp *http.Response) (string, error) {
	bodyBytes, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", fmt.Errorf("failed to read response body for CheckOnlineClientCard request: %w", err)
	}
	defer resp.Body.Close()

	return string(bodyBytes), nil
}
