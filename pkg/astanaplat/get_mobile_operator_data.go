package astanaplat

import (
	"context"
	"errors"

	aperrs "git.redmadrobot.com/zaman/backend/zaman/errs/apBridge"
	"git.redmadrobot.com/zaman/backend/zaman/pkg/astanaplat/entity"
)

func (p *AstanaPlatProviderImpl) GetMobileOperatorData(
	ctx context.Context, phone string,
) (*entity.GetMobileOperatorDataResponseBody, error) {
	re := RequestExecutor[entity.GetMobileOperatorDataResponseBody]{
		p: p,
		createRequestFunc: func() entity.Request {
			return p.requestBuilder.NewGetMobileOperatorDataRequest(phone)
		},
	}

	response, err := re.Exec(ctx, true)
	if err != nil {
		targetErrWithBody := &entity.ErrorStatusBody{}
		if errors.As(err, targetErrWithBody) {
			if targetErrWithBody.Status == entity.ErrorCodeNotFound {
				return nil, aperrs.ApBridgeErrs().PhoneNotFoundError()
			}

			return nil, targetErrWithBody
		}

		return nil, err
	}

	return response, nil
}
