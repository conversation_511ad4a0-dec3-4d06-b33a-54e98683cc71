package astanaplat

import (
	"bytes"
	"context"
	"encoding/xml"
	"errors"
	"fmt"
	"io"
	"net/http"

	"git.redmadrobot.com/backend-go/rmr-pkg/core/logs"
	"git.redmadrobot.com/backend-go/rmr-pkg/httpx"

	"git.redmadrobot.com/zaman/backend/zaman/pkg/astanaplat/entity"
)

type RequestExecutor[R any] struct {
	p                 *AstanaPlatProviderImpl
	createRequestFunc func() entity.Request
}

func (re *RequestExecutor[R]) Exec(ctx context.Context, isFirst bool) (*R, error) {
	var buf bytes.Buffer
	if err := xml.NewEncoder(&buf).Encode(re.createRequestFunc()); err != nil {
		return nil, fmt.Errorf("failed encode request, err: %w", err)
	}

	logs.FromContext(ctx).Debug().Msgf("request: %s", buf.String())

	req, err := http.NewRequestWithContext(ctx, http.MethodPost, re.p.baseURL.String(), &buf)
	if err != nil {
		return nil, fmt.Errorf("failed create new request with context, err: %w", err)
	}

	req.Header.Set(httpx.HeaderContentType, httpx.ContentTypeXML)

	resp, err := re.p.HTTPClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to do request, err: %w", err)
	}

	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		bodyBytes, err := io.ReadAll(resp.Body)
		if err != nil {
			bodyBytes = []byte(err.Error())
		}

		return nil, fmt.Errorf("error status of http response [%d]: %s", resp.StatusCode, bodyBytes)
	}

	response, err := entity.DecodeResponse[R](resp.Body)
	if errors.Is(err, entity.ErrAuthenticationRequired) && isFirst {
		updateErr := re.p.sessionUpdater.update(ctx)
		if updateErr != nil && !errors.Is(updateErr, errUpdateSessionLocked) {
			// Если не пролизошла ошибка брокировки то значит произошла ошибка при обновлении токена
			return nil, fmt.Errorf("failed update session token, err = %w", updateErr)
		}

		// Пробуем повторно произвести запрос
		return re.Exec(
			ctx,
			false,
		)
	}

	if err != nil {
		return nil, fmt.Errorf("failed decode response, err: %w", err)
	}

	return response, nil
}
