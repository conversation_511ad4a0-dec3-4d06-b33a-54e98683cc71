package astanaplat

import (
	"context"

	"git.redmadrobot.com/zaman/backend/zaman/pkg/astanaplat/entity"
)

func (p *AstanaPlatProviderImpl) CreatePayment(
	ctx context.Context, numTrans, account, serviceID, amount, commission, date string,
) (*entity.CreatePaymentResponseBodyCheck, error) {
	re := RequestExecutor[entity.CreatePaymentResponseBody]{
		p: p,
		createRequestFunc: func() entity.Request {
			return p.requestBuilder.NewCreatePaymentRequest(
				numTrans, account, serviceID, amount, commission, date,
			)
		},
	}

	response, err := re.Exec(ctx, true)
	if err != nil {
		return nil, err
	}

	return &response.Payments.Check, nil
}
