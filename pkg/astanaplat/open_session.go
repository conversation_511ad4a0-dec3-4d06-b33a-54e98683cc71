package astanaplat

import (
	"bytes"
	"context"
	"encoding/xml"
	"fmt"
	"io"
	"net/http"

	"git.redmadrobot.com/backend-go/rmr-pkg/httpx"

	"git.redmadrobot.com/zaman/backend/zaman/pkg/astanaplat/entity"
)

func (p *AstanaPlatProviderImpl) openSession(ctx context.Context) (string, error) {
	var buf bytes.Buffer
	if err := xml.NewEncoder(&buf).Encode(p.requestBuilder.NewOpenSessionRequest()); err != nil {
		return "", fmt.Errorf("failed encode open session request, err: %w", err)
	}

	req, err := http.NewRequestWithContext(ctx, http.MethodPost, p.baseURL.String(), &buf)
	if err != nil {
		return "", fmt.Errorf("failed create new request with context, err: %w", err)
	}

	req.Header.Set(httpx.HeaderContentType, httpx.ContentTypeXML)

	resp, err := p.HTTPClient.Do(req)
	if err != nil {
		return "", fmt.Errorf("failed to do request for open session, err: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		bodyBytes, err := io.ReadAll(resp.Body)
		if err != nil {
			bodyBytes = []byte(err.Error())
		}

		return "", fmt.Errorf("error status of http response [%d]: %s", resp.StatusCode, bodyBytes)
	}

	session, err := entity.DecodeResponse[entity.OpenSessionResponseBody](resp.Body)
	if err != nil {
		return "", fmt.Errorf("failed decode response, err: %w", err)
	}

	return session.IDT.GUID, nil
}
