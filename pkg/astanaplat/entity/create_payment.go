package entity

type (
	CreatePaymentBody struct {
		Payments CreatePaymentBodyPayments `xml:"payments"`
	}

	CreatePaymentBodyPayments struct {
		Payment CreatePaymentBodyPayment `xml:"payment"`
	}

	CreatePaymentBodyPayment struct {
		TerminalID string `xml:"idTerminal"`
		NumTrans   string `xml:"numTrans"`
		Account    string `xml:"account"`
		ServiceID  string `xml:"idService"`
		Amount     string `xml:"amount"`
		Commission string `xml:"comission"` //nolint
		Date       string `xml:"date"`
	}
)

func NewCreatePaymentBody(
	terminalID, numTrans, account, serviceID, amount, commission, date string,
) CreatePaymentBody {
	return CreatePaymentBody{
		Payments: CreatePaymentBodyPayments{
			Payment: CreatePaymentBodyPayment{
				TerminalID: terminalID,
				NumTrans:   numTrans,
				Account:    account,
				ServiceID:  serviceID,
				Amount:     amount,
				Commission: commission,
				Date:       date,
			},
		},
	}
}

type (
	CreatePaymentResponseBody struct {
		Payments CreatePaymentResponseBodyPayments `xml:"payments"`
	}

	CreatePaymentResponseBodyPayments struct {
		Check CreatePaymentResponseBodyCheck `xml:"check"`
	}

	CreatePaymentResponseBodyCheck struct {
		TerminalID string  `xml:"idTerminal"`
		NumTrans   string  `xml:"numTrans"`
		Finally    int     `xml:"finally"`
		Date       *string `xml:"date"`
		Status     *int32  `xml:"status,omitempty"`
	}
)
