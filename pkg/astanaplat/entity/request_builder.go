package entity

import "sync"

type RequestBuilder struct {
	mu           *sync.RWMutex
	sessionToken string

	terminalID string
	userNumber string
	userPwd    string
}

func NewRequestBuilder(terminalID, userNumber, userPwd string) *RequestBuilder {
	return &RequestBuilder{
		mu: &sync.RWMutex{},

		terminalID: terminalID,
		userNumber: userNumber,
		userPwd:    userPwd,
	}
}

// LockGettingSessionToken - метод нужен для блокирования чтения устаревшего токена методом getSessionTokenPtr
func (rb *RequestBuilder) LockGettingSessionToken() {
	rb.mu.Lock()
}

// SetNewSessionTokenWithUnlock - метод обновляет токен и разблокирует создание новых запросов
func (rb *RequestBuilder) SetNewSessionTokenWithUnlock(st string) {
	rb.sessionToken = st
	rb.mu.Unlock()
}

func (rb *RequestBuilder) UnLockGettingSessionToken() {
	rb.mu.Unlock()
}

// getSessionTokenPtr - получение токена сессии, блокируется если токен находится в обновлении
func (rb *RequestBuilder) getSessionTokenPtr() *string {
	rb.mu.RLock()
	st := rb.sessionToken
	rb.mu.RUnlock()
	return &st
}

func (rb *RequestBuilder) NewOpenSessionRequest() Request {
	return Request{
		Type: requestTypeOpenSession,
		Body: NewOpenSessionBody(rb.terminalID, rb.userNumber, rb.userPwd),
	}
}

func (rb *RequestBuilder) NewGetMobileOperatorDataRequest(phone string) Request {
	return Request{
		Type:         requestTypeGetMobileOperatorData,
		SessionToken: rb.getSessionTokenPtr(),
		Body:         NewGetMobileOperatorDataBody(phone),
	}
}

func (rb *RequestBuilder) NewCheckPaymentStatusRequest(numTrans string) Request {
	return Request{
		Type:         requestTypePayments,
		SessionToken: rb.getSessionTokenPtr(),
		Body: NewCheckPaymentStatusBody(
			rb.terminalID,
			numTrans,
		),
	}
}

func (rb *RequestBuilder) NewCreatePaymentRequest(
	numTrans, account, serviceID, amount, commission, date string,
) Request {
	return Request{
		Type:         requestTypePayments,
		SessionToken: rb.getSessionTokenPtr(),
		Body: NewCreatePaymentBody(
			rb.terminalID,
			numTrans,
			account,
			serviceID,
			amount,
			commission,
			date,
		),
	}
}
