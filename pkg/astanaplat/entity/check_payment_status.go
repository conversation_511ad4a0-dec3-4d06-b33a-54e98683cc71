package entity

type (
	CheckPaymentStatusBody struct {
		Payments CheckPaymentStatusBodyPayments `xml:"payments"`
	}

	CheckPaymentStatusBodyPayments struct {
		Check CheckPaymentStatusBodyCheck `xml:"check"`
	}

	CheckPaymentStatusBodyCheck struct {
		TerminalID string `xml:"idTerminal"`
		NumTrans   string `xml:"numTrans"`
	}
)

func NewCheckPaymentStatusBody(terminalID, numTrans string) CheckPaymentStatusBody {
	return CheckPaymentStatusBody{
		Payments: CheckPaymentStatusBodyPayments{
			Check: CheckPaymentStatusBodyCheck{
				TerminalID: terminalID,
				NumTrans:   numTrans,
			},
		},
	}
}

type (
	CheckPaymentStatusResponseBody struct {
		Payments CheckPaymentStatusResponseBodyPayments `xml:"payments"`
	}

	CheckPaymentStatusResponseBodyPayments struct {
		Check CheckPaymentStatusResponseBodyCheck `xml:"check"`
	}

	CheckPaymentStatusResponseBodyCheck struct {
		TerminalID string `xml:"idTerminal"`
		NumTrans   string `xml:"numTrans"`
		Finally    int    `xml:"finally"`
		Date       string `xml:"date"`
		Status     *int32 `xml:"status"`
	}
)
