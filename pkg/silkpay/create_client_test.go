package silkpay

import (
	"context"
	"encoding/json"
	"github.com/stretchr/testify/require"
	"io"
	"net/http"
	"net/http/httptest"
	"testing"
)

func TestSilkPayClient_CreateClientAccountAndCard(t *testing.T) {
	// Arrange
	expectedResp := CreateClientAccountAndCardResponse{
		Pan:        "529360******6161",
		EmbossName: "Test17 Test17",
		ExpiryDate: "1230",
		Status:     "Active",
	}
	testUserIIN := "************"

	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		require.Equal(t, "application/json", r.Header.Get("Content-Type"))

		body, err := io.ReadAll(r.Body)
		require.NoError(t, err)
		var req CreateClientAccountAndCardRequest
		err = json.Unmarshal(body, &req)
		require.NoError(t, err)

		require.Equal(t, req.Person.Iin, testUserIIN)

		w.WriteHeader(http.StatusOK)
		_ = json.NewEncoder(w).Encode(expectedResp)
	}))
	defer server.Close()

	client := &SilkPayClient{
		BaseURL:    server.URL,
		HTTPClient: server.Client(),
	}

	req := CreateClientAccountAndCardRequest{
		Person: Person{
			Iin: testUserIIN,
		},
		AccountDetails: AccountDetails{},
		CardDetails:    CardDetails{},
	}

	// Act
	resp, err := client.CreateClientAccountAndCard(context.Background(), req)

	// Assert
	require.NoError(t, err)
	require.NotNil(t, resp)
	require.Equal(t, expectedResp.Pan, resp.Pan)
	require.Equal(t, expectedResp.EmbossName, resp.EmbossName)
	require.Equal(t, expectedResp.ExpiryDate, resp.ExpiryDate)
	require.Equal(t, expectedResp.Status, resp.Status)
}

func TestSilkPayClient_CreateClientAccountAndCard_BadRequest(t *testing.T) {
	// Arrange
	expectedResp := map[string]string{
		"error": "bad_request",
	}
	testUserIIN := "************"

	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		require.Equal(t, "application/json", r.Header.Get("Content-Type"))

		body, err := io.ReadAll(r.Body)
		require.NoError(t, err)
		var req CreateClientAccountAndCardRequest
		err = json.Unmarshal(body, &req)
		require.NoError(t, err)

		require.Equal(t, req.Person.Iin, testUserIIN)

		w.WriteHeader(http.StatusBadRequest)
		_ = json.NewEncoder(w).Encode(expectedResp)
	}))
	defer server.Close()

	client := &SilkPayClient{
		BaseURL:    server.URL,
		HTTPClient: server.Client(),
	}

	req := CreateClientAccountAndCardRequest{
		Person: Person{
			Iin: testUserIIN,
		},
		AccountDetails: AccountDetails{},
		CardDetails:    CardDetails{},
	}

	// Act
	resp, err := client.CreateClientAccountAndCard(context.Background(), req)

	// Assert
	require.NotNil(t, err)
	require.Nil(t, resp)
}
