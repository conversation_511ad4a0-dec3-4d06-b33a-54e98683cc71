package silkpay

import (
	"context"
	"net/http"
	"time"
)

var _ SilkpayProvider = (*SilkPayClient)(nil)

const (
	createClientAccountAndCardReqURLPath = "/processing/api/v3/clients"
)

type (
	SilkpayProvider interface {
		Authenticate(ctx context.Context) (*AuthenticateResponse, error)
		CreateClientAccountAndCard(ctx context.Context, request CreateClientAccountAndCardRequest) (*CreateClientAccountAndCardResponse, error)
	}

	SilkPayClient struct {
		HTTPClient         *http.Client
		BaseURL            string
		EncodedCredentials string
	}
)

func NewSilkPayClient(cfg *Config) *SilkPayClient {
	httpClient := &http.Client{
		Timeout: time.Duration(cfg.RequestTimeoutSeconds) * time.Second,
	}

	return &SilkPayClient{
		HTTPClient: httpClient,
		BaseURL:    cfg.BaseURL,
	}
}
