package silkpay

type (
	CreateClientAccountAndCardRequest struct {
		Person         Person         `json:"person"`
		AccountDetails AccountDetails `json:"accountDetails"`
		CardDetails    CardDetails    `json:"cardDetails"`
		AccessToken    string
	}

	AccountDetails struct {
		ContractTypeRid  string `json:"contractTypeRid"`
		ContractRid      string `json:"contractRid"`
		ContractCurrency string `json:"contractCurrency"`
		Iban             string `json:"iban"`
	}

	CardDetails struct {
		ContractTypeRid string          `json:"contractTypeRid"`
		ContractRid     string          `json:"contractRid"`
		CardParams      CardParams      `json:"cardParams"`
		DeliveryAddress DeliveryAddress `json:"deliveryAddress"`
		Attrs           []Attr          `json:"attrs"`
	}

	Attr struct {
		Key   string `json:"key"`
		Value string `json:"value"`
	}

	CardParams struct {
		ExpDate    string `json:"expDate"`
		EmbossName string `json:"embossName"`
		Status     string `json:"status"`
	}

	DeliveryAddress struct {
		CountryID int64  `json:"countryId"`
		City      string `json:"city"`
		Street    string `json:"street"`
		House     string `json:"house"`
	}

	Person struct {
		Iin             string          `json:"iin"`
		Rid             string          `json:"rid"`
		LastName        string          `json:"lastName"`
		FirstName       string          `json:"firstName"`
		MiddleName      string          `json:"middleName"`
		ScreenName      string          `json:"screenName"`
		Gender          string          `json:"gender"`
		BirthDate       string          `json:"birthDate"`
		MobilePhone     string          `json:"mobilePhone"`
		WorkPhone       string          `json:"workPhone"`
		Email           string          `json:"email"`
		Citizenship     int64           `json:"citizenship"`
		BirthPlace      string          `json:"birthPlace"`
		BirthName       string          `json:"birthName"`
		LastNameLat     string          `json:"lastNameLat"`
		FirstNameLat    string          `json:"firstNameLat"`
		EmployerTitle   string          `json:"employerTitle"`
		Notes           string          `json:"notes"`
		RiskLevel       string          `json:"riskLevel"`
		Document        Document        `json:"document"`
		LanguageCode    string          `json:"languageCode"`
		ResidentAddress ResidentAddress `json:"residentAddress"`
	}

	Document struct {
		Type      string `json:"type"`
		Number    string `json:"number"`
		IssueDate string `json:"issueDate"`
		ExpDate   string `json:"expDate"`
		Issuer    string `json:"issuer"`
	}

	ResidentAddress struct {
		CountryID     int64  `json:"countryId"`
		CityRid       string `json:"cityRid"`
		CityTitle     string `json:"cityTitle"`
		AddressInCity string `json:"addressInCity"`
		Zip           string `json:"zip"`
	}

	CreateClientAccountAndCardResponse struct {
		Pan        string `json:"pan"`
		EmbossName string `json:"embossName"`
		ExpiryDate string `json:"expiryDate"`
		Status     string `json:"status"`
	}

	AuthenticateResponse struct {
		AccessToken string `json:"access_token"`
		TokenType   string `json:"token_type"`
		ExpiresIn   int    `json:"expires_in"`
	}
)
