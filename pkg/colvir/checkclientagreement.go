package colvir

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"net/url"

	"git.redmadrobot.com/zaman/backend/zaman/pkg/colvir/entity"
)

// RequestCheckClientAgreement Метод загрузки информации о статусе договора РКО
func (p *ColvirProviderImpl) RequestCheckClientAgreement(
	ctx context.Context, request entity.CheckClientAgreementRequest) (*entity.CheckClientAgreementResponse, error) {
	targetURL, err := p.getRequestCheckClientAgreementURL()
	if err != nil {
		return nil, err
	}

	var buf bytes.Buffer
	if err := json.NewEncoder(&buf).Encode(request); err != nil {
		return nil, fmt.Errorf("failed to encode request body for create client agreement request: %w", err)
	}

	req, err := http.NewRequestWithContext(ctx, http.MethodGet, targetURL, &buf)
	if err != nil {
		return nil, fmt.Errorf("failed to create request for create client agreement request: %w", err)
	}
	req.Header.Set("Content-Type", "application/json")

	resp, err := p.HTTPClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to do request for create client agreement request: %w", err)
	}
	defer resp.Body.Close()

	var respBody entity.CheckClientAgreementResponse
	if resp.StatusCode != http.StatusOK {
		errResp, err := p.handleColvirRespErrStatusCode(ctx, resp)
		if err != nil {
			return nil, err
		}

		respBody.ColvirError = errResp
		return &respBody, ErrInternalServerError
	}

	var response []entity.CheckClientAgreement
	if err = json.NewDecoder(resp.Body).Decode(&response); err != nil {
		return nil, fmt.Errorf("failed to decode response for create client agreement request: %w", err)
	}

	respBody.CheckClientAgreement = response
	return &respBody, nil
}

func (p *ColvirProviderImpl) getRequestCheckClientAgreementURL() (string, error) {
	result, err := url.JoinPath(p.V2BaseURL, "/dearko/loadStatus")
	if err != nil {
		return "", fmt.Errorf("failed to join path for create client agreement request: %w", err)
	}

	return result, nil
}
