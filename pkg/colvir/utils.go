package colvir

import (
	"encoding/json"
	"fmt"
	"net/http"

	"git.redmadrobot.com/zaman/backend/zaman/pkg/colvir/entity"
)

func handleStatusCode(statusCode int, bodyBytes []byte) error {
	switch {
	case statusCode == http.StatusGatewayTimeout:
		return ErrStatusGatewayTimeout
	default:
		return fmt.Errorf("error status of http response [%d]: %s", statusCode, bodyBytes)
	}
}

func handleColvirErrorResp(statusCode int, bodyBytes []byte) (*entity.ColvirErrResp, error) {
	var colvirErrResp entity.ColvirErrResp
	if err := json.Unmarshal(bodyBytes, &colvirErrResp); err != nil {
		return nil, fmt.Errorf("error status of http response [%d]: %s", statusCode, bodyBytes)
	}

	return &colvirErrResp, nil
}
