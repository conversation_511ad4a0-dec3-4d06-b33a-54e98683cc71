package colvir

import (
	"context"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strconv"

	"git.redmadrobot.com/backend-go/rmr-pkg/core/errs"
	"git.redmadrobot.com/backend-go/rmr-pkg/core/logs"
	"git.redmadrobot.com/backend-go/rmr-pkg/tools/serial"

	"git.redmadrobot.com/zaman/backend/zaman/pkg/colvir/entity"
)

const (
	colvirLoanCalcLoadPreScheduleEndpoint = "/loancalc/loadPreSchedule"
)

func (p *ColvirProviderImpl) LoanCalcLoadPreSchedule(ctx context.Context, req *entity.LoanCalcLoadPreScheduleReq) (*entity.LoanCalcLoadPreScheduleResp, error) {
	logger := logs.FromContext(ctx)

	queryParams := url.Values{}
	queryParams.Add("productCode", req.ProductCode)
	queryParams.Add("beginDate", req.BeginDate)
	queryParams.Add("endDate", req.EndDate)
	queryParams.Add("percentRate", req.PercentRate.String())
	if req.ThirdExpenses != nil {
		queryParams.Add("thirdExpenses", req.ThirdExpenses.String())
	}
	if req.ClientCode != nil {
		queryParams.Add("clientCode", *req.ClientCode)
	}
	if req.JurFl != nil {
		queryParams.Add("jurFl", strconv.FormatBool(*req.JurFl))
	}
	if req.EnterpreneurFl != nil {
		queryParams.Add("enterpreneurFl", strconv.FormatBool(*req.EnterpreneurFl))
	}
	queryParams.Add("contractSum", req.ContractSum.String())
	if req.CurrencyCode != nil {
		queryParams.Add("currencyCode", *req.CurrencyCode)
	}
	queryParams.Add("payDay", strconv.Itoa(int(req.PayDay)))
	if req.GracePoints != nil {
		queryParams.Add("gracePoints", strconv.Itoa(int(*req.GracePoints)))
	}
	if req.GracePointsPrc != nil {
		queryParams.Add("gracePointsPrc", strconv.Itoa(int(*req.GracePointsPrc)))
	}
	if req.TarifCode != nil {
		queryParams.Add("tarifCode", *req.TarifCode)
	}
	logger.Debug().Interface("queryParams", queryParams).Send()

	reqURL := p.getRequestLoanCalcLoadPreScheduleURL(queryParams)
	logger.Debug().Msgf("reqURL: %s", reqURL)
	httpReq, err := http.NewRequestWithContext(ctx, http.MethodGet, reqURL, nil)
	if err != nil {
		return nil, errs.Wrapf(err, "failed to create new http request")
	}

	httpReq.Header.Set("Content-Type", "application/json;charset=UTF-8")

	resp, err := p.HTTPClient.Do(httpReq)
	if err != nil {
		return nil, errs.Wrapf(err, "failed to do http request")
	}
	defer func(body io.ReadCloser) {
		err := body.Close()
		if err != nil {
			logs.FromContext(ctx).Error().Msgf("failed to close response body: %v", err)
		}
	}(resp.Body)

	if resp.StatusCode != http.StatusOK {
		errResp, err := p.handleColvirRespErrStatusCode(ctx, resp)
		if err != nil {
			return nil, err
		}

		return &entity.LoanCalcLoadPreScheduleResp{
			ColvirError: errResp,
		}, ErrInternalServerError
	}

	// TODO: удалить после дебагинга
	// bodyBytes, err := io.ReadAll(resp.Body)
	// if err != nil {
	// 	return nil, errs.Wrapf(err, "failed to read response body")
	// }
	// log.Println(string(bodyBytes))
	// resp.Body = io.NopCloser(bytes.NewBuffer(bodyBytes))

	decodedResp, err := serial.JSONDecode[entity.LoanCalcLoadPreScheduleResp](resp.Body)
	if err != nil {
		return nil, errs.Wrapf(err, "failed to decode http response body")
	}
	if decodedResp.ColvirError != nil {
		return nil, errs.Wrapf(err, "failed to decode http response body")
	}
	logger.Debug().Interface("decodedResp", decodedResp).Send()

	return &decodedResp, nil
}

func (p *ColvirProviderImpl) getRequestLoanCalcLoadPreScheduleURL(queryParams url.Values) string {
	return fmt.Sprintf("%s%s?%s", p.V2BaseURL, colvirLoanCalcLoadPreScheduleEndpoint, queryParams.Encode())
}
