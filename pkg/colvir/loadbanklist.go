package colvir

import (
	"bytes"
	"context"
	"fmt"
	"io"
	"net/http"

	"git.redmadrobot.com/backend-go/rmr-pkg/core/errs"
	"git.redmadrobot.com/backend-go/rmr-pkg/tools/serial"

	"git.redmadrobot.com/zaman/backend/zaman/pkg/colvir/entity"
)

const (
	colvirLoadBankListEndpoint = "/cxf/domain/v1"
)

func (p *ColvirProviderImpl) RequestLoadBankList(
	ctx context.Context,
) (*entity.LoadBankListResponse, error) {
	var buf bytes.Buffer
	if err := serial.XMLEncode(&buf, entity.NewLoadBankListRequest()); err != nil {
		return nil, errs.Wrapf(err, "failed to encode request body")
	}

	req, err := http.NewRequestWithContext(
		ctx,
		http.MethodPost,
		p.getRequestLoadBankListURL(),
		bytes.NewBuffer(buf.Bytes()),
	)
	if err != nil {
		return nil, errs.Wrapf(err, "failed to create new http request")
	}

	req.Header.Set("Content-Type", "application/xml;charset=UTF-8")

	resp, err := p.HTTPClient.Do(req)
	if err != nil {
		return nil, errs.Wrapf(err, "failed to do http request")
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		bodyBytes, err := io.ReadAll(resp.Body)
		if err != nil {
			bodyBytes = []byte(err.Error())
		}
		return nil, handleStatusCode(resp.StatusCode, bodyBytes)
	}

	bankListResponse, err := serial.XMLDecode[entity.LoadBankListResponse](resp.Body)
	if err != nil {
		return nil, errs.Wrapf(err, "failed to decode http response body")
	}

	if bankListResponse.Body.LoadBankListResponse.Errors != nil {
		return nil, bankListResponse.Body.LoadBankListResponse.Errors
	}

	return &bankListResponse, nil
}

func (p *ColvirProviderImpl) getRequestLoadBankListURL() string {
	return fmt.Sprintf("%s%s", p.BaseURL, colvirLoadBankListEndpoint)
}
