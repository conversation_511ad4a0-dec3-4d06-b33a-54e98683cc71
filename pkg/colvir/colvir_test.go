package colvir

import (
	"context"
	"encoding/json"
	"encoding/xml"
	"fmt"
	"net/http"
	"net/http/httptest"
	"os"
	"reflect"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"

	"git.redmadrobot.com/backend-go/rmr-pkg/httpx"
	"git.redmadrobot.com/backend-go/rmr-pkg/tools/serial"
	"github.com/google/uuid"
	"github.com/stretchr/testify/require"

	"git.redmadrobot.com/zaman/backend/zaman/pkg/colvir/entity"
)

var (
	createClientExampleErrResponse = entity.ColvirErrResp{
		Code:        10612,
		QCTXID:      "************",
		Text:        "Запись с данным идентификатором request_id = 0ada77f7-98b1-4241-81e7-c1ebf986b2ad уже существует в журнале АБС",
		Description: "Запись с данным идентификатором request_id уже существует в журнале АБС.",
		HTTPCode:    500,
		Module:      "ibclients",
		Hint: entity.Hint{
			Reason: "ORA-00",
		},
		Level: entity.Level{
			Type:  "CRITICAL",
			Value: 0,
		},
		EDate: "2025-02-13T15:45:32",
	}
)

func TestColvirImpl_RequestTaxPayer(t *testing.T) {
	taxPayer := `<soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
    <soap:Body>
        <cl:findTaxPayerResponseElem xmlns:a="http://bus.colvir.com/common/about/v1" xmlns:q="http://bus.colvir.com/common/query/v1" xmlns:b="http://bus.colvir.com/common/basis/v1" xmlns:s="http://bus.colvir.com/common/support/v1" xmlns:f="http://bus.colvir.com/common/forms/v1" xmlns:cl="http://bus.colvir.com/service/clients/v1" xmlns:dm="http://bus.colvir.com/common/domain/v1">
            <s:code>0</s:code>
            <s:responseTime>12</s:responseTime>
            <s:responseDbTime>6</s:responseDbTime>
            <s:requestId>6A8A44AE-924D-409E-B07D-E56F7D564A65</s:requestId>
            <s:route>cbs@93.177.106.6:6502</s:route>
            <cl:title>СЕРКИБАЕВ АЗЛХАН АМАНГЕЛЬДИНОВИЧ</cl:title>
            <cl:titlePhysical>СЕРКИБАЕВ АЗЛХАН АМАНГЕЛЬДИНОВИЧ</cl:titlePhysical>
            <cl:titleLegal>ИП "АЗАМАТ"</cl:titleLegal>
            <cl:typeTaxpayer>0</cl:typeTaxpayer>
            <cl:flNotResident>0</cl:flNotResident>
            <cl:typePP>32</cl:typePP>
            <cl:flInactive>0</cl:flInactive>
            <cl:taxAuthorityCode>321</cl:taxAuthorityCode>
        </cl:findTaxPayerResponseElem>
    </soap:Body>
</soap:Envelope>`

	mockServer := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		if r.Header.Get("Content-Type") != "application/xml;charset=UTF-8" {
			http.Error(w, "invalid content type", http.StatusUnsupportedMediaType)
			return
		}

		w.WriteHeader(http.StatusOK)
		w.Header().Set("Content-Type", httpx.ContentTypeXML)
		_, err := w.Write([]byte(taxPayer))
		require.NoError(t, err)
	}))
	defer mockServer.Close()

	colvir := &ColvirProviderImpl{
		HTTPClient: &http.Client{},
		BaseURL:    mockServer.URL,
	}

	t.Run("RequestTaxPayer", func(t *testing.T) {
		got, err := colvir.RequestTaxPayer(context.Background(), "tt.param")
		if err != nil {
			t.Errorf("RequestTaxPayer() error = %v", err)
			return
		}
		assert.Equal(t, *got.Body.FindTaxPayerElem.TitleLegal, "ИП \"АЗАМАТ\"")
		assert.Equal(t, got.Body.FindTaxPayerElem.Title, "СЕРКИБАЕВ АЗЛХАН АМАНГЕЛЬДИНОВИЧ")
		assert.Equal(t, got.Body.FindTaxPayerElem.TypeTaxpayer, 0)
		assert.Equal(t, got.Body.FindTaxPayerElem.Code, 0)
		assert.Equal(t, *got.Body.FindTaxPayerElem.TitlePhysical, "СЕРКИБАЕВ АЗЛХАН АМАНГЕЛЬДИНОВИЧ")
	})
}

func TestColvirImpl_LoadClientBankRelationLink(t *testing.T) {
	resp := &entity.LoadClientBankRelationLinkResponseEnvelope{
		XMLName: xml.Name{Local: "Envelope"},
		Body: entity.LoadClientBankRelationLinkResponseBody{
			LoadClientBankRelationLinkResponse: entity.LoadClientBankRelationLinkResponse{
				ColvirRequestID: uuid.New().String(),
				ExecuteResult: &entity.LoadClientBankRelationLinkExecuteResult{
					Code: "testCode",
					Name: "testName",
				},
				Result: &entity.LoadClientBankRelationLinkResult{
					Code:              "testCode",
					Name:              "testName",
					Description:       "testDescription",
					AffiliationFl:     "true",
					ColvirReferenceID: "testReferenceId",
					Client: entity.LoadClientBankRelationLinkClient{
						Code: "testCode",
						Name: "testName",
					},
					Nord:     "testNord",
					FromDate: "testFromData",
				},
				Errors: &entity.LoadClientBankRelationLinkErrors{
					Code:     "testCode",
					Message:  "testMessage",
					Severity: "testSeverity",
				},
			},
		},
	}

	mockServer := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		if r.Header.Get("Content-Type") != "text/xml;charset=UTF-8" {
			http.Error(w, "invalid content type", http.StatusUnsupportedMediaType)
			return
		}

		w.WriteHeader(http.StatusOK)
		w.Header().Set("Content-Type", "text/xml;charset=UTF-8")
		err := serial.XMLEncode(w, resp)
		require.NoError(t, err)
	}))
	defer mockServer.Close()

	colvir := &ColvirProviderImpl{
		HTTPClient: &http.Client{},
		BaseURL:    mockServer.URL,
	}

	t.Run("LoadClientBankRelationLink", func(t *testing.T) {
		got, err := colvir.LoadClientBankRelationLink(context.Background(), "testClientCode")
		if err != nil {
			t.Errorf("LoadClientBankRelationLink() error = %v", err)
			return
		}
		if !reflect.DeepEqual(got, resp) {
			t.Errorf("LoadClientBankRelationLink() got = %v, want %v", got, resp)
		}
	})
}

func TestColvirImpl_LoadClientBankRelationLink_Real(t *testing.T) {
	baseURL := os.Getenv("TEST_COLVIR_LOAD_CLIENT_BANK_RELATION_BASE_URL")
	if baseURL == "" {
		t.Skip("skip test")
	}
	clientCode := os.Getenv("TEST_COLVIR_LOAD_CLIENT_BANK_RELATION_CLIENT_CODE")
	if clientCode == "" {
		t.Log("client code is empty")
	}

	colvir := &ColvirProviderImpl{
		HTTPClient: &http.Client{},
		BaseURL:    baseURL,
	}

	t.Run("LoadClientBankRelationLink", func(t *testing.T) {
		got, err := colvir.LoadClientBankRelationLink(context.Background(), clientCode)
		if err != nil {
			t.Errorf("LoadClientBankRelationLink() error = %v", err)
			return
		}
		fmt.Printf("%+v\n", got)
	})
}

func TestColvirImpl_CreateClient(t *testing.T) {
	createClientExampleSuccessResponse := entity.CreateClientResp{
		ResultCode:  "0",
		ResultMsg:   "Ответ успешно сформирован",
		ClientCode:  "********",
		ClientDepID: "735",
		ClientID:    "23275",
		GBLErrs:     []entity.GBLError{},
	}

	mockServer := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		if r.Header.Get("Content-Type") != "application/json" {
			http.Error(w, "invalid content type", http.StatusUnsupportedMediaType)
			return
		}

		var reqBody entity.CreateClientRequest
		err := json.NewDecoder(r.Body).Decode(&reqBody)
		require.NoError(t, err)

		switch {
		case reqBody.FilialCode == "01":
			w.WriteHeader(http.StatusInternalServerError)
			w.Header().Set("Content-Type", "application/json")
			data, err := json.Marshal(createClientExampleErrResponse)
			require.NoError(t, err)

			_, err = w.Write(data)
			require.NoError(t, err)
		default:
			w.WriteHeader(http.StatusOK)
			w.Header().Set("Content-Type", "application/json")
			data, err := json.Marshal(createClientExampleSuccessResponse)
			require.NoError(t, err)

			_, err = w.Write(data)
			require.NoError(t, err)
		}
	}))
	defer mockServer.Close()

	t.Run("Success", func(t *testing.T) {
		colvir := &ColvirProviderImpl{
			HTTPClient: &http.Client{},
			V2BaseURL:  mockServer.URL,
		}

		got, err := colvir.RequestCreateClient(context.Background(), &entity.CreateClientRequest{
			RequestID:  uuid.New().String(),
			FilialCode: "00",
			LastName:   "Test",
			IIN:        "",
		})
		require.NoError(t, err)
		if !reflect.DeepEqual(got, &createClientExampleSuccessResponse) {
			t.Errorf("RequestCreateClient() got = %v, want %v", got, createClientExampleSuccessResponse)
		}
	})

	t.Run("Error", func(t *testing.T) {
		colvir := &ColvirProviderImpl{
			HTTPClient: &http.Client{},
			V2BaseURL:  mockServer.URL,
		}
		_, err := colvir.RequestCreateClient(context.Background(), &entity.CreateClientRequest{
			RequestID:  uuid.New().String(),
			FilialCode: "01",
			LastName:   "Test",
			IIN:        "",
		})
		require.NotNil(t, err)
	})
}

func TestColvirImpl_UpdateClient(t *testing.T) {
	exampleUserLastName := "Test"

	updateClientExampleSuccessResponse := entity.UpdateClientResp{
		ResultCode:  "0",
		ResultMsg:   "Ok!",
		ClientCode:  "123456789",
		ClientDepID: "1",
		ClientID:    "1",
	}

	updateClientExampleErrResponse := entity.UpdateClientResp{
		ResultCode:  "10605",
		ResultMsg:   "",
		ClientCode:  "",
		ClientDepID: "",
		ClientID:    "",
		GBLErrs: []entity.GBLError{
			{
				Code:     "10617",
				Status:   0,
				LongName: "",
				TxtErr:   nil,
			},
		},
	}

	mockServer := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		if r.Header.Get("Content-Type") != "application/json" {
			http.Error(w, "invalid content type", http.StatusUnsupportedMediaType)
			return
		}

		var reqBody entity.UpdateClientRequest
		err := json.NewDecoder(r.Body).Decode(&reqBody)
		require.NoError(t, err)

		switch {
		case reqBody.ClientDepID == "1":
			w.WriteHeader(http.StatusInternalServerError)
			w.Header().Set("Content-Type", "application/json")
			data, err := json.Marshal(updateClientExampleErrResponse)
			require.NoError(t, err)

			_, err = w.Write(data)
			require.NoError(t, err)
		default:
			w.WriteHeader(http.StatusOK)
			w.Header().Set("Content-Type", "application/json")
			data, err := json.Marshal(updateClientExampleSuccessResponse)
			require.NoError(t, err)

			_, err = w.Write(data)
			require.NoError(t, err)
		}
	}))
	defer mockServer.Close()

	t.Run("Success", func(t *testing.T) {
		colvir := &ColvirProviderImpl{
			HTTPClient: &http.Client{},
			V2BaseURL:  mockServer.URL,
		}

		got, err := colvir.RequestUpdateClient(context.Background(), &entity.UpdateClientRequest{
			RequestID:   uuid.New().String(),
			LastName:    &exampleUserLastName,
			ClientCode:  "123456789",
			ClientDepID: "11",
			ClientID:    "1",
			Addresses:   []entity.Address{},
			Contacts:    []entity.ContactUpdate{},
		})
		require.NoError(t, err)
		if !reflect.DeepEqual(got, &updateClientExampleSuccessResponse) {
			t.Errorf("RequestUpdateClient() got = %v, want %v", got, updateClientExampleSuccessResponse)
		}
	})

	t.Run("Error", func(t *testing.T) {
		colvir := &ColvirProviderImpl{
			HTTPClient: &http.Client{},
			V2BaseURL:  mockServer.URL,
		}
		_, err := colvir.RequestUpdateClient(context.Background(), &entity.UpdateClientRequest{
			RequestID:   uuid.New().String(),
			LastName:    &exampleUserLastName,
			ClientCode:  "123456789",
			ClientDepID: "1",
			ClientID:    "1",
			Addresses:   []entity.Address{},
			Contacts:    []entity.ContactUpdate{},
		})
		require.NotNil(t, err)
	})
}

func TestColvirImpl_FindClient(t *testing.T) {
	findClientExampleSuccessResponse := entity.FindClientResp{
		ResultCode: 0,
		ResultMsg:  "Успех",
		Clients: []entity.Clients{
			{
				ID:    "2392",
				DepID: "735",
				Code:  "00000545",
				State: "OPENED",
				Type:  "0",
			},
		},
	}

	findClientExampleNotFoundResponse := entity.FindClientResp{
		ResultCode: 10602,
		ResultMsg:  "Клиент не найден",
	}

	findClientExampleMissingParamResponse := entity.FindClientResp{
		ResultCode: 10624,
		ResultMsg:  "Не заполнен параметр %",
	}

	mockServer := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		if r.Header.Get("Content-Type") != "application/json" {
			http.Error(w, "invalid content type", http.StatusUnsupportedMediaType)
			return
		}

		var reqBody entity.FindClientReq
		err := json.NewDecoder(r.Body).Decode(&reqBody)
		require.NoError(t, err)

		switch reqBody.PersonIIN {
		case "720710301206":
			w.WriteHeader(http.StatusOK)
			w.Header().Set("Content-Type", "application/json")
			data, err := json.Marshal(findClientExampleSuccessResponse)
			require.NoError(t, err)

			_, err = w.Write(data)
			require.NoError(t, err)
		case "000000000000":
			w.WriteHeader(http.StatusOK)
			w.Header().Set("Content-Type", "application/json")
			data, err := json.Marshal(findClientExampleNotFoundResponse)
			require.NoError(t, err)

			_, err = w.Write(data)
			require.NoError(t, err)
		case "":
			w.WriteHeader(http.StatusOK)
			w.Header().Set("Content-Type", "application/json")
			data, err := json.Marshal(findClientExampleMissingParamResponse)
			require.NoError(t, err)

			_, err = w.Write(data)
			require.NoError(t, err)
		default:
			w.WriteHeader(http.StatusInternalServerError)
		}
	}))
	defer mockServer.Close()

	t.Run("Success", func(t *testing.T) {
		colvir := &ColvirProviderImpl{
			HTTPClient: &http.Client{},
			V2BaseURL:  mockServer.URL,
		}

		got, err := colvir.RequestFindClient(context.Background(), entity.FindClientReq{
			RequestID: "111111",
			PersonIIN: "720710301206",
		})
		require.NoError(t, err)
		if !reflect.DeepEqual(got, &findClientExampleSuccessResponse) {
			t.Errorf("RequestFindClient() got = %v, want %v", got, findClientExampleSuccessResponse)
		}
	})

	t.Run("ClientNotFound", func(t *testing.T) {
		colvir := &ColvirProviderImpl{
			HTTPClient: &http.Client{},
			V2BaseURL:  mockServer.URL,
		}

		got, err := colvir.RequestFindClient(context.Background(), entity.FindClientReq{
			RequestID: "111112",
			PersonIIN: "000000000000",
		})
		require.NoError(t, err)
		if !reflect.DeepEqual(got, &findClientExampleNotFoundResponse) {
			t.Errorf("RequestFindClient() got = %v, want %v", got, findClientExampleNotFoundResponse)
		}
	})

	t.Run("MissingParameter", func(t *testing.T) {
		colvir := &ColvirProviderImpl{
			HTTPClient: &http.Client{},
			V2BaseURL:  mockServer.URL,
		}

		got, err := colvir.RequestFindClient(context.Background(), entity.FindClientReq{
			RequestID: "111113",
		})
		require.NoError(t, err)
		if !reflect.DeepEqual(got, &findClientExampleMissingParamResponse) {
			t.Errorf("RequestFindClient() got = %v, want %v", got, findClientExampleMissingParamResponse)
		}
	})
}

func TestColvirImpl_RequestFindClientAccountsList(t *testing.T) {
	findClientAccountsListResponse := &entity.FindClientAccountsListResponse{
		XMLName: xml.Name{Local: "Envelope"},
		Body: &entity.LoadClientAccountsListElemResponseBody{
			Text: "text",
			LoadClientAccountsListResponseElem: entity.LoadClientAccountsListElemResponseElem{
				Code:           "0",
				ResponseTime:   "3978",
				ResponseDBTime: "2915",
				RequestID:      "84A0BE70-2960-49AC-9A12-C4A0EB4EEF3E",
				Route:          "cbs@localhost:8181",
				AccountsList: entity.AccountsList{
					ClientAccountsListItem: &entity.ClientAccountsListItem{
						Text:       "text",
						ClientCode: "0117.0013584",
						ClientAccounts: []entity.ClientAccounts{
							{
								ClientCode: "0117.0013584",
								Number:     "****************************",
								DateOpened: "2015-05-15",
								Iban:       "****************************",
								Type:       "LLINE",
								Currency: entity.Currency{
									Code: "BYN",
									Name: "БЕЛОРУССКИЙ РУБЛЬ",
								},
								Activfl:       true,
								StatusExtCode: "OPENED",
								Status: entity.Status{
									Code: "OPENED",
									Name: "Открыт",
								},
								Title:                "счет по договору",
								OwnerName:            "Иван Иван",
								Balance:              111,
								BalanceNatVal:        0,
								BlockedBalance:       -10000,
								BlockedBalanceNatVal: -10000,
								AccountLockCurrent: entity.AccountLockCurrent{
									Value: entity.TypeValue{
										Code: "0",
										Name: "Без ограничений",
									},
								},
								Branch: entity.TypeValue{
									Code: "117",
									Name: "ГБ ЗАО \"МТБанк\"",
								},
								Bank: entity.TypeValue{
									Code: "*********",
									Name: "ЗАО \"МТБАНК\"",
								},
								AccountPlanType: entity.TypeValue{
									Code: "99112",
									Name: "Обязательства перед клиентами по предоставлению денежных средств",
								},
								TariffCategory: "001",
								AutCode: entity.TypeValue{
									Code: "117.000",
									Name: "Ответственный исполнитель 000",
								},
							},
						},
					},
				},
			},
		},
	}

	mockServer := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		if r.Header.Get("Content-Type") != "application/xml;charset=UTF-8" {
			http.Error(w, "invalid content type", http.StatusUnsupportedMediaType)
			return
		}

		w.WriteHeader(http.StatusOK)
		w.Header().Set("Content-Type", httpx.ContentTypeXML)
		err := serial.XMLEncode(w, findClientAccountsListResponse)
		require.NoError(t, err)
	}))
	defer mockServer.Close()

	colvir := &ColvirProviderImpl{
		HTTPClient: &http.Client{},
		BaseURL:    mockServer.URL,
	}

	t.Run("RequestFindClientAccountsList", func(t *testing.T) {
		ibans := []string{"********************", "********************"}

		got, err := colvir.RequestFindClientAccountsList(context.Background(), entity.RequestFindClientAccountsListReq{AccountNumbers: ibans})
		if err != nil {
			t.Errorf("RequestFindClientAccountsList() error = %v", err)
			return
		}
		if !reflect.DeepEqual(got, findClientAccountsListResponse) {
			t.Errorf("RequestFindClientAccountsList() got = %v, want %v", got, findClientAccountsListResponse)
		}
	})
}

const loadBankListResponseXMLSuccess = `
<soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
    <soap:Body>
        <dm:loadBankListResponse xmlns:a="http://bus.colvir.com/common/about/v1" xmlns:q="http://bus.colvir.com/common/query/v1" xmlns:b="http://bus.colvir.com/common/basis/v1" xmlns:s="http://bus.colvir.com/common/support/v1" xmlns:f="http://bus.colvir.com/common/forms/v1" xmlns:dm="http://bus.colvir.com/common/domain/v1">
            <s:code>0</s:code>
            <s:responseTime>79</s:responseTime>
            <s:responseDbTime>71</s:responseDbTime>
            <s:requestId>df0ca1f9-ec7f-4034-acb3-a96273712e12</s:requestId>
            <s:route>cbs@************:6502</s:route>
            <s:pageInfo>
                <s:start>1</s:start>
                <s:next>5</s:next>
                <s:loaded>4</s:loaded>
                <s:total>64</s:total>
                <s:hasMore>true</s:hasMore>
            </s:pageInfo>
            <dm:banksList>
                <dm:bank>
                    <dm:bic>HSBCKZKA</dm:bic>
                    <dm:telex>HSBCKZKA</dm:telex>
                    <dm:name>ДБ АО "HSBC БАНК КАЗАХСТАН"</dm:name>
                    <dm:nameAddress>ДБ АО HSBC БАНК КАЗАХСТАН
 KAZAKHSTAN</dm:nameAddress>
                    <dm:regionCode>19</dm:regionCode>
                    <dm:arcFl>1</dm:arcFl>
                </dm:bank>
                <dm:bank>
                    <dm:bic>DABNKZ2P</dm:bic>
                    <dm:telex>DABNKZ2P</dm:telex>
                    <dm:name>АО "Дочерний банк "Punjab National Bank" - Казахстан"</dm:name>
                    <dm:nameAddress>АО ДОЧЕРНИЙ БАНК PUNJAB NATIONAL
BANK - КАЗАХСТАН KAZAKHSTAN</dm:nameAddress>
                    <dm:regionCode>19</dm:regionCode>
                    <dm:arcFl>0</dm:arcFl>
                </dm:bank>
                <dm:bank>
                    <dm:bic>CABRKZKA</dm:bic>
                    <dm:telex>CABRKZKA</dm:telex>
                    <dm:name>ЗАО"ЦЕНТР-АЗИАТСКИЙ БАНК СОТР.И РАЗВИТИЯ"</dm:name>
                    <dm:nameAddress>ЗАОЦЕНТР-АЗИАТСКИЙ БАНК СОТР.И
РАЗВИТИЯ KAZAKHSTAN</dm:nameAddress>
                    <dm:regionCode>19</dm:regionCode>
                    <dm:arcFl>1</dm:arcFl>
                </dm:bank>
                <dm:bank>
                    <dm:bic>CSTDKZKA</dm:bic>
                    <dm:telex>CSTDKZKA</dm:telex>
                    <dm:name>УУМО (Банк-Кастодиан) ГУ Национальный Банк РК</dm:name>
                    <dm:nameAddress>УУМО (БАНК-КАСТОДИАН) ГУ
НАЦИОНАЛЬНЫЙ БАНК РК KAZAKHSTAN</dm:nameAddress>
                    <dm:regionCode>19</dm:regionCode>
                    <dm:arcFl>0</dm:arcFl>
                </dm:bank>
            </dm:banksList>
        </dm:loadBankListResponse>
    </soap:Body>
</soap:Envelope>
`

const loadBankListResponseXMLFailed = `
<soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
    <soap:Body>
        <soap:Fault>
            <faultcode>soap:Client</faultcode>
            <faultstring>Message part {http://bus.colvir.com/common/domain/v1}loadBankLstRequest was not recognized.  (Does it exist in service WSDL?)</faultstring>
        </soap:Fault>
    </soap:Body>
</soap:Envelope>
`

func TestColvirImpl_RequestLoadBankList(t *testing.T) {

	newMockServer := func(response string, httpStatus int) *httptest.Server {
		return httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			if r.Header.Get("Content-Type") != "application/xml;charset=UTF-8" {
				http.Error(w, "invalid content type", http.StatusUnsupportedMediaType)
				return
			}

			w.WriteHeader(httpStatus)
			w.Header().Set("Content-Type", httpx.ContentTypeXML)
			_, err := w.Write([]byte(response))
			require.NoError(t, err)
		}))
	}

	t.Run("requestLoadBankList success", func(t *testing.T) {
		mockServer := newMockServer(loadBankListResponseXMLSuccess, http.StatusOK)
		colvir := &ColvirProviderImpl{
			HTTPClient: &http.Client{},
			BaseURL:    mockServer.URL,
		}

		got, err := colvir.RequestLoadBankList(context.Background())
		require.NoError(t, err)
		require.Len(t, got.Body.LoadBankListResponse.BanksList.Bank, 4)
	})

	t.Run("requestLoadBankList failed", func(t *testing.T) {
		mockServer := newMockServer(loadBankListResponseXMLFailed, http.StatusInternalServerError)
		colvir := &ColvirProviderImpl{
			HTTPClient: &http.Client{},
			BaseURL:    mockServer.URL,
		}

		_, err := colvir.RequestLoadBankList(context.Background())
		require.Error(t, err)
	})
}

const loadDomainValuesResponseXML_Success = `
<soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
    <soap:Body>
        <dm:DomainValuesLoadResultElem xmlns:a="http://bus.colvir.com/common/about/v1" xmlns:q="http://bus.colvir.com/common/query/v1" xmlns:b="http://bus.colvir.com/common/basis/v1" xmlns:s="http://bus.colvir.com/common/support/v1" xmlns:f="http://bus.colvir.com/common/forms/v1" xmlns:dm="http://bus.colvir.com/common/domain/v1">
            <s:code>0</s:code>
            <s:responseTime>25</s:responseTime>
            <s:responseDbTime>17</s:responseDbTime>
            <s:requestId>2387e35f-e516-486f-a43b-13082b2335b7</s:requestId>
            <s:route>cbs@************:6502</s:route>
            <dm:value>
                <dm:code>21</dm:code>
                <dm:name>Правительство</dm:name>
                <dm:description>Признак нерезидента</dm:description>
            </dm:value>
            <dm:value>
                <dm:code>22</dm:code>
                <dm:name>Местные органы</dm:name>
                <dm:description>Признак нерезидента</dm:description>
            </dm:value>
            <dm:value>
                <dm:code>23</dm:code>
                <dm:name>НацБанк и центральные банки</dm:name>
                <dm:description>Признак нерезидента</dm:description>
            </dm:value>
        </dm:DomainValuesLoadResultElem>
    </soap:Body>
</soap:Envelope>
`

const loadDomainValuesResponseXML_Failed500 = `
<soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
    <soap:Body>
        <soap:Fault>
            <faultcode>soap:Client</faultcode>
            <faultstring>Error reading XMLStreamReader: Undeclared namespace prefix "v1"
 at [row,col {unknown-source}]: [3,28]</faultstring>
        </soap:Fault>
    </soap:Body>
</soap:Envelope>
`

const loadDomainValuesResponseXML_Failed200 = `
<soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
    <soap:Body>
        <dm:DomainValuesLoadResultElem xmlns:a="http://bus.colvir.com/common/about/v1" xmlns:q="http://bus.colvir.com/common/query/v1" xmlns:b="http://bus.colvir.com/common/basis/v1" xmlns:s="http://bus.colvir.com/common/support/v1" xmlns:f="http://bus.colvir.com/common/forms/v1" xmlns:dm="http://bus.colvir.com/common/domain/v1">
            <s:code>1</s:code>
            <s:responseTime>87</s:responseTime>
            <s:responseDbTime>24</s:responseDbTime>
            <s:requestId>11da8247-d0a1-426a-8827-3888cf8b7947</s:requestId>
            <s:route>cbs@************:6502</s:route>
            <s:errors>
                <s:code>DM-00011</s:code>
                <s:message>No solution could be found in W2_DOMAIN_MNT_METHOD for values (DCODE=KO;PARAMFL=false).</s:message>
                <s:severity>error</s:severity>
            </s:errors>
        </dm:DomainValuesLoadResultElem>
    </soap:Body>
</soap:Envelope>
`

func TestColvirImpl_RequestLoadDomainValues(t *testing.T) {
	newMockServer := func(response string, httpStatus int) *httptest.Server {
		return httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			if r.Header.Get("Content-Type") != "application/xml;charset=UTF-8" {
				http.Error(w, "invalid content type", http.StatusUnsupportedMediaType)
				return
			}

			w.WriteHeader(httpStatus)
			w.Header().Set("Content-Type", httpx.ContentTypeXML)
			_, err := w.Write([]byte(response))
			require.NoError(t, err)
		}))
	}

	t.Run("requestLoadDomainValues success", func(t *testing.T) {
		mockServer := newMockServer(loadDomainValuesResponseXML_Success, http.StatusOK)
		colvir := &ColvirProviderImpl{
			HTTPClient: &http.Client{},
			BaseURL:    mockServer.URL,
		}

		got, err := colvir.RequestLoadDomainValues(context.Background(), "KOD")
		require.NoError(t, err)
		require.Len(t, got.Body.DomainValuesLoadResultElem.Value, 3)
	})

	t.Run("requestLoadDomainValues failed 500", func(t *testing.T) {
		mockServer := newMockServer(loadDomainValuesResponseXML_Failed500, http.StatusInternalServerError)
		colvir := &ColvirProviderImpl{
			HTTPClient: &http.Client{},
			BaseURL:    mockServer.URL,
		}

		_, err := colvir.RequestLoadDomainValues(context.Background(), "KOD")
		require.Error(t, err)
	})

	t.Run("requestLoadDomainValues failed 200", func(t *testing.T) {
		mockServer := newMockServer(loadDomainValuesResponseXML_Failed200, http.StatusOK)
		colvir := &ColvirProviderImpl{
			HTTPClient: &http.Client{},
			BaseURL:    mockServer.URL,
		}

		_, err := colvir.RequestLoadDomainValues(context.Background(), "KOD")
		require.Error(t, err)
	})
}

const loadDomainHierarchyValuesResponseXML_Success = `
<soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
    <soap:Body>
        <dm:DomainHierarchyValuesLoadResult xmlns:a="http://bus.colvir.com/common/about/v1" xmlns:q="http://bus.colvir.com/common/query/v1" xmlns:b="http://bus.colvir.com/common/basis/v1" xmlns:s="http://bus.colvir.com/common/support/v1" xmlns:f="http://bus.colvir.com/common/forms/v1" xmlns:dm="http://bus.colvir.com/common/domain/v1">
            <s:code>0</s:code>
            <s:responseTime>18</s:responseTime>
            <s:responseDbTime>9</s:responseDbTime>
            <s:requestId>8f8f8dea-9567-499e-ab80-1613af1f8930</s:requestId>
            <s:route>cbs@************:6502</s:route>
            <dm:value>
                <dm:code>BE</dm:code>
                <dm:name>Бельгия</dm:name>
                <dm:description>Бельгия</dm:description>
                <dm:id>124</dm:id>
                <dm:isGroup>1</dm:isGroup>
            </dm:value>
            <dm:value>
                <dm:code>BE01</dm:code>
                <dm:name>BRUSSELS</dm:name>
                <dm:description>BRUSSELS</dm:description>
                <dm:id>125</dm:id>
                <dm:idHi>124</dm:idHi>
                <dm:isGroup>0</dm:isGroup>
            </dm:value>
            <dm:value>
                <dm:code>BF</dm:code>
                <dm:name>Буркина-Фасо</dm:name>
                <dm:description>Буркина-Фасо</dm:description>
                <dm:id>333</dm:id>
                <dm:isGroup>0</dm:isGroup>
            </dm:value>
            <dm:value>
                <dm:code>BG</dm:code>
                <dm:name>Болгария</dm:name>
                <dm:description>Болгария</dm:description>
                <dm:id>334</dm:id>
                <dm:isGroup>0</dm:isGroup>
            </dm:value>
        </dm:DomainHierarchyValuesLoadResult>
    </soap:Body>
</soap:Envelope>
`

const loadDomainHierarchyValuesResponseXML_Failed500 = `
<soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
    <soap:Body>
        <soap:Fault>
            <faultcode>soap:Client</faultcode>
            <faultstring>Error reading XMLStreamReader: Undeclared namespace prefix "v1"
 at [row,col {unknown-source}]: [3,28]</faultstring>
        </soap:Fault>
    </soap:Body>
</soap:Envelope>
`

const loadDomainHierarchyValuesResponseXML_Failed200 = `
<soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
    <soap:Body>
        <dm:DomainHierarchyValuesLoadResult xmlns:a="http://bus.colvir.com/common/about/v1" xmlns:q="http://bus.colvir.com/common/query/v1" xmlns:b="http://bus.colvir.com/common/basis/v1" xmlns:s="http://bus.colvir.com/common/support/v1" xmlns:f="http://bus.colvir.com/common/forms/v1" xmlns:dm="http://bus.colvir.com/common/domain/v1">
            <s:code>1</s:code>
            <s:responseTime>87</s:responseTime>
            <s:responseDbTime>24</s:responseDbTime>
            <s:requestId>11da8247-d0a1-426a-8827-3888cf8b7947</s:requestId>
            <s:route>cbs@************:6502</s:route>
            <s:errors>
                <s:code>DM-00011</s:code>
                <s:message>No solution could be found in W2_DOMAIN_MNT_METHOD for values (DCODE=KO;PARAMFL=false).</s:message>
                <s:severity>error</s:severity>
            </s:errors>
        </dm:DomainHierarchyValuesLoadResult>
    </soap:Body>
</soap:Envelope>
`

func TestColvirImpl_RequestLoadDomainHierarchyValues(t *testing.T) {
	newMockServer := func(response string, httpStatus int) *httptest.Server {
		return httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			if r.Header.Get("Content-Type") != "application/xml;charset=UTF-8" {
				http.Error(w, "invalid content type", http.StatusUnsupportedMediaType)
				return
			}

			w.WriteHeader(httpStatus)
			w.Header().Set("Content-Type", httpx.ContentTypeXML)
			_, err := w.Write([]byte(response))
			require.NoError(t, err)
		}))
	}

	t.Run("requestLoadDomainHierarchyValues success", func(t *testing.T) {
		mockServer := newMockServer(loadDomainHierarchyValuesResponseXML_Success, http.StatusOK)
		colvir := &ColvirProviderImpl{
			HTTPClient: &http.Client{},
			BaseURL:    mockServer.URL,
		}

		got, err := colvir.RequestLoadDomainHierarchyValues(context.Background(), "COUNTRY")
		require.NoError(t, err)
		require.Len(t, got.Body.DomainValuesLoadResultElem.Value, 4)
	})

	t.Run("requestLoadDomainHierarchyValues failed 500", func(t *testing.T) {
		mockServer := newMockServer(loadDomainHierarchyValuesResponseXML_Failed500, http.StatusInternalServerError)
		colvir := &ColvirProviderImpl{
			HTTPClient: &http.Client{},
			BaseURL:    mockServer.URL,
		}

		_, err := colvir.RequestLoadDomainHierarchyValues(context.Background(), "COUNTRY")
		require.Error(t, err)
	})

	t.Run("requestLoadDomainHierarchyValues failed 200", func(t *testing.T) {
		mockServer := newMockServer(loadDomainHierarchyValuesResponseXML_Failed200, http.StatusOK)
		colvir := &ColvirProviderImpl{
			HTTPClient: &http.Client{},
			BaseURL:    mockServer.URL,
		}

		_, err := colvir.RequestLoadDomainHierarchyValues(context.Background(), "COUNTRY")
		require.Error(t, err)
	})
}

func TestColvirImpl_CreateClientAgreement(t *testing.T) {
	createClientAgreementExampleSuccessResponse := entity.CreateClientAgreementResp{
		DeaReferenceID: "1234_1153",
	}

	mockServer := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		if r.Header.Get("Content-Type") != "application/json" {
			http.Error(w, "invalid content type", http.StatusUnsupportedMediaType)
			return
		}

		var reqBody entity.CreateClientAgreementRequest
		err := json.NewDecoder(r.Body).Decode(&reqBody)
		require.NoError(t, err)

		switch {
		case reqBody.ClientCode == "invalid":
			http.Error(w, "invalid client code", http.StatusBadRequest)
		default:
			w.WriteHeader(http.StatusOK)
			w.Header().Set("Content-Type", "application/json")
			data, err := json.Marshal(createClientAgreementExampleSuccessResponse)
			require.NoError(t, err)

			_, err = w.Write(data)
			require.NoError(t, err)
		}
	}))
	defer mockServer.Close()

	t.Run("Success", func(t *testing.T) {
		colvir := &ColvirProviderImpl{
			HTTPClient: &http.Client{},
			V2BaseURL:  mockServer.URL,
		}

		got, err := colvir.RequestCreateClientAgreement(context.Background(), entity.CreateClientAgreementRequest{
			ChanelCode:      "BPM",
			DeaTemplateCode: "FL.TQ",
			ClientCode:      "100000000031",
			CurrencyCode:    "UZS",
			DepCode:         "10462",
			TarifCode:       "201UZ.FL",
		})

		require.NoError(t, err)
		if !reflect.DeepEqual(got, &createClientAgreementExampleSuccessResponse) {
			t.Errorf("RequestCreateClientAgreement() got = %v, want %v", got, createClientAgreementExampleSuccessResponse)
		}
	})

	t.Run("InvalidClientCode", func(t *testing.T) {
		colvir := &ColvirProviderImpl{
			HTTPClient: &http.Client{},
			V2BaseURL:  mockServer.URL,
		}
		_, err := colvir.RequestCreateClientAgreement(context.Background(), entity.CreateClientAgreementRequest{
			ChanelCode: "BPM",
			ClientCode: "invalid",
		})
		require.NotNil(t, err)
	})
}

func TestColvirImpl_RequestCheckClientAgreement(t *testing.T) {
	checkClientAgreementErrorResponse := entity.CheckClientAgreementResponse{
		ColvirError: &entity.ColvirErrResp{},
	}

	clientAgreements := []entity.CheckClientAgreement{
		{
			DeaReferenceID: "2056_1053",
			AccountCode:    "21002000399990015001",
			DocNum:         "********-000002",
			DocDate: entity.DocDate{
				FromDate: "2020-12-07T00:00:00",
			},
			DocType: entity.DocType{
				Code: "RKO.JUR.1201.MACC",
				Name: "Договор основного счета (юр.лица и ИП)",
			},
			DocStatus: entity.DocType{
				Code: "EXCLUDED",
				Name: "Переведен",
			},
		},
		{
			DeaReferenceID: "2980_1253305",
			AccountCode:    "20296000268000065430",
			DocNum:         "********-000195",
			DocDate: entity.DocDate{
				FromDate: "2020-12-10T00:00:00",
			},
			DocType: entity.DocType{
				Code: "RKO.JUR.1201.MACC",
				Name: "Договор основного счета (юр.лица и ИП)",
			},
			DocStatus: entity.DocType{
				Code: "TO_EXCLUDE",
				Name: "Подготовка к переводу",
			},
		},
		{
			DeaReferenceID: "2980_2423",
			AccountCode:    "20206000060000002006",
			DocNum:         "********-000007",
			DocDate: entity.DocDate{
				FromDate: "2021-01-06T00:00:00",
			},
			DocType: entity.DocType{
				Code: "RKO.FIZ.1201.SACC",
				Name: "Договор вклада ДДВ (физ.лица)",
			},
			DocStatus: entity.DocType{
				Code: "ACTIVE",
				Name: "Действующий",
			},
		},
	}

	checkClientAgreementExampleSuccessResponse := entity.CheckClientAgreementResponse{
		CheckClientAgreement: clientAgreements,
	}

	mockServer := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		if r.Header.Get("Content-Type") != "application/json" {
			http.Error(w, "invalid content type", http.StatusUnsupportedMediaType)
			return
		}

		var reqBody entity.CheckClientAgreementRequest
		err := json.NewDecoder(r.Body).Decode(&reqBody)
		require.NoError(t, err)

		switch reqBody.DeaReferenceIDLst {
		case "2980_22821630":
			w.WriteHeader(http.StatusOK)
			w.Header().Set("Content-Type", "application/json")
			data, err := json.Marshal(checkClientAgreementErrorResponse)
			require.NoError(t, err)

			_, err = w.Write(data)
			require.NoError(t, err)
		case "invalid":
			w.WriteHeader(http.StatusInternalServerError)
		default:
			w.WriteHeader(http.StatusOK)
			w.Header().Set("Content-Type", "application/json")
			data, err := json.Marshal(clientAgreements)
			require.NoError(t, err)

			_, err = w.Write(data)
			require.NoError(t, err)
		}
	}))
	defer mockServer.Close()

	t.Run("Success", func(t *testing.T) {
		colvir := &ColvirProviderImpl{
			HTTPClient: &http.Client{},
			V2BaseURL:  mockServer.URL,
		}

		got, err := colvir.RequestCheckClientAgreement(context.Background(), entity.CheckClientAgreementRequest{
			DeaReferenceIDLst: "2980_22821630,2980_1253305,2980_2423",
		})
		fmt.Printf("got = %v", got)
		require.NoError(t, err)
		if !reflect.DeepEqual(got, &checkClientAgreementExampleSuccessResponse) {
			t.Errorf("RequestCheckClientAgreement() got = %v, want %v", got, checkClientAgreementExampleSuccessResponse)
		}
	})

	t.Run("Error", func(t *testing.T) {
		colvir := &ColvirProviderImpl{
			HTTPClient: &http.Client{},
			V2BaseURL:  mockServer.URL,
		}

		_, err := colvir.RequestCheckClientAgreement(context.Background(), entity.CheckClientAgreementRequest{
			DeaReferenceIDLst: "2980_22821630",
		})
		require.Error(t, err)
	})

	t.Run("InvalidDeaReferenceID", func(t *testing.T) {
		colvir := &ColvirProviderImpl{
			HTTPClient: &http.Client{},
			V2BaseURL:  mockServer.URL,
		}

		_, err := colvir.RequestCheckClientAgreement(context.Background(), entity.CheckClientAgreementRequest{
			DeaReferenceIDLst: "invalid",
		})
		require.Error(t, err)
	})
}

const loadBankHolidaysResponseXML_Success = `
<soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
    <soap:Body>
        <dm:loadBankHolidaysResponseElem xmlns:a="http://bus.colvir.com/common/about/v1" xmlns:q="http://bus.colvir.com/common/query/v1" xmlns:b="http://bus.colvir.com/common/basis/v1" xmlns:s="http://bus.colvir.com/common/support/v1" xmlns:f="http://bus.colvir.com/common/forms/v1" xmlns:dm="http://bus.colvir.com/common/domain/v1">
            <s:code>0</s:code>
            <s:responseTime>11</s:responseTime>
            <s:responseDbTime>5</s:responseDbTime>
            <s:requestId>119cc946-f29b-4bd5-bf4d-05c620287561</s:requestId>
            <s:route>cbs@************:6502</s:route>
            <dm:calendarCode>KZ</dm:calendarCode>
            <dm:holiday>
                <dm:day>7</dm:day>
                <dm:month>1</dm:month>
                <dm:year>2025</dm:year>
                <dm:holiday>Рождество Христово</dm:holiday>
                <dm:isHoliday>1</dm:isHoliday>
                <dm:weekDay>2</dm:weekDay>
            </dm:holiday>
            <dm:holiday>
                <dm:day>8</dm:day>
                <dm:month>1</dm:month>
                <dm:year>2025</dm:year>
                <dm:isHoliday>0</dm:isHoliday>
                <dm:weekDay>3</dm:weekDay>
            </dm:holiday>
            <dm:holiday>
                <dm:day>9</dm:day>
                <dm:month>1</dm:month>
                <dm:year>2025</dm:year>
                <dm:isHoliday>0</dm:isHoliday>
                <dm:weekDay>4</dm:weekDay>
            </dm:holiday>
            <dm:holiday>
                <dm:day>10</dm:day>
                <dm:month>1</dm:month>
                <dm:year>2025</dm:year>
                <dm:isHoliday>0</dm:isHoliday>
                <dm:weekDay>5</dm:weekDay>
            </dm:holiday>
            <dm:holiday>
                <dm:day>11</dm:day>
                <dm:month>1</dm:month>
                <dm:year>2025</dm:year>
                <dm:holiday>Суббота</dm:holiday>
                <dm:isHoliday>1</dm:isHoliday>
                <dm:weekDay>6</dm:weekDay>
            </dm:holiday>
            <dm:holiday>
                <dm:day>12</dm:day>
                <dm:month>1</dm:month>
                <dm:year>2025</dm:year>
                <dm:holiday>Воскресенье</dm:holiday>
                <dm:isHoliday>1</dm:isHoliday>
                <dm:weekDay>7</dm:weekDay>
            </dm:holiday>
            <dm:holiday>
                <dm:day>13</dm:day>
                <dm:month>1</dm:month>
                <dm:year>2025</dm:year>
                <dm:isHoliday>0</dm:isHoliday>
                <dm:weekDay>1</dm:weekDay>
            </dm:holiday>
            <dm:holiday>
                <dm:day>14</dm:day>
                <dm:month>1</dm:month>
                <dm:year>2025</dm:year>
                <dm:isHoliday>0</dm:isHoliday>
                <dm:weekDay>2</dm:weekDay>
            </dm:holiday>
        </dm:loadBankHolidaysResponseElem>
    </soap:Body>
</soap:Envelope>
`

const loadBankHolidaysResponseXML_Failed500 = `
<soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
    <soap:Body>
        <soap:Fault>
            <faultcode>soap:VersionMismatch</faultcode>
            <faultstring>"ttp://schemas.xmlsoap.org/soap/envelope/", the namespace on the "Envelope" element, is not a valid SOAP version.</faultstring>
        </soap:Fault>
    </soap:Body>
</soap:Envelope>
`

const loadBankHolidaysResponseXML_Failed200 = `
<soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
    <soap:Body>
        <dm:loadBankHolidaysResponseElem xmlns:a="http://bus.colvir.com/common/about/v1" xmlns:q="http://bus.colvir.com/common/query/v1" xmlns:b="http://bus.colvir.com/common/basis/v1" xmlns:s="http://bus.colvir.com/common/support/v1" xmlns:f="http://bus.colvir.com/common/forms/v1" xmlns:dm="http://bus.colvir.com/common/domain/v1">
            <s:code>1</s:code>
            <s:responseTime>9</s:responseTime>
            <s:responseDbTime>0</s:responseDbTime>
            <s:requestId>a26359ef-b503-4197-aa3c-dd64bf2f973f</s:requestId>
            <s:route>cbs@************:6502</s:route>
            <s:errors>
                <s:code>GF-10000</s:code>
                <s:message>Validation error: [3, 46]: cvc-datatype-valid.1.2.1: '2025-01-14-' is not a valid value for 'date'.</s:message>
                <s:severity>error</s:severity>
            </s:errors>
            <s:errors>
                <s:code>GF-10000</s:code>
                <s:message>Validation error: [3, 46]: cvc-type.3.1.3: The value '2025-01-14-' of element 'v1:endDate' is not valid.</s:message>
                <s:severity>error</s:severity>
            </s:errors>
        </dm:loadBankHolidaysResponseElem>
    </soap:Body>
</soap:Envelope>
`

func TestColvirImpl_RequestLoadBankHolidays(t *testing.T) {
	newMockServer := func(response string, httpStatus int) *httptest.Server {
		return httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			if r.Header.Get("Content-Type") != "application/xml;charset=UTF-8" {
				http.Error(w, "invalid content type", http.StatusUnsupportedMediaType)
				return
			}

			w.WriteHeader(httpStatus)
			w.Header().Set("Content-Type", httpx.ContentTypeXML)
			_, err := w.Write([]byte(response))
			require.NoError(t, err)
		}))
	}

	startDate := time.Date(2025, 1, 7, 0, 0, 0, 0, time.UTC)
	endDate := time.Date(2025, 1, 14, 0, 0, 0, 0, time.UTC)

	t.Run("requestLoadBankHolidays success", func(t *testing.T) {
		mockServer := newMockServer(loadBankHolidaysResponseXML_Success, http.StatusOK)
		colvir := &ColvirProviderImpl{
			HTTPClient: &http.Client{},
			BaseURL:    mockServer.URL,
		}

		got, err := colvir.RequestLoadBankHolidays(context.Background(), startDate, endDate)
		require.NoError(t, err)
		require.Equal(t, got.Body.LoadBankHolidaysResponseElem.Code, "0")
		require.Len(t, got.Body.LoadBankHolidaysResponseElem.Holiday, 8)

		require.Equal(t, got.Body.LoadBankHolidaysResponseElem.Holiday[0].Day, int32(7))
		require.Equal(t, got.Body.LoadBankHolidaysResponseElem.Holiday[0].Month, int32(1))
		require.Equal(t, got.Body.LoadBankHolidaysResponseElem.Holiday[0].Year, int32(2025))
		require.Equal(t, got.Body.LoadBankHolidaysResponseElem.Holiday[0].Holiday, "Рождество Христово")
		require.Equal(t, got.Body.LoadBankHolidaysResponseElem.Holiday[0].IsHoliday, 1)
		require.Equal(t, got.Body.LoadBankHolidaysResponseElem.Holiday[0].WeekDay, int32(2))

		require.Equal(t, got.Body.LoadBankHolidaysResponseElem.Holiday[1].Day, int32(8))
		require.Equal(t, got.Body.LoadBankHolidaysResponseElem.Holiday[1].Month, int32(1))
		require.Equal(t, got.Body.LoadBankHolidaysResponseElem.Holiday[1].Year, int32(2025))
		require.Equal(t, got.Body.LoadBankHolidaysResponseElem.Holiday[1].IsHoliday, 0)
		require.Equal(t, got.Body.LoadBankHolidaysResponseElem.Holiday[1].WeekDay, int32(3))
	})

	t.Run("requestLoadBankHolidays failed 500", func(t *testing.T) {
		mockServer := newMockServer(loadBankHolidaysResponseXML_Failed500, http.StatusInternalServerError)
		colvir := &ColvirProviderImpl{
			HTTPClient: &http.Client{},
			BaseURL:    mockServer.URL,
		}

		_, err := colvir.RequestLoadBankHolidays(context.Background(), startDate, endDate)
		require.Error(t, err)
	})

	t.Run("requestLoadBankHolidays failed 200", func(t *testing.T) {
		mockServer := newMockServer(loadBankHolidaysResponseXML_Failed200, http.StatusOK)
		colvir := &ColvirProviderImpl{
			HTTPClient: &http.Client{},
			BaseURL:    mockServer.URL,
		}

		_, err := colvir.RequestLoadBankHolidays(context.Background(), startDate, endDate)
		require.Error(t, err)
	})
}

func TestColvirImpl_CreateClientSMEIP(t *testing.T) {
	createClientIPExampleSuccessResponse := entity.CreateClientIPResp{
		ResultCode:   "0",
		ResultMsg:    "Ответ успешно сформирован",
		ClientCode:   "********",
		ClientDepID:  "735",
		ClientID:     "23275",
		ClientStatus: "OPENED",
		ClientFiz: &entity.ClientFizResp{
			ClientCode:   "********",
			ClientDepID:  "735",
			ClientID:     "17748",
			ClientStatus: "OPENED",
		},
		GBLErrs: []entity.GBLError{},
	}

	mockServer := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		if r.Header.Get("Content-Type") != "application/json" {
			http.Error(w, "invalid content type", http.StatusUnsupportedMediaType)
			return
		}

		var reqBody entity.CreateClientIPRequest
		err := json.NewDecoder(r.Body).Decode(&reqBody)
		require.NoError(t, err)

		switch {
		case reqBody.FilialCode == "01":
			w.WriteHeader(http.StatusInternalServerError)
			w.Header().Set("Content-Type", "application/json")
			data, err := json.Marshal(createClientExampleErrResponse)
			require.NoError(t, err)

			_, err = w.Write(data)
			require.NoError(t, err)
		default:
			w.WriteHeader(http.StatusOK)
			w.Header().Set("Content-Type", "application/json")
			data, err := json.Marshal(createClientIPExampleSuccessResponse)
			require.NoError(t, err)

			_, err = w.Write(data)
			require.NoError(t, err)
		}
	}))
	defer mockServer.Close()

	t.Run("Success", func(t *testing.T) {
		colvir := &ColvirProviderImpl{
			HTTPClient: &http.Client{},
			V2BaseURL:  mockServer.URL,
		}

		got, err := colvir.RequestCreateClientSMEIP(context.Background(), &entity.CreateClientIPRequest{
			RequestID:  uuid.New().String(),
			FilialCode: "00",
			Name:       "Test",
			IIN:        "",
		})
		require.NoError(t, err)
		if !reflect.DeepEqual(got, &createClientIPExampleSuccessResponse) {
			t.Errorf("RequestCreateClient() got = %v, want %v", got, createClientIPExampleSuccessResponse)
		}
	})

	t.Run("Error", func(t *testing.T) {
		colvir := &ColvirProviderImpl{
			HTTPClient: &http.Client{},
			V2BaseURL:  mockServer.URL,
		}
		_, err := colvir.RequestCreateClientSMEIP(context.Background(), &entity.CreateClientIPRequest{
			RequestID:  uuid.New().String(),
			FilialCode: "01",
			Name:       "Test",
			IIN:        "",
		})
		require.NotNil(t, err)
	})
}

func TestColvirImpl_UpdateClientIP(t *testing.T) {
	updateClientExampleSuccessResponse := entity.UpdateClientIPResp{
		ResultCode:  "0",
		ResultMsg:   "Ok!",
		ClientCode:  "123456789",
		ClientDepID: "1",
		ClientID:    "1",
	}

	updateClientExampleErrResponse := entity.UpdateClientIPResp{
		ResultCode:  "10605",
		ResultMsg:   "Ошибка обработки",
		ClientCode:  "",
		ClientDepID: "",
		ClientID:    "",
		GBLErrs: []entity.GBLError{
			{
				Code:     "10617",
				Status:   0,
				LongName: "Ошибка обработки клиента",
				TxtErr:   nil,
			},
		},
	}

	mockServer := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		if r.Header.Get("Content-Type") != "application/json" {
			http.Error(w, "invalid content type", http.StatusUnsupportedMediaType)
			return
		}

		var reqBody entity.UpdateClientIPRequest
		err := json.NewDecoder(r.Body).Decode(&reqBody)
		require.NoError(t, err)

		if len(reqBody.RegDocs) == 0 { // Проверка на обязательность RegDocs
			http.Error(w, "missing required field: regDoc", http.StatusBadRequest)
			return
		}

		// Симуляция ошибки, если ClientCode == "error"
		if reqBody.ClientCode == "error" {
			w.WriteHeader(http.StatusInternalServerError)
			w.Header().Set("Content-Type", "application/json")
			data, err := json.Marshal(updateClientExampleErrResponse)
			require.NoError(t, err)

			_, err = w.Write(data)
			require.NoError(t, err)
			return
		}

		// Успешный ответ
		w.WriteHeader(http.StatusOK)
		w.Header().Set("Content-Type", "application/json")
		data, err := json.Marshal(updateClientExampleSuccessResponse)
		require.NoError(t, err)

		_, err = w.Write(data)
		require.NoError(t, err)
	}))
	defer mockServer.Close()

	t.Run("Success", func(t *testing.T) {
		colvir := &ColvirProviderImpl{
			HTTPClient: &http.Client{},
			V2BaseURL:  mockServer.URL,
		}

		got, err := colvir.RequestUpdateClientSMEIP(context.Background(), &entity.UpdateClientIPRequest{
			RequestID:   uuid.New().String(),
			ClientCode:  "123456789",
			ClientDepID: "1",
			ClientID:    "1",
			RegDocs: []entity.UpdateRegDocIP{ // Обязательное поле
				{
					Type:       "01",
					Series:     "AB",
					Number:     "123456",
					IssueDate:  "2024-01-01",
					ExpireDate: "2034-01-01",
					RegDate:    "2024-01-01",
					IssuePlace: "Registry Office",
				},
			},
			Addresses: []entity.UpdateAddressIP{},
			Contacts:  []entity.UpdateContactIP{},
		})
		require.NoError(t, err)
		require.Equal(t, updateClientExampleSuccessResponse, *got)
	})

	t.Run("Error - Missing RegDocs", func(t *testing.T) {
		colvir := &ColvirProviderImpl{
			HTTPClient: &http.Client{},
			V2BaseURL:  mockServer.URL,
		}

		_, err := colvir.RequestUpdateClientSMEIP(context.Background(), &entity.UpdateClientIPRequest{
			RequestID:   uuid.New().String(),
			ClientCode:  "123456789",
			ClientDepID: "1",
			ClientID:    "1",
			Addresses:   []entity.UpdateAddressIP{},
			Contacts:    []entity.UpdateContactIP{},
		})
		require.Error(t, err)
		require.Contains(t, err.Error(), "missing required field: regDoc")
	})

	t.Run("Error - Server Failure", func(t *testing.T) {
		colvir := &ColvirProviderImpl{
			HTTPClient: &http.Client{},
			V2BaseURL:  mockServer.URL,
		}

		_, err := colvir.RequestUpdateClientSMEIP(context.Background(), &entity.UpdateClientIPRequest{
			RequestID:   uuid.New().String(),
			ClientCode:  "error", // Вызовет ошибку сервера
			ClientDepID: "1",
			ClientID:    "1",
			RegDocs: []entity.UpdateRegDocIP{ // Обязательное поле
				{
					Type:       "01",
					Series:     "AB",
					Number:     "123456",
					IssueDate:  "2024-01-01",
					ExpireDate: "2034-01-01",
					RegDate:    "2024-01-01",
					IssuePlace: "Registry Office",
				},
			},
			Addresses: []entity.UpdateAddressIP{},
			Contacts:  []entity.UpdateContactIP{},
		})
		require.ErrorIs(t, err, ErrInternalServerError)
	})
}
