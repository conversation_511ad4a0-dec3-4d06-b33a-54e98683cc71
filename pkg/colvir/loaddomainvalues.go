package colvir

import (
	"bytes"
	"context"
	"fmt"
	"io"
	"net/http"
	"net/url"

	"git.redmadrobot.com/backend-go/rmr-pkg/core/errs"
	"git.redmadrobot.com/backend-go/rmr-pkg/tools/serial"

	"git.redmadrobot.com/zaman/backend/zaman/pkg/colvir/entity"
)

const (
	colvirLoadDomainValuesEndpoint = "/cxf/domain/v1"
)

func (p *ColvirProviderImpl) RequestLoadDomainValues(
	ctx context.Context,
	domainValueCode string,
) (*entity.LoadDomainValuesResponse, error) {
	var buf bytes.Buffer
	if err := serial.XMLEncode(&buf, entity.NewLoadDomainValuesRequest(domainValueCode)); err != nil {
		return nil, errs.Wrapf(err, "failed to encode request body")
	}

	endpoint, err := p.getRequestLoadDomainValuesURL()
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequestWithContext(
		ctx,
		http.MethodPost,
		endpoint,
		bytes.NewBuffer(buf.Bytes()),
	)
	if err != nil {
		return nil, errs.Wrapf(err, "failed to create new http request")
	}

	req.Header.Set("Content-Type", "application/xml;charset=UTF-8")

	resp, err := p.HTTPClient.Do(req)
	if err != nil {
		return nil, errs.Wrapf(err, "failed to do http request")
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		bodyBytes, err := io.ReadAll(resp.Body)
		if err != nil {
			bodyBytes = []byte(err.Error())
		}
		return nil, handleStatusCode(resp.StatusCode, bodyBytes)
	}

	domainValuesResponse, err := serial.XMLDecode[entity.LoadDomainValuesResponse](resp.Body)
	if err != nil {
		return nil, errs.Wrapf(err, "failed to decode http response body")
	}

	if domainValuesResponse.Body.DomainValuesLoadResultElem.Errors != nil {
		return nil, domainValuesResponse.Body.DomainValuesLoadResultElem.Errors
	}

	return &domainValuesResponse, nil
}

func (p *ColvirProviderImpl) getRequestLoadDomainValuesURL() (string, error) {
	result, err := url.JoinPath(p.BaseURL, colvirLoadDomainValuesEndpoint)
	if err != nil {
		return "", fmt.Errorf("failed to join path for load domain values request: %w", err)
	}

	return result, nil
}
