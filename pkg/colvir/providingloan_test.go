package colvir

import (
	"context"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/google/go-cmp/cmp"
	"github.com/stretchr/testify/require"

	"git.redmadrobot.com/zaman/backend/zaman/pkg/colvir/entity"
)

var (
	ProvidingLoanReqJson = `{
		"colvirReferenceId": "2980_2236610267",
		"number": "080993",
		"amount": "120000",
		"typeOfPayment": "1",
		"paymentDetails": {
		"account": "29801000360000014306",
		"bankCode": "123",
		"name": "Тестовый Тест Тестович",
		"inn": "123",
		"description": "Тестовое назначение 2",
		"knp": "00501"
		},
		"useDetails": {
		"partner": "************",
		"productId": "**********",
		"account": "29801000060000014284",
		"bankCode": "123",
		"amount": 120000,
		"cmsStrategy": "0",
		"cmsAccount": "29801000060000014284",
		"cmsBnkCode": "456",
		"cmsAmount": 15000,
		"onlineFl": "0"
}
}`

	providingLoanSuccessRespJson = `{
		"colvirReferenceId": "694_1010352",
		"productId": "**********"
}`
	providingLoanFailureRespJson = `{
		"Code": 10403,
		"QCTXID": "************",
		"Text": "Ошибка получения данных",
		"Description": "Не найден договор по референсу 2980_227124971",
		"HTTPCode": 500,
		"Module": "loancreate",
		"Hint": {},
		"Level": {
		"Type": "CRITICAL",
		"Value": 0
		},
		"EDate": "2024-02-21T13:09:26"
}`
)

func TestColvirProviderImpl_ProvidingLoan(t *testing.T) {
	var (
		providingLoanReq  *entity.ProvidingLoanReq
		providingLoanResp *entity.ProvidingLoanResp
		colvirErr         *entity.ColvirErrResp
	)
	require.NoError(t, json.Unmarshal([]byte(ProvidingLoanReqJson), &providingLoanReq))
	require.NoError(t, json.Unmarshal([]byte(providingLoanSuccessRespJson), &providingLoanResp))
	require.NoError(t, json.Unmarshal([]byte(providingLoanFailureRespJson), &colvirErr))

	mockServer := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		if r.Header.Get("Content-Type") != "application/json" {
			http.Error(w, "invalid content type", http.StatusUnsupportedMediaType)
			return
		}

		var reqBody entity.ProvidingLoanReq
		err := json.NewDecoder(r.Body).Decode(&reqBody)
		require.NoError(t, err)

		switch {
		case reqBody.Number == "00":
			w.WriteHeader(http.StatusInternalServerError)
			w.Header().Set("Content-Type", "application/json")

			_, err = w.Write([]byte(providingLoanFailureRespJson))
			require.NoError(t, err)
		default:
			w.WriteHeader(http.StatusOK)
			w.Header().Set("Content-Type", "application/json")

			_, err = w.Write([]byte(providingLoanSuccessRespJson))
			require.NoError(t, err)
		}
	}))
	defer mockServer.Close()

	colvir := &ColvirProviderImpl{
		HTTPClient: &http.Client{},
		V2BaseURL:  mockServer.URL,
	}

	t.Run("LoanCalcLoadPreSchedule success", func(t *testing.T) {
		got, err := colvir.RequestProvidingLoan(context.Background(), providingLoanReq)
		if err != nil {
			t.Errorf("LoanCalcLoadPreSchedule() error = %v", err)
			return
		}
		diff := cmp.Diff(got, providingLoanResp)
		if diff != "" {
			t.Errorf("LoanCalcLoadPreSchedule() got = %+v, want %+v", got, providingLoanResp)
			t.Errorf("LoanCalcLoadPreSchedule() diff = %s", diff)
		}
	})

	t.Run("LoanCalcLoadPreSchedule fail", func(t *testing.T) {
		providingLoanReq.Number = "00"
		got, err := colvir.RequestProvidingLoan(context.Background(), providingLoanReq)
		require.Error(t, err)

		diff := cmp.Diff(got, &entity.ProvidingLoanResp{
			ColvirError: colvirErr,
		})
		if diff != "" {
			t.Errorf("LoanCalcLoadPreSchedule() got = %+v, want %+v", got, providingLoanResp)
			t.Errorf("LoanCalcLoadPreSchedule() diff = %s", diff)
		}
	})

	t.Run("ServerNotResponding", func(t *testing.T) {
		// Создаем клиент с несуществующим URL сервера
		nonRespondingColvir := &ColvirProviderImpl{
			HTTPClient: &http.Client{},
			V2BaseURL:  "http://non-existent-server",
		}

		// Вызываем метод
		resp, err := nonRespondingColvir.RequestProvidingLoan(context.Background(), providingLoanReq)

		// Проверяем, что ошибка соответствует ожидаемой
		require.Equal(t, ErrColvirServerNotResponding, err)

		// Проверяем, что ответ содержит ожидаемую информацию об ошибке
		require.NotNil(t, resp)
		require.NotNil(t, resp.ColvirError)
		require.Equal(t, "failed to do request for RequestProvidingLoan", resp.ColvirError.Text)
		require.NotEmpty(t, resp.ColvirError.Description)
	})
}
