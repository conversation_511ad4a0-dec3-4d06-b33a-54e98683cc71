package colvir

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"net/url"

	"git.redmadrobot.com/zaman/backend/zaman/pkg/colvir/entity"
)

func (p *ColvirProviderImpl) RequestFindClient(ctx context.Context, request entity.FindClientReq) (*entity.FindClientResp, error) {
	targetURL, err := p.getRequestFindClientURL()
	if err != nil {
		return nil, err
	}

	var buf bytes.Buffer
	if err := json.NewEncoder(&buf).Encode(request); err != nil {
		return nil, fmt.Errorf("failed to encode request body for find client request: %w", err)
	}

	req, err := http.NewRequestWithContext(ctx, http.MethodGet, targetURL, &buf)
	if err != nil {
		return nil, fmt.Errorf("failed to create request for find client request: %w", err)
	}
	req.Header.Set("Content-Type", "application/json")

	resp, err := p.HTTPClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to do request for find client request: %w", err)
	}
	defer resp.Body.Close()

	var respBody entity.FindClientResp
	if resp.StatusCode != http.StatusOK {
		errResp, err := p.handleColvirRespErrStatusCode(ctx, resp)
		if err != nil {
			return nil, err
		}

		colvirErr := ErrInternalServerError
		if errResp != nil {
			switch errResp.Code {
			case 10602:
				colvirErr = ErrColvirClientNotFound
			case 10624:
				colvirErr = ErrColvirClientFindBadRequest
			}
		}

		respBody.ColvirError = errResp
		return &respBody, colvirErr
	}

	if err = json.NewDecoder(resp.Body).Decode(&respBody); err != nil {
		return nil, fmt.Errorf("failed to decode response for find client request: %w", err)
	}

	return &respBody, nil
}

func (p *ColvirProviderImpl) getRequestFindClientURL() (string, error) {
	result, err := url.JoinPath(p.V2BaseURL, "/IBClients/Customer/Find")
	if err != nil {
		return "", fmt.Errorf("failed to join path for find client request: %w", err)
	}

	return result, nil
}
