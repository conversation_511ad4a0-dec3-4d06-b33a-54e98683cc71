package colvir

import (
	"bytes"
	"context"
	"fmt"
	"net/http"

	"git.redmadrobot.com/backend-go/rmr-pkg/core/errs"
	"git.redmadrobot.com/backend-go/rmr-pkg/tools/serial"

	"git.redmadrobot.com/zaman/backend/zaman/pkg/colvir/entity"
)

const (
	colvirFindTaxPayerSoapAction = "clients-findTaxPayer"
	colvirFindTaxPayerEndpoint   = "/cxf/clients/v1"
)

func (p *ColvirProviderImpl) RequestTaxPayer(ctx context.Context, iin string) (*entity.FindTaxPayerResponse, error) {
	reqBody := entity.NewTaxPayerRequest(iin)

	var buf bytes.Buffer
	if err := serial.XMLEncode(&buf, reqBody); err != nil {
		return nil, errs.Wrapf(err, "failed to encode request body")
	}

	req, err := http.NewRequestWithContext(
		ctx,
		http.MethodPost,
		p.getRequestTaxPayerURL(),
		bytes.NewBuffer(buf.Bytes()),
	)
	if err != nil {
		return nil, errs.Wrapf(err, "failed to create new http request")
	}

	req.Header.Set("Content-Type", "application/xml;charset=UTF-8")
	req.Header.Set("SOAPAction", colvirFindTaxPayerSoapAction)

	resp, err := p.HTTPClient.Do(req)
	if err != nil {
		return nil, errs.Wrapf(err, "failed to do http request")
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		errResp, err := p.handleColvirRespErrStatusCode(ctx, resp)
		if err != nil {
			return nil, err
		}

		return &entity.FindTaxPayerResponse{
			ColvirError: errResp,
		}, ErrInternalServerError
	}

	payerResponse, err := serial.XMLDecode[entity.FindTaxPayerResponse](resp.Body)
	if err != nil {
		return nil, errs.Wrapf(err, "failed to decode http response body")
	}

	return &payerResponse, nil
}

func (p *ColvirProviderImpl) getRequestTaxPayerURL() string {
	return fmt.Sprintf("%s%s", p.BaseURL, colvirFindTaxPayerEndpoint)
}
