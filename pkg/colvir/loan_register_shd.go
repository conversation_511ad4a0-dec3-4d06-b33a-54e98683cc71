package colvir

import (
	"bytes"
	"context"
	"errors"
	"fmt"
	"net/http"
	"net/url"

	"git.redmadrobot.com/backend-go/rmr-pkg/core/errs"
	"git.redmadrobot.com/backend-go/rmr-pkg/tools/serial"
	"git.redmadrobot.com/zaman/backend/zaman/pkg/colvir/entity"
)

// RequestLoanRegisterShd отправляет запрос на регистрацию SHD кредита в Colvir
func (p *ColvirProviderImpl) RequestLoanRegisterShd(
	ctx context.Context,
	request *entity.LoanRegisterShdReq,
) (*entity.LoanRegisterShdResp, error) {
	var buf bytes.Buffer
	if err := serial.XMLEncode(&buf, request); err != nil {
		return nil, errs.Wrapf(err, "failed to encode request body for register SHD loan request")
	}

	endpoint, err := p.getRequestLoanRegisterShdEndpoint()
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequestWithContext(
		ctx,
		http.MethodPost,
		endpoint,
		bytes.NewBuffer(buf.Bytes()),
	)
	if err != nil {
		return nil, errs.Wrapf(err, "failed to create new http request")
	}

	req.Header.Set("Content-Type", "application/xml;charset=UTF-8")

	resp, err := p.HTTPClient.Do(req)
	if err != nil {
		// Создаем объект ответа с информацией об ошибке
		var respBody entity.LoanRegisterShdResp
		statusCode := http.StatusBadGateway
		if resp != nil {
			statusCode = resp.StatusCode
		}
		respBody.ColvirError = &entity.ColvirErrResp{
			Text:        "failed to do request for RequestLoanRegisterShd",
			Description: err.Error(),
			HTTPCode:    statusCode,
		}

		// Возвращаем объект ответа с пользовательской ошибкой
		return &respBody, ErrColvirServerNotResponding
	}
	defer resp.Body.Close()

	var respBody entity.LoanRegisterShdResp
	if resp.StatusCode != http.StatusOK {
		errResp, err := p.handleColvirRespErrStatusCode(ctx, resp)
		if err != nil {
			return nil, err
		}

		respBody.ColvirError = errResp
		return &respBody, ErrInternalServerError
	}

	loanRegisterShdResponse, err := serial.XMLDecode[entity.LoanRegisterShdResp](resp.Body)
	if err != nil {
		decodeErr := fmt.Errorf("failed to decode response for RequestLoanRegisterShd request: %w", err)

		errResp, colvirParseErr := p.handleColvirRespErrStatusCode(ctx, resp)
		if colvirParseErr != nil {
			return nil, errors.Join(decodeErr, colvirParseErr)
		}
		// Если статус 200, но не смогли распарсить, то создаем ошибку
		// Данная ошибка нужна для тех поддержки
		requestDetailedErr := entity.MakeColvirDetailedError(
			"RequestLoanRegisterShd",
			errResp.Text,
			errResp.Description,
			errResp.HTTPCode)

		return nil, errors.Join(decodeErr, requestDetailedErr)
	}

	return &loanRegisterShdResponse, nil
}

// getRequestLoanRegisterShdEndpoint возвращает URL эндпоинта для регистрации SHD кредита
func (p *ColvirProviderImpl) getRequestLoanRegisterShdEndpoint() (string, error) {
	result, err := url.JoinPath(p.BaseURL, colvirLoansEndpoint)
	if err != nil {
		return "", fmt.Errorf("failed to join path for loan register SHD request: %w", err)
	}

	return result, nil
}
