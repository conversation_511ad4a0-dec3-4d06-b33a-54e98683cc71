package colvir

import (
	"bytes"
	"context"
	"fmt"
	"io"
	"net/http"

	"git.redmadrobot.com/backend-go/rmr-pkg/core/errs"
	"git.redmadrobot.com/backend-go/rmr-pkg/core/logs"
	"git.redmadrobot.com/backend-go/rmr-pkg/tools/serial"

	"git.redmadrobot.com/zaman/backend/zaman/pkg/colvir/entity"
)

const (
	colvirLoadDomesticPaymentStatusSoapAction = "payment-domestic-loadDomesticPaymentStatus"
	colvirLoadDomesticPaymentStatusEndpoint   = "/cxf/payments-domestic/v1"
)

func (p *ColvirProviderImpl) RequestLoadDomesticPaymentStatus(
	ctx context.Context, paymentIDs []string,
) (*entity.LoadDomesticPaymentStatusResponse, error) {
	r := entity.NewLoadDomesticPaymentStatusRequest(paymentIDs)

	var buf bytes.Buffer
	if err := serial.XMLEncode(&buf, r); err != nil {
		return nil, errs.Wrapf(err, "failed to encode request body for RequestLoadDomesticPaymentStatus")
	}

	logs.FromContext(ctx).Debug().Msgf("RequestLoadDomesticPaymentStatus() request body: %s", buf.String())

	req, err := http.NewRequestWithContext(
		ctx,
		http.MethodPost,
		p.getRequestLoadDomesticPaymentURL(),
		bytes.NewBuffer(buf.Bytes()),
	)
	if err != nil {
		return nil, errs.Wrapf(err, "failed to create new http request for RequestLoadDomesticPaymentStatus")
	}

	req.Header.Set("Content-Type", "application/xml;charset=UTF-8")
	req.Header.Set("SOAPAction", colvirLoadDomesticPaymentStatusSoapAction)

	resp, err := p.HTTPClient.Do(req)
	if err != nil {
		return nil, errs.Wrapf(err, "failed to do http request for RequestLoadDomesticPaymentStatus")
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		bodyBytes, err := io.ReadAll(resp.Body)
		if err != nil {
			bodyBytes = []byte(err.Error())
		}

		return nil, handleStatusCode(resp.StatusCode, bodyBytes)
	}

	loadPaymentStatusResponse, err := serial.XMLDecode[entity.LoadDomesticPaymentStatusResponse](resp.Body)
	if err != nil {
		return nil, errs.Wrapf(err, "failed to decode http response body for RequestLoadDomesticPaymentStatus")
	}

	return &loadPaymentStatusResponse, nil
}

func (p *ColvirProviderImpl) getRequestLoadDomesticPaymentURL() string {
	return fmt.Sprintf("%s%s", p.BaseURL, colvirLoadDomesticPaymentStatusEndpoint)
}
