package colvir

import (
	"bytes"
	"context"
	"errors"
	"fmt"
	"net/http"
	"net/url"

	"git.redmadrobot.com/backend-go/rmr-pkg/core/errs"
	"git.redmadrobot.com/backend-go/rmr-pkg/tools/serial"
	"git.redmadrobot.com/zaman/backend/zaman/pkg/colvir/entity"
)

func (p *ColvirProviderImpl) RequestLoansCalculateTotalCost(
	ctx context.Context,
	request *entity.LoansCalculateTotalCostReq,
) (*entity.LoansCalculateTotalCostResp, error) {
	var buf bytes.Buffer
	if err := serial.XMLEncode(&buf, request); err != nil {
		return nil, errs.Wrapf(err, "failed to encode request body for calculate total cost request")
	}

	endpoint, err := p.getRequestLoansCalculateTotalCost()
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequestWithContext(
		ctx,
		http.MethodPost,
		endpoint,
		bytes.NewBuffer(buf.Bytes()),
	)
	if err != nil {
		return nil, errs.Wrapf(err, "failed to create new http request")
	}

	req.Header.Set("Content-Type", "application/xml;charset=UTF-8")

	resp, err := p.HTTPClient.Do(req)
	if err != nil {
		// Создаем объект ответа с информацией об ошибке
		var respBody entity.LoansCalculateTotalCostResp
		statusCode := http.StatusBadGateway
		if resp != nil {
			statusCode = resp.StatusCode
		}
		respBody.ColvirError = &entity.ColvirErrResp{
			Text:        "failed to do request for RequestLoansCalculateTotalCost",
			Description: err.Error(),
			HTTPCode:    statusCode,
		}

		// Возвращаем объект ответа с пользовательской ошибкой
		return &respBody, ErrColvirServerNotResponding
	}
	defer resp.Body.Close()

	var respBody entity.LoansCalculateTotalCostResp
	if resp.StatusCode != http.StatusOK {
		errResp, err := p.handleColvirRespErrStatusCode(ctx, resp)
		if err != nil {
			return nil, err
		}

		respBody.ColvirError = errResp
		return &respBody, ErrInternalServerError
	}

	calculateScheduleResponse, err := serial.XMLDecode[entity.LoansCalculateTotalCostResp](resp.Body)
	if err != nil {
		decodeErr := fmt.Errorf("failed to decode response for RequestLoansCalculateTotalCost request: %w", err)

		errResp, colvirParseErr := p.handleColvirRespErrStatusCode(ctx, resp)
		if colvirParseErr != nil {
			return nil, errors.Join(decodeErr, colvirParseErr)
		}
		// Если статус 200, но не смогли распарсить, то создаем ошибку
		// Данная ошибка нужна для тех поддержки
		requestDetailedErr := entity.MakeColvirDetailedError(
			"RequestLoansCalculateTotalCost",
			errResp.Text,
			errResp.Description,
			errResp.HTTPCode)

		return nil, errors.Join(decodeErr, requestDetailedErr)
	}

	return &calculateScheduleResponse, nil
}

func (p *ColvirProviderImpl) getRequestLoansCalculateTotalCost() (string, error) {
	result, err := url.JoinPath(p.BaseURL, colvirLoansEndpoint)
	if err != nil {
		return "", fmt.Errorf("failed to join path for load domain values request: %w", err)
	}

	return result, nil
}
