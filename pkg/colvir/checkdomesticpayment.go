package colvir

import (
	"bytes"
	"context"
	"errors"
	"fmt"
	"io"
	"net/http"

	"git.redmadrobot.com/backend-go/rmr-pkg/core/errs"
	"git.redmadrobot.com/backend-go/rmr-pkg/core/logs"
	"git.redmadrobot.com/backend-go/rmr-pkg/tools/serial"

	"git.redmadrobot.com/zaman/backend/zaman/pkg/colvir/entity"
)

const (
	colvirCheckDomesticPaymentSoapAction = "payment-domestic-checkDomesticPayment"
	colvirCheckDomesticPaymentEndpoint   = "/cxf/payments-domestic/v1"
)

func (p *ColvirProviderImpl) RequestCheckDomesticPayment(ctx context.Context, r *entity.CheckDomesticPaymentRequest) (
	*entity.CheckDomesticPaymentResponse, error,
) {
	l := logs.FromContext(ctx)

	var buf bytes.Buffer
	if err := serial.XMLEncode(&buf, r); err != nil {
		return nil, errs.Wrapf(err, "failed to encode request body for RequestCheckDomesticPayment")
	}

	l.Debug().Msgf("RequestCheckDomesticPayment() request body: %s", buf.String())

	req, err := http.NewRequestWithContext(
		ctx,
		http.MethodPost,
		p.getRequestCheckDomesticPaymentURL(),
		bytes.NewBuffer(buf.Bytes()),
	)
	if err != nil {
		return nil, errs.Wrapf(err, "failed to create new http request for RequestCheckDomesticPayment")
	}

	req.Header.Set("Content-Type", "application/xml;charset=UTF-8")
	req.Header.Set("SOAPAction", colvirCheckDomesticPaymentSoapAction)

	resp, err := p.HTTPClient.Do(req)
	if err != nil {
		l.Err(err).Msg("failed to do http request RequestCheckDomesticPayment")

		if errors.Is(err, context.Canceled) || errors.Is(err, context.DeadlineExceeded) {
			err = ErrStatusGatewayTimeout
		}

		return nil, err
	}

	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		bodyBytes, err := io.ReadAll(resp.Body)
		if err != nil {
			bodyBytes = []byte(err.Error())
		}

		l.Debug().
			Msgf("RequestCheckDomesticPayment() unexpected status, response body: %s", bodyBytes)

		return nil, handleStatusCode(resp.StatusCode, bodyBytes)
	}

	checkPaymentResponse, err := serial.XMLDecode[entity.CheckDomesticPaymentResponse](resp.Body)
	if err != nil {
		err = errs.Wrapf(err, "failed to decode http response body for RequestCheckDomesticPayment")
		return nil, err
	}

	return &checkPaymentResponse, nil
}

func (p *ColvirProviderImpl) getRequestCheckDomesticPaymentURL() string {
	return fmt.Sprintf("%s%s", p.BaseURL, colvirCheckDomesticPaymentEndpoint)
}
