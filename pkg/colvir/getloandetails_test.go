package colvir

import (
	"context"
	"fmt"
	"net/http"
	"net/http/httptest"
	"os"
	"testing"

	"git.redmadrobot.com/zaman/backend/zaman/pkg/colvir/entity"
	"github.com/google/go-cmp/cmp"
	"github.com/shopspring/decimal"
)

func TestColvirImpl_GetLoanDetails(t *testing.T) {
	srvResponse := `{
   "currency":"KZT",
   "CreatedAt":null,
   "code":"ТМ-SME-00-2025-00122",
   "fromDate":"2025-06-13T00:00:00",
   "department":{
      "name":"Акционерное общество Исламский банк Заман-Банк",
      "code":"000"
   },
   "rate":"37",
   "params":[
      {
         "param":{
            "code":"ANNUITY_SUM",
            "name":"Сумма аннуитетного платежа",
            "domainСode":"VARCHAR_30",
            "value":"38964.97"
         }
      },
      {
         "param":{
            "name":"Дата расчета коэффициента долговой нагрузки",
            "domainСode":"DDATE",
            "value":"05.03.2025",
            "code":"L_DEBTBURDRCLC"
         }
      }
   ],
   "availableAmount":"0",
   "issue":[
      {
         "date":"2025-06-13T00:00:00",
         "amount":"654008"
      }
   ],
   "UpdatedAt":null,
   "product":{
      "code":"ZB.220.01",
      "name":"Товарная мурабаха ИП (30/360)"
   },
   "toDate":"2027-06-13T00:00:00",
   "amount":"654008",
   "status":"ACTUAL",
   "purpose":{
      "code":"13",
      "name":"Пополнение оборотных средств"
   }
}`

	resp := &entity.GetLoanDetailsResp{
		Product: &entity.GetLoanDetailsRespProduct{
			Code: "ZB.220.01",
			Name: "Товарная мурабаха ИП (30/360)",
		},
		Code:     "ТМ-SME-00-2025-00122",
		FromDate: "2025-06-13T00:00:00",
		ToDate:   "2027-06-13T00:00:00",
		Amount:   decimal.NewFromFloat(654008),
		Currency: "KZT",
		Status:   "ACTUAL",
		Department: &entity.GetLoanDetailsRespDepartment{
			Code: "000",
			Name: "Акционерное общество Исламский банк Заман-Банк",
		},
		Rate: decimal.NewFromFloat(37),
		Purpose: &entity.GetLoanDetailsRespPurpose{
			Code: "13",
			Name: "Пополнение оборотных средств",
		},
		Issue: []*entity.GetLoanDetailsRespIssue{
			{
				Amount: decimal.NewFromFloat(654008),
				Date:   "2025-06-13T00:00:00",
			},
		},
		AvailableAmount: decimal.NewFromFloat(0),
		Params: entity.GetLoanDetailsRespParamsElementParamsSlice{
			{
				Param: &entity.GetLoanDetailsRespParamsElementParam{
					Code:       "ANNUITY_SUM",
					Name:       "Сумма аннуитетного платежа",
					DomainCode: "VARCHAR_30",
					Value:      "38964.97",
				},
			},
			{
				Param: &entity.GetLoanDetailsRespParamsElementParam{
					Code:       "L_DEBTBURDRCLC",
					Name:       "Дата расчета коэффициента долговой нагрузки",
					DomainCode: "DDATE",
					Value:      "05.03.2025",
				},
			},
		},
	}

	mockServer := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		if r.Header.Get("Content-Type") != "application/json;charset=UTF-8" {
			http.Error(w, "invalid content type", http.StatusUnsupportedMediaType)
			return
		}

		w.WriteHeader(http.StatusOK)
		w.Header().Set("Content-Type", "application/json;charset=UTF-8")
		_, _ = w.Write([]byte(srvResponse))
	}))
	defer mockServer.Close()

	colvir := &ColvirProviderImpl{
		HTTPClient: &http.Client{},
		V2BaseURL:  mockServer.URL,
	}

	t.Run("GetLoanDetails", func(t *testing.T) {
		got, err := colvir.GetLoanDetails(context.Background(), &entity.GetLoanDetailsReq{})
		if err != nil {
			t.Errorf("GetLoanDetails() error = %v", err)
			return
		}
		diff := cmp.Diff(got, resp)
		if diff != "" {
			t.Errorf("GetLoanDetails() got = %+v, want %+v", got, resp)
			t.Errorf("GetLoanDetails() diff = %s", diff)
		}
	})
}

func TestColvirImpl_GetLoanDetails_Real(t *testing.T) {
	// comment when you want to run this test, don't forget to uncomment the line after testing
	t.Skip("skip test")

	baseURL := os.Getenv("TEST_COLVIR_V2_BASE_URL")
	if baseURL == "" {
		t.Skip("TEST_COLVIR_V2_BASE_URL is empty")
	}

	colvir := &ColvirProviderImpl{
		HTTPClient: &http.Client{},
		V2BaseURL:  baseURL,
	}

	// Fill this request with your data
	req := &entity.GetLoanDetailsReq{
		ColvirReferenceID: "692_1714423",
		ParamsFl:          false,
	}

	if err := req.Validate(); err != nil {
		t.Errorf("GetLoanDetails() error = %v", err)
		return
	}

	t.Run("GetLoanDetails", func(t *testing.T) {
		got, err := colvir.GetLoanDetails(context.Background(), req)
		if err != nil {
			t.Errorf("GetLoanDetails() error = %v", err)
			return
		}
		fmt.Printf("%+v\n", got)
	})
}
