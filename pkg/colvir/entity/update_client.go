package entity

type (
	UpdateClientRequest struct {
		RequestID       string          `json:"request_id"`                // Уникальный идентификатор запроса
		ClientCode      string          `json:"clientCode"`                // Код клиента
		ClientDepID     string          `json:"clientDepId"`               // Идентификатор подразделения клиента
		ClientID        string          `json:"clientId"`                  // Идентификатор клиента
		LastName        *string         `json:"lastName,omitempty"`        // Фамилия
		FirstName       *string         `json:"firstName"`                 // Имя
		MiddleName      *string         `json:"middleName,omitempty"`      // Отчество
		BirthDate       *string         `json:"birthDate,omitempty"`       // Дата рождения в формате yyyy-MM-dd
		Gender          *string         `json:"gender,omitempty"`          // Пол M / F
		CitizenshipCode *string         `json:"citizenshipCode,omitempty"` // Код страны гражданства
		Addresses       []Address       `json:"addresses,omitempty"`       // Список адресов клиента
		Contacts        []ContactUpdate `json:"contact,omitempty"`         // Список контактов клиента
		DocType         *string         `json:"docType,omitempty"`         // Код типа документа
		DocSeries       *string         `json:"docSeries,omitempty"`       // Серия документа
		DocNumber       *string         `json:"docNumber,omitempty"`       // Номер документа
		DocIssueDate    *string         `json:"docIssueDate,omitempty"`    // Дата выдачи в формате yyyy-MM-dd
		DocExpireDate   *string         `json:"docExpireDate,omitempty"`   // Дата окончания действия документа yyyy-MM-dd
		DocIssuePlace   *string         `json:"docIssuePlace,omitempty"`   // Место выдачи документа
		Email           *string         `json:"email,omitempty"`           // Email
	}

	UpdateClientResp struct {
		ResultCode   string         `json:"result_code"`  // Код результата выполнения запроса
		ResultMsg    string         `json:"result_msg"`   // Текст сообщения о результате
		ClientCode   string         `json:"clientCode"`   // Код клиента
		ClientDepID  string         `json:"clientDepId"`  // Идентификатор подразделения клиента
		ClientID     string         `json:"clientId"`     // Идентификатор клиента
		ClientStatus *string        `json:"clientStatus"` // Статус карточки клиента (может отсутствовать)
		GBLErrs      []GBLError     `json:"gblerr"`
		ColvirError  *ColvirErrResp `json:"colvirErrResp,omitempty"`
	}

	ContactUpdate struct {
		Type       *string `json:"type"`   // Тип контакта - MOB - мобильный телефон
		Number     *string `json:"number"` // Номер телефона
		Base       *string `json:"base"`
		ChangeType string  `json:"changeType"` // Тип изменения контакта - UPD - обновление
	}
)
