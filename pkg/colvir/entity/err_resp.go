package entity

type (
	ColvirErrResp struct {
		Code        int    `json:"Code"`
		Status      int    `json:"Status"`
		QCTXID      string `json:"QCTXID"`
		Text        string `json:"Text"`
		Description string `json:"Description"`
		HTTPCode    int    `json:"HTTPCode"`
		Module      string `json:"Module"`
		Hint        Hint   `json:"Hint"`
		Level       Level  `json:"Level"`
		EDate       string `json:"EDate"`
	}

	Hint struct {
		Reason string `json:"Reason"`
	}

	Level struct {
		Type  string `json:"Type"`
		Value int    `json:"Value"`
	}
)
