package entity

type (
	GetClientRequest struct {
		RequestID   string `json:"request_id"`
		ClientCode  string `json:"clientCode"`
		ClientDepID string `json:"clientDepId,omitempty"`
		ClientID    string `json:"clientId,omitempty"`
	}

	GetClientResponse struct {
		ResultCode      string    `json:"result_code"`
		ResultMsg       string    `json:"result_msg"`
		FilialCode      string    `json:"filialCode"`
		ClientCode      string    `json:"clientCode"`
		ClientDepID     string    `json:"clientDepId"`
		ClientID        string    `json:"clientId"`
		Status          string    `json:"status"`
		LastName        string    `json:"lastName"`
		FirstName       string    `json:"firstName"`
		MiddleName      string    `json:"middleName"`
		BirthDate       string    `json:"birthDate"`
		Resident        string    `json:"resident"`
		ResidenceCode   string    `json:"residenceCode"`
		CitizenshipCode string    `json:"citizenshipCode"`
		Iin             string    `json:"iin"`
		DocType         string    `json:"docType"`
		Number          string    `json:"number"`
		DocIssueDate    string    `json:"docIssueDate"`
		DocExpireDate   string    `json:"docExpireDate"`
		DocIssuePlace   string    `json:"docIssuePlace"`
		Address         []Address `json:"address"`
		ContactType     string    `json:"contactType"`
		ColvirError     *ColvirErrResp
	}
)
