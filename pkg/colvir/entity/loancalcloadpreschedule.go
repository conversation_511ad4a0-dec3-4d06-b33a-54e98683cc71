package entity

import (
	"fmt"

	"github.com/shopspring/decimal"
)

type (
	LoanCalcLoadPreScheduleReq struct {
		ProductCode    string           `json:"productCode" bson:"productCode"`       // Код продукта
		BeginDate      string           `json:"beginDate" bson:"beginDate"`           // Дата начала договора
		EndDate        string           `json:"endDate" bson:"endDate"`               // Дата окончания договора
		PercentRate    decimal.Decimal  `json:"percentRate" bson:"percentRate"`       // Процентная ставка
		ThirdExpenses  *decimal.Decimal `json:"thirdExpenses" bson:"thirdExpenses"`   // сумма расходов 3х лиц (для расчета ГЭСВ)
		ClientCode     *string          `json:"clientCode" bson:"clientCode"`         // Код клиента
		JurFl          *bool            `json:"jurFl" bson:"jurFl"`                   // признак юрлица клиента
		EnterpreneurFl *bool            `json:"enterpreneurFl" bson:"enterpreneurFl"` // признак ПБОЮФЛ клиента
		ContractSum    decimal.Decimal  `json:"contractSum" bson:"contractSum"`       // сумма договора
		CurrencyCode   *string          `json:"currencyCode" bson:"currencyCode"`     // валюта договора
		PayDay         int32            `json:"payDay" bson:"payDay"`                 // день платежа
		GracePoints    *int32           `json:"gracePoints" bson:"gracePoints"`       // количество льготных пунктов по ОД
		GracePointsPrc *int32           `json:"gracePointsPrc" bson:"gracePointsPrc"` // количество льготных пунктов по процентам
		TarifCode      *string          `json:"tarifCode" bson:"tarifCode"`           // Тарифная категория
	}

	LoanCalcLoadPreScheduleDetail struct {
		Code   string          `json:"code" bson:"code"`
		Amount decimal.Decimal `json:"amount" bson:"amount"`
		Status string          `json:"status" bson:"status"`
	}

	LoanCalcLoadPreScheduleScheduleEntry struct {
		Amount       decimal.Decimal                  `json:"amount" bson:"amount"`
		Date         string                           `json:"date" bson:"date"`
		DebtAfterPay decimal.Decimal                  `json:"debtAfterPay" bson:"debtAfterPay"`
		Details      []*LoanCalcLoadPreScheduleDetail `json:"details" bson:"details"`
	}

	LoanCalcLoadPreScheduleResp struct {
		YearEffectiveRate decimal.Decimal                         `json:"yearEffectiveRate" bson:"yearEffectiveRate"`
		Schedule          []*LoanCalcLoadPreScheduleScheduleEntry `json:"schedule" bson:"schedule"`
		ColvirError       *ColvirErrResp                          `json:"colvirError" bson:"colvirError"`
	}
)

func (req *LoanCalcLoadPreScheduleReq) Validate() error {
	if req.ProductCode == "" {
		return fmt.Errorf("productCode is required")
	}
	if req.BeginDate == "" {
		return fmt.Errorf("beginDate is required")
	}
	if req.EndDate == "" {
		return fmt.Errorf("endDate is required")
	}
	if req.PercentRate == decimal.Zero {
		return fmt.Errorf("percentRate is 0")
	}
	if req.ContractSum == decimal.Zero {
		return fmt.Errorf("contractSum is 0")
	}
	if req.PayDay == 0 {
		return fmt.Errorf("payDay is required")
	}

	return nil
}
