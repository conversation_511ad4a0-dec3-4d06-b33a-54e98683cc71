package entity

import "encoding/xml"

type (
	LoadBankHolidaysRequest struct {
		XMLName xml.Name             `xml:"soapenv:Envelope"`
		Soapenv string               `xml:"xmlns:soapenv,attr"`
		V1      string               `xml:"xmlns:v1,attr"`
		Body    LoadBankHolidaysBody `xml:"soapenv:Body"`
	}

	LoadBankHolidaysBody struct {
		LoadBankHolidaysElem LoadBankHolidaysBodyElem `xml:"v1:loadBankHolidaysElem"`
	}

	LoadBankHolidaysBodyElem struct {
		StartDate    string `xml:"v1:startDate"`
		EndDate      string `xml:"v1:endDate"`
		CalendarCode string `xml:"v1:calendarCode"`
	}

	LoadBankHolidaysResponse struct {
		XMLName xml.Name `xml:"Envelope"`
		Soap    string   `xml:"soap,attr"`
		Body    struct {
			LoadBankHolidaysResponseElem struct {
				Code         string        `xml:"code"`
				Errors       *ColvirErrors `xml:"errors,omitempty"`
				CalendarCode string        `xml:"calendarCode"`
				Holiday      []struct {
					Day       int32  `xml:"day"`
					Month     int32  `xml:"month"`
					Year      int32  `xml:"year"`
					Holiday   string `xml:"holiday"`
					IsHoliday int    `xml:"isHoliday"`
					WeekDay   int32  `xml:"weekDay"`
				} `xml:"holiday"`
			} `xml:"loadBankHolidaysResponseElem"`
		} `xml:"Body"`
	}
)

func NewLoadBankHolidaysRequest(startDate, endDate string) *LoadBankHolidaysRequest {
	return &LoadBankHolidaysRequest{
		Soapenv: colvirXMLNs,
		V1:      colvirXMLNsDomainV1,
		Body: LoadBankHolidaysBody{
			LoadBankHolidaysElem: LoadBankHolidaysBodyElem{
				StartDate:    startDate,
				EndDate:      endDate,
				CalendarCode: "KZ",
			},
		},
	}
}
