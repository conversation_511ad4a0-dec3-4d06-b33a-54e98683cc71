package entity

import (
	"encoding/xml"
	"strings"
	"time"

	"github.com/google/uuid"
)

// Примеры сообщений без загрузки лимитов/блокировок
type (
	// RequestFindClientAccountsListReq представляет собой структуру запроса для получения списка счетов клиентов
	RequestFindClientAccountsListReq struct {
		ClientCodes    []string `json:"client_codes,omitempty"`    // Список кодов клиентов
		AccountNumbers []string `json:"account_numbers,omitempty"` // Список номеров счетов
	}

	FindClientAccountsListRequest struct {
		XMLName  xml.Name                      `xml:"soapenv:Envelope"`
		XMLNs    string                        `xml:"xmlns:soapenv,attr"`
		XMLNsV1  string                        `xml:"xmlns:v1,attr"`
		XMLNsV11 string                        `xml:"xmlns:v11,attr"`
		Header   *FindClientAccountsListHeader `xml:"soapenv:HeaderRequest"`
		Body     *FindClientAccountsListBody   `xml:"soapenv:Body"`
	}

	FindClientAccountsListHeader struct{}

	FindClientAccountsListBody struct {
		LoadClientAccountsListElem *LoadClientAccountsListElem `xml:"v1:loadClientAccountsListElem"`
	}

	LoadClientAccountsListElem struct {
		Head                    *Head                    `xml:"v11:head"`
		ClientCode              string                   `xml:"v1:clientCode,omitempty"`
		ClientAccount           *string                  `xml:"v1:clientAccount,omitempty"`
		ClientAccountsListQuery *ClientAccountsListQuery `xml:"v1:clientAccountsListQuery,omitempty"`
	}

	ClientAccountsListQuery struct {
		In In `xml:"v12:in"`
	}

	In struct {
		Attr  string  `xml:"attr,attr"`
		Value []Value `xml:"value"`
	}

	Value struct {
		Text string `xml:",chardata"`
		Type string `xml:"type,attr"`
	}

	FindClientAccountsListResponse struct {
		XMLName xml.Name                                `xml:"Envelope"`
		Body    *LoadClientAccountsListElemResponseBody `xml:"Body"`
	}
)

// Примеры сообщений без загрузки лимитов/блокировок
type (
	LoadClientAccountsListElemResponseBody struct {
		Text                               string                                 `xml:",chardata"`
		LoadClientAccountsListResponseElem LoadClientAccountsListElemResponseElem `xml:"loadClientAccountsListResponseElem"`
	}

	LoadClientAccountsListElemResponseElem struct {
		Text           string       `xml:",chardata"`
		Code           string       `xml:"code"`
		ResponseTime   string       `xml:"responseTime"`
		ResponseDBTime string       `xml:"responseDbTime"`
		RequestID      string       `xml:"requestId"`
		Route          string       `xml:"route"`
		AccountsList   AccountsList `xml:"accountsList"`
	}

	AccountsList struct {
		ClientAccountsListItem *ClientAccountsListItem `xml:"clientAccountsListItem"`
	}

	ClientAccountsListItem struct {
		Text           string           `xml:",chardata"`
		ClientCode     string           `xml:"clientCode"`
		ClientAccounts []ClientAccounts `xml:"clientAccounts"`
	}
)

type ClientAccounts struct {
	Text                 string             `xml:",chardata"`
	ClientCode           string             `xml:"clientCode"`
	Number               string             `xml:"number"`
	DateOpened           string             `xml:"dateOpened"`
	DateClosed           string             `xml:"dateClosed"`
	Iban                 string             `xml:"iban"`
	Type                 string             `xml:"type"`
	Currency             Currency           `xml:"currency"`
	Activfl              bool               `xml:"activfl"`       // Признак активный/пассивный счет
	StatusExtCode        string             `xml:"statusExtCode"` // Cтатус счета(A,B,C) - код для внешних систем. Согласно
	Status               Status             `xml:"status"`
	Title                string             `xml:"title"`
	OwnerName            string             `xml:"ownerName"`
	ShortName            string             `xml:"shortName"`
	Balance              float64            `xml:"balance"`
	BalanceNatVal        float64            `xml:"balanceNatVal"`
	BlockedBalance       float64            `xml:"blockedBalance"`
	BlockedBalanceNatVal float64            `xml:"blockedBalanceNatVal"`
	AccountLockCurrent   AccountLockCurrent `xml:"accountLockCurrent"`
	Branch               TypeValue          `xml:"branch"`
	Bank                 TypeValue          `xml:"bank"`
	AccountPlanType      TypeValue          `xml:"accountPlanType"`
	TariffCategory       string             `xml:"tariffCategory"` // Код тарифной категории
	AutCode              TypeValue          `xml:"autCode"`        // Группа обслуживания
	RestrictionFl        string             `xml:"restrictionFl"`  // Наличие арестов/приостановок
	Crd2Exists           bool               `xml:"crd2Exists"`     // Наличие задолженности по картотеке 2
}

type Currency struct {
	Text string `xml:",chardata"`
	Code string `xml:"code"`
	Name string `xml:"name"`
}

type Status struct {
	Text string `xml:",chardata"`
	Code string `xml:"code"`
	Name string `xml:"name"`
}

type AccountLockCurrent struct {
	Text             string           `xml:",chardata"`
	Value            TypeValue        `xml:"value"`
	AccountLocksList AccountLocksList `xml:"accountLocksList"`
}

type AccountLocksList struct {
	Text        string        `xml:",chardata"`
	AccountLock []AccountLock `xml:"accountLock"`
}

type AccountLock struct {
	Text        string    `xml:",chardata"`
	AccountCode string    `xml:"accountCode"`
	LockID      string    `xml:"lockId"`
	LockType    TypeValue `xml:"lockType"`
	FromDate    string    `xml:"fromDate"`
	ToDate      string    `xml:"toDate"`
	Description string    `xml:"description"`
	LockOrdNum  string    `xml:"lockOrdNum"`
	LockOrdDate string    `xml:"lockOrdDate"`
	LockOrgName string    `xml:"lockOrgName"`
	Refer       string    `xml:"refer"`
	IsActive    string    `xml:"isActive"`
}

type TypeValue struct {
	Text string `xml:",chardata"`
	Code string `xml:"code"`
	Name string `xml:"name"`
}

// NewFindClientAccountsListByIbanRequest создает новый запрос на получение списка счетов клиента по списку IBAN
func NewFindClientAccountsListByIbanRequest(ibans []string) *FindClientAccountsListRequest {
	ibanValues := make([]Value, len(ibans))

	for i := range ibans {
		ibanValues[i] = Value{
			Type: "string",
			Text: ibans[i],
		}
	}

	return &FindClientAccountsListRequest{
		XMLNs:    colvirXMLNs,
		XMLNsV1:  colvirXMLNsV1,
		XMLNsV11: colvirXMLNsV11,
		Header:   &FindClientAccountsListHeader{},
		Body: &FindClientAccountsListBody{
			LoadClientAccountsListElem: &LoadClientAccountsListElem{
				Head: &Head{
					RequestID: uuid.New().String(),
					Params: &Params{
						ClientType:       colvirClientType,
						InterfaceVersion: colvirIfaceVersion,
						Language:         colvirLanguageValue,
						OperationalDate:  time.Now().Format(colvirDateFormat),
					},
				},
				ClientAccountsListQuery: &ClientAccountsListQuery{
					In: In{
						Attr:  "iban",
						Value: ibanValues,
					},
				},
			},
		},
	}
}

func NewFindClientAccountsListByClientCodeRequest(clientCodes []string) *FindClientAccountsListRequest {
	clientCode := strings.Join(clientCodes, ",")

	return &FindClientAccountsListRequest{
		XMLNs:    colvirXMLNs,
		XMLNsV1:  colvirXMLNsV1,
		XMLNsV11: colvirXMLNsV11,
		Header:   &FindClientAccountsListHeader{},
		Body: &FindClientAccountsListBody{
			LoadClientAccountsListElem: &LoadClientAccountsListElem{
				Head: &Head{
					RequestID: uuid.New().String(),
					Params: &Params{
						ClientType:       colvirClientType,
						InterfaceVersion: colvirIfaceVersion,
						Language:         colvirLanguageValue,
						OperationalDate:  time.Now().Format(colvirDateFormat),
					},
				},
				ClientCode: clientCode,
			},
		},
	}
}

// NewFindClientAccountsListByAccountNumberRequest создает новый запрос на получение списка счетов клиента по номеру счета
func NewFindClientAccountsListByAccountNumberRequest(accountNumbers []string) *FindClientAccountsListRequest {
	accountNumber := strings.Join(accountNumbers, ",")

	return &FindClientAccountsListRequest{
		XMLNs:    colvirXMLNs,
		XMLNsV1:  colvirXMLNsV1,
		XMLNsV11: colvirXMLNsV11,
		Header:   &FindClientAccountsListHeader{},
		Body: &FindClientAccountsListBody{
			LoadClientAccountsListElem: &LoadClientAccountsListElem{
				Head: &Head{
					RequestID: uuid.New().String(),
					Params: &Params{
						ClientType:       colvirClientType,
						InterfaceVersion: colvirIfaceVersion,
						Language:         colvirLanguageValue,
						OperationalDate:  time.Now().Format(colvirDateFormat),
					},
				},
				ClientAccount: &accountNumber,
			},
		},
	}
}

// NewFindClientAccountsListByClientCodeAndAccountNumberRequest создает новый запрос на получение списка счетов клиента по коду клиента и номеру счета
func NewFindClientAccountsListByClientCodeAndAccountNumberRequest(clientCodes, accountNumbers []string) *FindClientAccountsListRequest {
	clientCode := strings.Join(clientCodes, ",")
	accountNumber := strings.Join(accountNumbers, ",")

	return &FindClientAccountsListRequest{
		XMLNs:    colvirXMLNs,
		XMLNsV1:  colvirXMLNsV1,
		XMLNsV11: colvirXMLNsV11,
		Header:   &FindClientAccountsListHeader{},
		Body: &FindClientAccountsListBody{
			LoadClientAccountsListElem: &LoadClientAccountsListElem{
				Head: &Head{
					RequestID: uuid.New().String(),
					Params: &Params{
						ClientType:       colvirClientType,
						InterfaceVersion: colvirIfaceVersion,
						Language:         colvirLanguageValue,
						OperationalDate:  time.Now().Format(colvirDateFormat),
					},
				},
				ClientCode:    clientCode,
				ClientAccount: &accountNumber,
			},
		},
	}
}
