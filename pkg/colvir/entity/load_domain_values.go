package entity

import "encoding/xml"

const (
	DomainCodeKNP     = "KNP"
	DomainCodeKOD     = "KOD"
	DomainCodeCountry = "COUNTRY"
)

type (
	LoadDomainValuesRequest struct {
		XMLName xml.Name                    `xml:"soapenv:Envelope"`
		Soapenv string                      `xml:"xmlns:soapenv,attr"`
		V1      string                      `xml:"xmlns:v1,attr"`
		Body    LoadDomainValuesRequestBody `xml:"soapenv:Body"`
	}

	LoadDomainValuesRequestBody struct {
		DomainCodeRequestElem LoadDomainValuesRequestBodyElem `xml:"v1:DomainCodeRequestElem"`
	}

	LoadDomainValuesRequestBodyElem struct {
		DomainCode string `xml:"v1:domainCode"`
	}

	LoadDomainValuesResponse struct {
		XMLName xml.Name `xml:"Envelope"`
		Body    struct {
			DomainValuesLoadResultElem struct {
				Code   int           `xml:"code"`
				Errors *ColvirErrors `xml:"errors,omitempty"`
				Value  []struct {
					Code        string `xml:"code"`
					Name        string `xml:"name"`
					Description string `xml:"description"`
					IsArchived  *bool  `xml:"isArchived,omitempty"`
				} `xml:"value"`
			} `xml:"DomainValuesLoadResultElem"`
		} `xml:"Body"`
	}
)

func NewLoadDomainValuesRequest(domainCode string) LoadDomainValuesRequest {
	return LoadDomainValuesRequest{
		Soapenv: colvirXMLNs,
		V1:      colvirXMLNsDomainV1,
		Body: LoadDomainValuesRequestBody{
			DomainCodeRequestElem: LoadDomainValuesRequestBodyElem{
				DomainCode: domainCode,
			},
		},
	}
}
