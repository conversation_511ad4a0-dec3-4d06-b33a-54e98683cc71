package entity

import (
	"encoding/xml"
)

type (
	ExecuteDomesticPaymentRequest struct {
		XMLName xml.Name                          `xml:"soapenv:Envelope"`
		Soapenv string                            `xml:"xmlns:soapenv,attr"`
		V1      string                            `xml:"xmlns:v1,attr"`
		V11     string                            `xml:"xmlns:v11,attr"`
		V12     string                            `xml:"xmlns:v12,attr"`
		V13     string                            `xml:"xmlns:v13,attr"`
		Body    ExecuteDomesticPaymentRequestBody `xml:"soapenv:Body"`
	}

	ExecuteDomesticPaymentRequestBody struct {
		ExecuteDomesticPaymentElem DomesticPaymentElem `xml:"v1:executeDomesticPaymentElem"`
	}
)

func NewExecuteDomesticPaymentRequest() *ExecuteDomesticPaymentRequest {
	const (
		colvirXMLNs    = "http://schemas.xmlsoap.org/soap/envelope/"
		colvirXMLNsV1  = "http://bus.colvir.com/service/payments/domestic/v1"
		colvirXMLNsV11 = "http://bus.colvir.com/common/support/v1"
		colvirXMLNsV12 = "http://bus.colvir.com/common/domain/v1"
		colvirXMLNsV13 = "http://bus.colvir.com/common/basis/v1"
	)

	return &ExecuteDomesticPaymentRequest{
		Soapenv: colvirXMLNs,
		V1:      colvirXMLNsV1,
		V11:     colvirXMLNsV11,
		V12:     colvirXMLNsV12,
		V13:     colvirXMLNsV13,
		Body: ExecuteDomesticPaymentRequestBody{
			ExecuteDomesticPaymentElem: DomesticPaymentElem{
				Head: DomesticPaymentElemHead{
					Params: DomesticPaymentElemHeadParams{
						InterfaceVersion: "1.0",
					},
				},
				Payment: DomesticPayment{
					ProcessingMethod: "normal",
					HandlingType:     0,
				},
			},
		},
	}
}

type (
	ExecuteDomesticPaymentResponse struct {
		XMLName xml.Name                           `xml:"Envelope"`
		Body    ExecuteDomesticPaymentResponseBody `xml:"Body"`
	}

	ExecuteDomesticPaymentResponseBody struct {
		ExecuteDomesticPaymentResponseElem ExecuteDomesticPaymentResponseElem `xml:"executeDomesticPaymentResponseElem"`
	}

	ExecuteDomesticPaymentResponseElem struct {
		Code   int                                   `xml:"code"`
		Result *ExecuteDomesticPaymentResponseResult `xml:"result"`
		Errors *ExecuteDomesticPaymentResponseErrors `xml:"errors"`
	}

	ExecuteDomesticPaymentResponseResult struct {
		ExecuteResult struct {
			Code int    `xml:"code"`
			Name string `xml:"name"`
		} `xml:"executeResult"`
		ColvirReferenceID string `xml:"colvirReferenceId"`
		Status            struct {
			Code string `xml:"code"`
			Name string `xml:"name"`
		} `xml:"status"`
		AcceptedDate   string  `xml:"acceptedDate"`
		DocumentNumber string  `xml:"documentNumber"`
		Date           string  `xml:"date"`
		ExecDate       *string `xml:"execDate,omitempty"`
	}

	ExecuteDomesticPaymentResponseErrors struct {
		Code     string `xml:"code"`
		Message  string `xml:"message"`
		Severity string `xml:"severity"`
	}
)
