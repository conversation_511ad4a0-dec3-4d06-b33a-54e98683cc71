package entity

import (
	"errors"

	"github.com/shopspring/decimal"
)

type (
	GetLoanDetailsReq struct {
		ColvirReferenceID string
		ParamsFl          bool
	}

	GetLoanDetailsRespProduct struct {
		Code string `json:"code"`
		Name string `json:"name"`
	}
	GetLoanDetailsRespDepartment struct {
		Code string `json:"code"`
		Name string `json:"name"`
	}
	GetLoanDetailsRespPurpose struct {
		Code string `json:"code"`
		Name string `json:"name"`
	}

	GetLoanDetailsRespIssueSlice = []*GetLoanDetailsRespIssue

	GetLoanDetailsRespIssue struct {
		Amount decimal.Decimal `json:"amount"`
		Date   string          `json:"date"`
	}

	GetLoanDetailsRespParamsElementParamsSlice = []*GetLoanDetailsRespParamsElement

	GetLoanDetailsRespParamsElement struct {
		Param *GetLoanDetailsRespParamsElementParam `json:"param"`
	}

	GetLoanDetailsRespParamsElementParam struct {
		Code       string `json:"code"`
		Name       string `json:"name"`
		DomainCode string `json:"domainСode"`
		Value      string `json:"value"`
	}
	GetLoanDetailsResp struct {
		Product         *GetLoanDetailsRespProduct                 `json:"product"`
		Code            string                                     `json:"code"`
		FromDate        string                                     `json:"fromDate"`
		ToDate          string                                     `json:"toDate"`
		Amount          decimal.Decimal                            `json:"amount"`
		Currency        string                                     `json:"currency"`
		Status          string                                     `json:"status"`
		Department      *GetLoanDetailsRespDepartment              `json:"department"`
		Rate            decimal.Decimal                            `json:"rate"`
		Purpose         *GetLoanDetailsRespPurpose                 `json:"purpose"`
		Issue           GetLoanDetailsRespIssueSlice               `json:"issue"`
		AvailableAmount decimal.Decimal                            `json:"availableAmount"`
		Params          GetLoanDetailsRespParamsElementParamsSlice `json:"params,omitempty"`
		ColvirError     *ColvirErrResp
	}
)

func (req *GetLoanDetailsReq) Validate() error {
	if req == nil {
		return errors.New("req is nil")
	}

	if req.ColvirReferenceID == "" {
		return errors.New("colvirReferenceId is required")
	}

	return nil
}
