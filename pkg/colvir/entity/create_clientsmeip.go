package entity

type (
	CreateClientIPRequest struct {
		RequestID       string             `json:"request_id"`               // Идентификатор запроса
		FilialCode      string             `json:"filialCode"`               // Код филиала подразделения
		Name            string             `json:"name"`                     // Наименование ИП
		LongName        string             `json:"longName"`                 // Длинное наименование
		RegDate         string             `json:"regDate"`                  // Дата регистрации/лицензирования организации
		IIN             string             `json:"iin"`                      // Идентификационный налоговый номер
		ClientFiz       ClientFiz          `json:"clientFiz"`                // Информация о ФЛ
		Addresses       []AddressIP        `json:"addresses,omitempty"`      // Список адресов клиента +(Массив может быть пустым)
		RegDocs         []RegDocIP         `json:"regDoc"`                   // Регистрационные документы
		Contacts        []ContactIP        `json:"contact,omitempty"`        // Контакты(телефоны, email) + (Массив может быть пустым)
		Classifications []ClassificationIP `json:"classification,omitempty"` // Список классификаторов клиента + (Массив может быть пустым)
	}

	ClientFiz struct {
		ClientCode      *string `json:"clientCode,omitempty"`      // Код клиента связанного клиента-физлица ± (+ если привязка клиента ФЛ)
		ClientDepID     *string `json:"clientDepId,omitempty"`     // Идентификатор подразделения связанного клиента-физлица ± (+ если привязка клиента ФЛ)
		ClientID        *string `json:"clientId,omitempty"`        // Идентификатор связанного клиента-физлица ± (+ если привязка клиента ФЛ)
		LastName        *string `json:"lastName,omitempty"`        // Фамилия ± (+ если новый клиент ФЛ)
		FirstName       *string `json:"firstName,omitempty"`       // Имя ± (+ если новый клиент ФЛ)
		MiddleName      *string `json:"middleName,omitempty"`      // Отчество ± (+ если новый клиент ФЛ)
		BirthDate       *string `json:"birthDate,omitempty"`       // Дата рождения ± (+ если новый клиент ФЛ)
		Gender          *string `json:"gender,omitempty"`          // Пол: M-мужской, F-женский ± (+ если новый клиент ФЛ)
		Resident        *bool   `json:"resident,omitempty"`        // Признак резидентства ± (+ если новый клиент ФЛ)
		ResidenceCode   *string `json:"residenceCode,omitempty"`   // Код страны резидентства (Буквенный) KZ ± (+ если новый клиент ФЛ)
		CitizenshipCode *string `json:"citizenshipCode,omitempty"` // Код страны гражданства (буквенный) KZ ± (+ если новый клиент ФЛ)
		DocType         *string `json:"docType,omitempty"`         // ПТ-Паспорт РК УЛ-Удостоверение личности РК ± (+ если новый клиент ФЛ)
		DocSeries       *string `json:"docSeries,omitempty"`       // Серия документа ± (+ если новый клиент ФЛ)
		DocNumber       *string `json:"docNumber,omitempty"`       // Номер документа ± (+ если новый клиент ФЛ)
		DocIssueDate    *string `json:"docIssueDate,omitempty"`    // Дата выдачи ± (+ если новый клиент ФЛ)
		DocExpireDate   *string `json:"docExpireDate,omitempty"`   // Срок действия ± (+ если новый клиент ФЛ)
		DocIssuePlace   *string `json:"docIssuePlace,omitempty"`   // Место выдачи ± (+ если новый клиент ФЛ)
	}

	AddressIP struct {
		Type          string  `json:"type"`                // 001 Место рождения 002 Место регистрации 003 Место жительства 005 Юридический (Для ИП) 006 Фактический (Для ИП)
		OwnType       *string `json:"owntype,omitempty"`   // (Может быть пустым) Примеры кодов и наименований: 0-Иное 1-Собственное 2-Аренда 3-Государственная 4-Муниципальная 5-Выкуплено 3-ми лицами
		Country       string  `json:"cntr"`                // Наименование страны: Пример: Казахстан
		Region        *string `json:"regt,omitempty"`      // (Может быть пустым) Город-регион Заполняется для городов: Астана, Алматы, Шымкент
		RegUnit       *string `json:"regu,omitempty"`      // (Может быть пустым) Город (если указан Город-регион , город не учитывается)
		RegUnitTDC    *string `json:"regu_tdc,omitempty"`  // (Может быть пустым) 09-город
		RegArea       *string `json:"regn,omitempty"`      // (Может быть пустым) Область
		RegAreaTDC    *string `json:"regn_tdc,omitempty"`  // (Может быть пустым) 02-область
		District      *string `json:"dstr,omitempty"`      // (Может быть пустым) Район
		DistrictTDC   *string `json:"dstr_tdc,omitempty"`  // (Может быть пустым) 03-район
		Settlement    *string `json:"sett,omitempty"`      // (Может быть пустым) Населенный пункт
		SettlementTDC *string `json:"sett_tdc,omitempty"`  // (Может быть пустым) Код населенного пункта
		Street        *string `json:"strt,omitempty"`      // (Может быть пустым) Улица
		StreetTDC     *string `json:"strt_tdc,omitempty"`  // (Может быть пустым) 05-улица
		Zone          *string `json:"zone,omitempty"`      // (Может быть пустым) Микрорайон
		ZoneTDC       *string `json:"zone_tdc,omitempty"`  // (Может быть пустым) 06-микрорайон
		House         *string `json:"house,omitempty"`     // (Может быть пустым) Дом
		HouseTDC      *string `json:"house_tdc,omitempty"` // (Может быть пустым) Корпус
		Body          *string `json:"body,omitempty"`      // (Может быть пустым) Строение
		Building      *string `json:"hbuild,omitempty"`    // (Может быть пустым) Владение
		Ownership     *string `json:"hvlad,omitempty"`     // (Может быть пустым) 07-дом
		Flat          *string `json:"flat,omitempty"`      // (Может быть пустым) Номер квартиры\офиса
		FlatTDC       *string `json:"flat_tdc,omitempty"`  // (Может быть пустым) 08-квартира
		Index         *string `json:"index,omitempty"`     // (Может быть пустым) Почтовый индекс
		KATO          *string `json:"kato,omitempty"`      // (Может быть пустым) Код по справочнику КАТО
		Full          *string `json:"full,omitempty"`      // (Может быть пустым) Адрес полной строкой
	}

	RegDocIP struct {
		Type       string `json:"type"`       // 6-Свидетельство о государственной регистрации индивидуального предпринимателя.Передаем: 6
		Series     string `json:"series"`     // Серия регистрационного документа
		Number     string `json:"number"`     // Номер регистрационного документа
		IssueDate  string `json:"issueDate"`  // Дата выдачи
		ExpireDate string `json:"expireDate"` // Срок действия
		RegDate    string `json:"regDate"`    // Дата регистрации
		IssuePlace string `json:"issuePlace"` // Место выдачи\Организация
	}

	ContactIP struct {
		Type   string `json:"type"`   // MOB-Мобильный телефон
		Number string `json:"number"` // Номер телефона
		Base   string `json:"base"`   // 1-основной
	}

	ClassificationIP struct {
		Code      string `json:"code"`      // OKATO-Классификатор административно-территориального деления ОКВЭД-Общий классификатор видов экономической деятельности ОКФС-Формы собственности КОПФ-Классификатор организационно-правовых форм хозяйствования ОКЭД-Общий классификатор видов экономической деятельности (статистика) Передается обязательно:-ОКВЭД-ОКАТО
		Value     string `json:"value"`     // Значение классификатора (цифровое)
		ValueName string `json:"valueName"` // Наименование значения классификатора
		Base      string `json:"base"`      // Признак: 1 - основной
	}

	CreateClientIPResp struct {
		ResultCode   string         `json:"result_code"`            // Код результата (число в виде строки) Код результата выполнения запроса: 0-Успешно 1-Ошибка
		ResultMsg    string         `json:"result_msg"`             // Сообщение результата
		ClientCode   string         `json:"clientCode,omitempty"`   // Код клиента
		ClientDepID  string         `json:"clientDepId,omitempty"`  // Идентификатор подразделения клиента
		ClientID     string         `json:"clientId,omitempty"`     // Идентификатор клиента
		ClientStatus string         `json:"clientStatus,omitempty"` // Статус клиента
		ClientFiz    *ClientFizResp `json:"clientFiz"`              // Данные клиента-физлица
		GBLErrs      []GBLError     `json:"gblerr"`                 // Список ошибок контроля gblerr
		ColvirError  *ColvirErrResp `json:"colvirErrResp"`          // Ошибки глобального уровня
	}

	// ClientFizResp - данные клиента-физлица в ответе
	ClientFizResp struct {
		ClientCode   string `json:"clientCode,omitempty"`  // Код клиента-физлица
		ClientDepID  string `json:"clientDepId,omitempty"` // Идентификатор подразделения
		ClientID     string `json:"clientId,omitempty"`    // Идентификатор клиента
		ClientStatus string `json:"clientStatus"`          // Статус клиента
	}
)
