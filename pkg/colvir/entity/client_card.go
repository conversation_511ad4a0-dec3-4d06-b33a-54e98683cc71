package entity

type (
	OpenClientCardRequest struct {
		RequestID   string `json:"request_id"`  // Уникальный идентификатор запроса
		ClientCode  string `json:"clientCode"`  // Код клиента
		ClientDepID string `json:"clientDepId"` // Идентификатор подразделения клиента
		ClientID    string `json:"clientId"`    // Идентификатор клиента
	}

	OpenClientCardResponse struct {
		ResultCode  string         `json:"result_code"` // Код результата выполнения запроса
		ResultMsg   string         `json:"result_msg"`  // Текст сообщения о результате
		FilialCode  string         `json:"filialCode"`  // Код филиала
		ClientCode  string         `json:"clientCode"`  // Код клиента
		ClientDepID string         `json:"clientDepId"` // Идентификатор подразделения клиента
		ClientID    string         `json:"clientId"`    // Идентификатор клиента
		Status      string         `json:"status"`      // Статус карточки клиента (код)
		GBLErrs     []GBLError     `json:"gblerr"`
		ColvirError *ColvirErrResp `json:"colvirErrResp"`
	}
)
