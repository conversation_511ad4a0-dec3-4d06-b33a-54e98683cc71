package entity

import (
	"encoding/xml"
	"time"

	"github.com/google/uuid"
)

const (
	colvirXMLNs           = "http://schemas.xmlsoap.org/soap/envelope/"
	colvirXMLNsV1         = "http://bus.colvir.com/service/clients/v1"
	colvirXMLNsV11        = "http://bus.colvir.com/common/support/v1"
	colvirXMLNsV12        = "http://bus.colvir.com/common/query/v1"
	colvirXMLNsV13        = "http://bus.colvir.com/common/basis/v1"
	colvirXMLNsPaymentsV1 = "http://bus.colvir.com/service/payments/domestic/v1"
	colvirXMLNsDomainV1   = "http://bus.colvir.com/common/domain/v1"
	colvirXMLNsLoans      = "http://bus.colvir.com/service/loans/v1"

	colvirClientType    = "CBS"
	colvirIfaceVersion  = "1.0"
	colvirLanguageValue = "ru"

	colvirDateFormat = "2006-01-02T15:04:05"
)

type (
	FindTaxPayerRequest struct {
		XMLName  xml.Name               `xml:"soapenv:Envelope"`
		XMLNs    string                 `xml:"xmlns:soapenv,attr"`
		XMLNsV1  string                 `xml:"xmlns:v1,attr"`
		XMLNsV11 string                 `xml:"xmlns:v11,attr"`
		Header   *FindTaxPayerReqHeader `xml:"soapenv:HeaderRequest"`
		Body     *FindTaxPayerReqBody   `xml:"soapenv:Body"`
	}

	FindTaxPayerReqHeader struct{}

	FindTaxPayerReqBody struct {
		FindTaxPayerElem *FindTaxPayerReqElem `xml:"v1:findTaxPayerElem"`
	}

	FindTaxPayerReqElem struct {
		Head *Head  `xml:"v11:head"`
		Iin  string `xml:"v1:iin"`
	}

	Head struct {
		RequestID string  `xml:"v11:requestId"`
		Params    *Params `xml:"v11:params"`
	}

	Params struct {
		ClientType       string `xml:"v11:clientType"`
		InterfaceVersion string `xml:"v11:interfaceVersion"`
		Language         string `xml:"v11:language"`
		OperationalDate  string `xml:"v11:operationalDate"`
	}

	FindTaxPayerResponse struct {
		XMLName     xml.Name              `xml:"Envelope"`
		Body        *FindTaxPayerRespBody `xml:"Body"`
		ColvirError *ColvirErrResp
	}

	FindTaxPayerRespBody struct {
		FindTaxPayerElem *FindTaxPayerResponseElem `xml:"findTaxPayerResponseElem"`
	}

	FindTaxPayerResponseElem struct {
		Code             int                             `xml:"code"`
		ResponseTime     int                             `xml:"responseTime"`
		ResponseDBTime   int                             `xml:"responseDbTime"`
		RequestID        string                          `xml:"requestId"`
		Route            string                          `xml:"route"`
		Title            string                          `xml:"title"`
		TitlePhysical    *string                         `xml:"titlePhysical,omitempty"`
		TitleLegal       *string                         `xml:"titleLegal,omitempty"`
		TypeTaxpayer     int                             `xml:"typeTaxpayer"`
		FlNotResident    int                             `xml:"flNotResident"`
		TypePp           int                             `xml:"typePP"`
		FlInactive       int                             `xml:"flInactive"`
		TaxAuthorityCode int                             `xml:"taxAuthorityCode"`
		Errors           *FindTaxPayerResponseElemErrors `xml:"errors"`
	}

	FindTaxPayerResponseElemErrors struct {
		Code     string `xml:"code"`
		Message  string `xml:"message"`
		Severity string `xml:"severity"`
	}
)

func NewTaxPayerRequest(iin string) *FindTaxPayerRequest {
	return &FindTaxPayerRequest{
		XMLNs:    colvirXMLNs,
		XMLNsV1:  colvirXMLNsV1,
		XMLNsV11: colvirXMLNsV11,
		Header:   &FindTaxPayerReqHeader{},
		Body: &FindTaxPayerReqBody{
			FindTaxPayerElem: &FindTaxPayerReqElem{
				Head: &Head{
					RequestID: uuid.New().String(),
					Params: &Params{
						ClientType:       colvirClientType,
						InterfaceVersion: colvirIfaceVersion,
						Language:         colvirLanguageValue,
						OperationalDate:  time.Now().Format(colvirDateFormat),
					},
				},
				Iin: iin,
			},
		},
	}
}
