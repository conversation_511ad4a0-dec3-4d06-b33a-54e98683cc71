package entity

import (
	"encoding/xml"
	"errors"
)

type (
	GetMissedPaymentsReq struct {
		Code       string
		Department string
	}
)

type GetMissedPaymentsResp struct {
	XMLName     xml.Name `xml:"http://schemas.xmlsoap.org/soap/envelope/ Envelope"`
	Body        Body     `xml:"Body"`
	ColvirError *ColvirErrResp
}

type Body struct {
	LoadLoanDelaysListResponse LoadLoanDelaysListResponse `xml:"LoadLoanDelaysListResponse"`
}

type LoadLoanDelaysListResponse struct {
	XMLName        xml.Name          `xml:"http://bus.colvir.com/service/loans/v1 LoadLoanDelaysListResponse"`
	Code           string            `xml:"http://bus.colvir.com/common/support/v1 code"`
	ResponseTime   int               `xml:"http://bus.colvir.com/common/support/v1 responseTime"`
	ResponseDBTime int               `xml:"http://bus.colvir.com/common/support/v1 responseDbTime"`
	RequestID      string            `xml:"http://bus.colvir.com/common/support/v1 requestId"`
	Route          string            `xml:"http://bus.colvir.com/common/support/v1 route"`
	Delays         Delays            `xml:"http://bus.colvir.com/service/loans/v1 delays"`
	Errors         *ColvirRespErrors `xml:"http://bus.colvir.com/common/support/v1 errors"`
}

type Delays struct {
	DelayList []Delay `xml:"http://bus.colvir.com/service/loans/v1 delay"`
}

type Delay struct {
	External       int    `xml:"http://bus.colvir.com/service/loans/v1 external"`
	Agreement      string `xml:"http://bus.colvir.com/service/loans/v1 agreement"`
	Department     string `xml:"http://bus.colvir.com/service/loans/v1 department"`
	BankCode       string `xml:"http://bus.colvir.com/service/loans/v1 bankCode"`
	FromDate       string `xml:"http://bus.colvir.com/service/loans/v1 fromDate"`
	HasDelay       int    `xml:"http://bus.colvir.com/service/loans/v1 hasDelay"`
	DelayDaysCount int    `xml:"http://bus.colvir.com/service/loans/v1 delayDaysCount"`
}

type ColvirRespErrors struct {
	Code     string `xml:"http://bus.colvir.com/common/support/v1 code"`
	Message  string `xml:"http://bus.colvir.com/common/support/v1 message"`
	Severity string `xml:"http://bus.colvir.com/common/support/v1 severity"`
}

func (r *GetMissedPaymentsResp) CheckError() error {
	colvirErrors := r.Body.LoadLoanDelaysListResponse.Errors

	if colvirErrors != nil {
		return errors.New(colvirErrors.Message)
	}

	return nil
}
