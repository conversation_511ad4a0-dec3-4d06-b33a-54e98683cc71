package entity

import (
	"encoding/xml"
)

const (
	colvirXMLNSLoansV1   = "http://bus.colvir.com/service/loans/v1"
	colvirXMLNSSupportV1 = "http://bus.colvir.com/common/support/v1"
	colvirXMLNSBasisV1   = "http://bus.colvir.com/common/basis/v1"
	colvirXMLNSDomainV1  = "http://bus.colvir.com/common/domain/v1"
)

type (
	LoansCalculateScheduleReq struct {
		XMLName xml.Name                      `xml:"soapenv:Envelope"`
		Soapenv string                        `xml:"xmlns:soapenv,attr"`
		V1      string                        `xml:"xmlns:v1,attr"`
		V11     string                        `xml:"xmlns:v11,attr"`
		V12     string                        `xml:"xmlns:v12,attr"`
		Header  string                        `xml:"soapenv:HeaderRequest"`
		Body    LoansCalculateScheduleReqBody `xml:"soapenv:Body"`
	}

	LoansCalculateScheduleReqBody struct {
		CalculationScheduleRequest CalculationScheduleRequest `xml:"v1:calculationScheduleRequest"`
	}

	CalculationScheduleRequest struct {
		Head              CalculationScheduleRequestHead `xml:"v11:head"`
		ColvirReferenceID *string                        `xml:"v12:colvirReferenceId"`
		AgreementCode     *string                        `xml:"v12:agreementCode"`
		ClientCode        *string                        `xml:"v12:clientCode"`
		AltFilter         *AgreementIn                   `xml:"v12:altFilter"`
	}

	CalculationScheduleRequestHead struct {
		RequestID *string                               `xml:"v11:requestId"`
		SessionID *string                               `xml:"v11:sessionId"`
		ProcessID *string                               `xml:"v11:processId"`
		Params    *CalculationScheduleRequestHeadParams `xml:"v11:params"`
	}

	CalculationScheduleRequestHeadParams struct {
		ClientType       *string `xml:"v11:clientType"`
		InterfaceVersion *string `xml:"v11:interfaceVersion"`
		Language         *string `xml:"v11:language"`
		OperationalDate  *string `xml:"v11:operationalDate"`
		ClientTimeout    *string `xml:"v11:clientTimeout"`
	}

	AgreementIn struct {
		BusinessObject    string `xml:"businessObject"`
		ColvirReferenceID string `xml:"colvirReferenceId"`
	}

	LoansCalculateScheduleResp struct {
		XMLName     xml.Name                    `xml:"Envelope"`
		Soap        string                      `xml:"soap,attr"`
		Body        CalculationScheduleRespBody `xml:"Body"`
		ColvirError *ColvirErrResp
	}

	CalculationScheduleRespBody struct {
		CalculationScheduleResponse CalculationScheduleResponse `xml:"calculationScheduleResponse"`
	}

	CalculationScheduleResponse struct {
		Code              *string                            `xml:"code"`
		ResponseTime      *string                            `xml:"responseTime"`
		ResponseDBTime    *string                            `xml:"responseDbTime"`
		RequestID         *string                            `xml:"requestId"`
		Route             *string                            `xml:"route"`
		AgreementCode     *string                            `xml:"agreementCode"`
		AgreementDepCode  *string                            `xml:"agreementDepCode"`
		ClientCode        *string                            `xml:"clientCode"`
		ColvirReferenceID *string                            `xml:"colvirReferenceId"`
		ExecuteResult     *ExecuteResult                     `xml:"executeResult"`
		ExecuteInfo       *ExecuteInfo                       `xml:"executeInfo"`
		Attributes        []Attribute                        `xml:"attributes"`
		Errors            *CalculationScheduleResponseErrors `xml:"errors"`
	}

	ExecuteResult struct {
		Code        *string `xml:"code"`
		Name        *string `xml:"name"`
		Description *string `xml:"description"`
	}

	ExecuteInfo struct {
		Code     *string `xml:"code"`
		Name     *string `xml:"name"`
		Severity string  `xml:"severity"`
	}

	Attribute struct {
		Code        *string `xml:"code"`
		Name        *string `xml:"name"`
		Description *string `xml:"description"`
		Value       *string `xml:"value"`
	}

	CalculationScheduleResponseErrors struct {
		Code     string  `xml:"code"`
		Message  string  `xml:"message"`
		Severity *string `xml:"severity"`
	}
)

func NewLoansCalculateScheduleRequest() LoansCalculateScheduleReq {
	return LoansCalculateScheduleReq{
		Soapenv: colvirXMLNs,
		V1:      colvirXMLNSLoansV1,
		V11:     colvirXMLNSSupportV1,
		V12:     colvirXMLNSBasisV1,
	}
}
