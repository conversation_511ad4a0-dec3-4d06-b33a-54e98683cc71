package entity

import (
	"encoding/xml"
)

type (
	LoansCalculateTotalCostReq struct {
		XMLName xml.Name                    `xml:"soapenv:Envelope"`
		Soapenv string                      `xml:"xmlns:soapenv,attr"`
		V1      string                      `xml:"xmlns:v1,attr"`
		V11     string                      `xml:"xmlns:v11,attr"`
		V12     string                      `xml:"xmlns:v12,attr"`
		V13     string                      `xml:"xmlns:v13,attr"`
		Header  string                      `xml:"soapenv:HeaderRequest"`
		Body    LoansCalculateTotalCostBody `xml:"soapenv:Body"`
	}

	LoansCalculateTotalCostBody struct {
		CalculateTotalCostRequest CalculateTotalCostRequest `xml:"v1:calculateTotalCostRequest"`
	}

	CalculateTotalCostRequest struct {
		Head              CalculateTotalCostRequestHead `xml:"v11:head"`
		ColvirReferenceID *string                       `xml:"v12:colvirReferenceId"`
		AgreementCode     *string                       `xml:"v12:agreementCode"`
		ClientCode        *string                       `xml:"v12:clientCode"`
		RateReasonCode    string                        `xml:"v1:rateReasonCode"`
		DateRecalc        *string                       `xml:"v1:dateRecalc"`
	}

	CalculateTotalCostRequestHead struct {
		RequestID *string                              `xml:"v11:requestId"`
		SessionID *string                              `xml:"v11:sessionId"`
		ProcessID *string                              `xml:"v11:processId"`
		Params    *CalculateTotalCostRequestHeadParams `xml:"v11:params"`
	}

	CalculateTotalCostRequestHeadParams struct {
		ClientType       *string `xml:"v11:clientType"`
		InterfaceVersion *string `xml:"v11:interfaceVersion"`
		Language         *string `xml:"v11:language"`
		OperationalDate  *string `xml:"v11:operationalDate"`
		ClientTimeout    *string `xml:"v11:clientTimeout"`
	}

	LoansCalculateTotalCostResp struct {
		XMLName     xml.Name                   `xml:"Envelope"`
		Soap        string                     `xml:"soap,attr"`
		Body        CalculateTotalCostRespBody `xml:"Body"`
		ColvirError *ColvirErrResp
	}

	CalculateTotalCostRespBody struct {
		CalculateTotalCostResponse CalculateTotalCostResponse `xml:"calculateTotalCostResponse"`
	}

	CalculateTotalCostResponse struct {
		Code              *string                           `xml:"code"`
		ResponseTime      *string                           `xml:"responseTime"`
		ResponseDBTime    *string                           `xml:"responseDbTime"`
		RequestID         *string                           `xml:"requestId"`
		Route             *string                           `xml:"route"`
		AgreementCode     *string                           `xml:"agreementCode"`
		AgreementDepCode  *string                           `xml:"agreementDepCode"`
		ClientCode        *string                           `xml:"clientCode"`
		ColvirReferenceID *string                           `xml:"colvirReferenceId"`
		ExecuteResult     *ExecuteResult                    `xml:"executeResult"`
		ExecuteInfo       *ExecuteInfo                      `xml:"executeInfo"`
		Attributes        []Attribute                       `xml:"attributes"`
		Errors            *CalculateTotalCostResponseErrors `xml:"errors"`
	}

	CalculateTotalCostResponseErrors struct {
		Code     string  `xml:"code"`
		Message  string  `xml:"message"`
		Severity *string `xml:"severity"`
	}
)

func NewLoansCalculateTotalCostRequest() LoansCalculateTotalCostReq {
	return LoansCalculateTotalCostReq{
		Soapenv: colvirXMLNs,
		V1:      colvirXMLNSLoansV1,
		V11:     colvirXMLNSSupportV1,
		V12:     colvirXMLNSBasisV1,
		V13:     colvirXMLNSDomainV1,
	}
}
