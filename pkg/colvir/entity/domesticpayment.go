package entity

type (
	DomesticPaymentElem struct {
		Head    DomesticPaymentElemHead `xml:"v11:head"`
		Payment DomesticPayment         `xml:"v1:payment"`
	}

	DomesticPaymentElemHead struct {
		Params DomesticPaymentElemHeadParams `xml:"v11:params"`
	}

	DomesticPaymentElemHeadParams struct {
		ClientType       string `xml:"v11:clientType"`
		InterfaceVersion string `xml:"v11:interfaceVersion"`
	}

	DomesticPayment struct {
		ProcessingMethod string                     `xml:"v12:processingMethod"` // normal
		DocumentType     DomainValueCode            `xml:"v12:documentType"`
		Code             string                     `xml:"v12:code"`
		Amount           string                     `xml:"v12:amount"`
		Currency         string                     `xml:"v12:currency"`
		DocumentDate     string                     `xml:"v12:documentDate"`
		ValueDate        string                     `xml:"v12:valueDate"`
		HandlingType     uint32                     `xml:"v12:handlingType"` // 0
		Payer            DomesticPaymentPayer       `xml:"v13:payer"`
		Beneficiary      DomesticPaymentBeneficiary `xml:"v13:beneficiary"`
		PaymentDetails   string                     `xml:"v13:paymentDetails"`
		PurposeCode      DomainValueCode            `xml:"v13:purposeCode"`
		BeneficiaryBank  BeneficiaryBank            `xml:"v1:beneficiaryBank"`
		RealPayer        *RealPaymentSide           `xml:"v1:realPayer,omitempty"`
		RealBeneficiary  *RealPaymentSide           `xml:"v1:realBeneficiary,omitempty"`
	}

	DomesticPaymentPayer struct {
		TaxIdentificationNumber string `xml:"v13:taxIdentificationNumber"`
		AccountIban             string `xml:"v13:accountIban"`
		CustomerType            string `xml:"v13:customerType"`
	}

	DomesticPaymentBeneficiary struct {
		TaxIdentificationNumber string          `xml:"v13:taxIdentificationNumber"`
		Name                    string          `xml:"v13:name"`
		PartyCode               DomainValueCode `xml:"v13:partyCode"`
		AccountIban             string          `xml:"v13:accountIban"`
	}

	DomainValueCode struct {
		Code string `xml:"v12:code"`
	}

	BeneficiaryBank struct {
		Bic  string `xml:"v12:bic"`
		Name string `xml:"v12:name"`
	}

	RealPaymentSide struct {
		Name                    string `xml:"v1:name"`
		TaxIdentificationNumber string `xml:"v1:taxIdentificationNumber"`
		LegalFl                 string `xml:"v1:legalFl"`
		ResidentFl              string `xml:"v1:residentFl"`
		CountryIsoCode          string `xml:"v1:countryISOCode"`
	}
)
