package entity

import "github.com/shopspring/decimal"

type (
	ProvidingLoanReq struct {
		ColvirReferenceID string          `json:"colvirReferenceId"`
		Amount            *string         `json:"amount"`
		Number            string          `json:"number"`
		TypeOfPayment     *string         `json:"typeOfPayment"`
		PaymentDetails    *PaymentDetails `json:"paymentDetails"`
		UseDetails        *UseDetails     `json:"useDetails"`
	}

	PaymentDetails struct {
		Account         *string          `json:"account"`
		BankCode        *string          `json:"bankCode"`
		Name            string           `json:"name"`
		IIN             *string          `json:"iin"`
		Pinfl           *string          `json:"pinfl"`
		Description     string           `json:"description"`
		KNP             *string          `json:"knp"`
		AnorFlag        *decimal.Decimal `json:"anorFlag"`
		TreasuryAccount *string          `json:"treasuryAccount"`
	}

	UseDetails struct {
		Partner     *string          `json:"partner"`
		ProductID   *string          `json:"productId"`
		BankCode    *string          `json:"bankCode"`
		Account     *string          `json:"account"`
		Amount      *decimal.Decimal `json:"amount"`
		CmsStrategy *string          `json:"cmsStrategy"`
		CmsAmount   *decimal.Decimal `json:"cmsAmount"`
		CmsBnkCode  *string          `json:"cmsBnkCode"`
		CmsAccount  *string          `json:"cmsAccount"`
		OnlineFl    *string          `json:"onlineFl"`
	}

	ProvidingLoanResp struct {
		ColvirReferenceID string         `json:"colvirReferenceId"`
		ProductID         *string        `json:"productId"`
		ColvirError       *ColvirErrResp `json:"-"`
	}
)
