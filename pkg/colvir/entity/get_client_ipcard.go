package entity

type (
	GetClientIPCardRequest struct {
		RequestID   string `json:"request_id"`  // Уникальный идентификатор запроса
		ClientCode  string `json:"clientCode"`  // Код клиента
		ClientDepID string `json:"clientDepId"` // Идентификатор подразделения клиента
		ClientID    string `json:"clientId"`
	}

	GetClientIPCardResponse struct {
		ResultCode  string         `json:"result_code"` // Код результата выполнения запроса
		ResultMsg   string         `json:"result_msg"`  // Текст сообщения о результате
		FilialCode  string         `json:"filialCode"`  // Код филиала
		ClientCode  string         `json:"clientCode"`  // Код клиента
		ClientDepID string         `json:"clientDepId"` // Идентификатор подразделения клиента
		ClientID    string         `json:"clientId"`    // Идентификатор клиента
		Status      string         `json:"status"`      // Статус карточки клиента (код)
		ColvirError *ColvirErrResp `json:"colvirErrResp"`
	}
)
