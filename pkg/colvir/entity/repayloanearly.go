package entity

import (
	"encoding/xml"
	"time"
)

const (
	RepaymentTypeFull     RepaymentType = "WSHD_FULL"      // Полное досрочное погашение
	RepaymentTypeDecEqual RepaymentType = "WSHD_DEC_EQUAL" // Частичное досрочное погашение с уменьшением суммы ежемесячного платежа
	RepaymentTypeOdOnly   RepaymentType = "ODONLY"         // Частичное погашение основного долга
)

type (
	RepayLoanEarlyReq struct {
		XMLName xml.Name              `xml:"soapenv:Envelope"`
		Soapenv string                `xml:"xmlns:soapenv,attr"`
		V1      string                `xml:"xmlns:v1,attr"`
		V11     string                `xml:"xmlns:v11,attr"`
		V12     string                `xml:"xmlns:v12,attr"`
		V13     string                `xml:"xmlns:v13,attr"`
		Header  string                `xml:"soapenv:Header"`
		Body    RepayLoanEarlyReqBody `xml:"soapenv:Body"`
	}

	RepayLoanEarlyReqBody struct {
		RepayLoanEarlyRequest RepayLoanEarlyRequest `xml:"v1:repayLoanEarlyRequest"`
	}

	RepayLoanEarlyReqBodyHead struct {
		Params *Params `xml:"v11:params"`
	}

	RepayLoanEarlyRequest struct {
		Head              RepayLoanEarlyReqBodyHead `xml:"v11:head"`
		ColvirReferenceID string                    `xml:"v12:colvirReferenceId"` //
		Amount            float32                   `xml:"v1:amount"`             // Сумма погашения
		Repayment         RepaymentType             `xml:"v1:repayment"`          // Метод досрочного погашения
		Source            string                    `xml:"v1:source"`             // Источник средств для досрочного погашения.
		SourseOrdNum      int                       `xml:"v1:sourceOrdNum"`       // Порядковый номер источника средств
		PetitionDate      string                    `xml:"v1:petitionDate"`       // Дата заявления.
		PetitionNumber    string                    `xml:"v1:petitionNumber"`     // Номер заявления. Всегда передаем 1.
	}

	RepaymentType string

	RepayLoanEarlyResp struct {
		Body struct {
			RepayLoanEarlyResponse RepayLoanEarlyResponse `xml:"repayLoanEarlyResponse"`
		} `xml:"Body"`
	}

	RepayLoanEarlyResponse struct {
		Code              int32                           `xml:"code"`
		ResponseTime      int32                           `xml:"responseTime"`
		ResponseDBTime    int32                           `xml:"responseDbTime"`
		RequestID         string                          `xml:"requestId"`
		Route             string                          `xml:"route"`
		Title             string                          `xml:"title"`
		ColvirReferenceID string                          `xml:"colvirReferenceId"`
		AgreementCode     string                          `xml:"agreementCode"`
		ClientCode        string                          `xml:"clientCode"`
		ExecuteResult     RepayLoanEarlyRespExecuteResult `xml:"executeResult"`
		Attributes        []Attribute                     `xml:"attributes"`
		Errors            *ColvirErrors                   `xml:"errors,omitempty"`
	}

	RepayLoanEarlyRespExecuteResult struct {
		Code string `xml:"code"`
		Name string `xml:"name"`
	}
)

func NewRepayLoanEarlyRequest() *RepayLoanEarlyReq {
	return &RepayLoanEarlyReq{
		Soapenv: colvirXMLNs,
		V1:      colvirXMLNSLoansV1,
		V11:     colvirXMLNsV11,
		V12:     colvirXMLNSBasisV1,
		V13:     colvirXMLNsDomainV1,
		Body: RepayLoanEarlyReqBody{
			RepayLoanEarlyRequest: RepayLoanEarlyRequest{
				Head: RepayLoanEarlyReqBodyHead{
					Params: &Params{
						ClientType:       colvirClientType,
						InterfaceVersion: colvirIfaceVersion,
						Language:         colvirLanguageValue,
						OperationalDate:  time.Now().Format(colvirDateFormat),
					},
				},
				Source:         "A", // Всегда передаем А - Безналично.
				SourseOrdNum:   1,
				PetitionNumber: "1",
			},
		},
	}
}
