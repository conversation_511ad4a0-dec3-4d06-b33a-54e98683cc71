package entity

import (
	"errors"
)

type (
	GetAllLoansReq struct {
		ClientCode string `json:"client_code"`
		DetailFl   bool   `json:"detail_fl"`
		AmountsFl  bool   `json:"amounts_fl"`
		ClosedFl   bool   `json:"closed_fl"`
	}

	Product struct {
		Code string
		Name string
	}

	Department struct {
		Code string
		Name string
	}

	Issue struct {
		Amount int64
		Date   string // ISO 8601 format
	}

	Purpose struct {
		Code string
		Name string
	}

	LoansItemDetails struct {
		Department      *Department
		Rate            float32
		Purpose         *Purpose
		Issue           []*Issue
		AvailableAmount int64
	}

	Item struct {
		ColvirReferenceID string
		Product           *Product
		Code              string
		FromDate          string
		ToDate            string
		Amount            int64
		Currency          string
		Status            string
		Details           *LoansItemDetails
	}

	GetAllLoansResp []Item

	GetAllLoansRespWrapper struct {
		AllLoans    *GetAllLoansResp
		ColvirError *ColvirErrResp
	}
)

func (r *GetAllLoansReq) Validate() error {
	if r == nil {
		return errors.New("GetAllLoansReq is nil")
	}

	if r.ClientCode == "" {
		return errors.New("ClientCode is empty")
	}

	return nil
}
