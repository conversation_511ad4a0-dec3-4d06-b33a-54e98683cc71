package entity

import (
	"fmt"

	"github.com/shopspring/decimal"
)

type (
	SaveLoanRequest struct {
		CliCode       string          `json:"cliCode"`       // Код клиента в Colvir (хранится на платформе)
		DeaCode       *string         `json:"deaCode"`       // Номер договора
		DepCode       string          `json:"depCode"`       // Подразделение договора
		SellDep       string          `json:"sellDep"`       // Точка продажи
		BalDep        string          `json:"balDep"`        // Балансовое подразделение
		SrvDep        string          `json:"srvDep"`        // Подразделение обслуживания
		ProdCode      string          `json:"prodCode"`      // Код продукта
		FromDate      string          `json:"fromdate"`      // Дата начала
		ToDate        *string         `json:"todate"`        // Дата окончания
		Period        *string         `json:"period"`        // Срок договора
		Amount        decimal.Decimal `json:"amount"`        // Сумма кредита
		Currency      string          `json:"currency"`      // Валюта (всегда KZT)
		Purpose       string          `json:"purpose"`       // Цель кредитования
		Params        []Parameter     `json:"params"`        // Параметры договора
		InterestValue string          `json:"interestValue"` // Процентная ставка
		Description   *string         `json:"description"`   // Комментарий
		ConGroupCodes []Parameter     `json:"conGroupCodes"` // Значения аналитик
		RegDate       string          `json:"regDate"`       // Дата регистрации
		CreditSource  []Source        `json:"creditSource"`  // Список целей использования с процентами (справочник PURPOSEOFLOAN)
		FinanceSource []Source        `json:"financeSource"` // Список источников финансирования с процентами
		InitState     *string         `json:"initState"`     // Начальное состояние
	}

	SaveLoanResponse struct {
		ColvirReferenceID string `json:"colvirReferenceId"` // Идентификатор созданного договора
		CliCode           string `json:"cliCode"`           // Код клиента
		DeaCode           string `json:"deaCode"`           // Номер договора
		DepCode           string `json:"depCode"`           // Код подразделения договора
		Status            string `json:"status"`            // Статус выполнения операции
		Result            string `json:"result"`            // Описание статуса выполнения операции
		ColvirError       *ColvirErrResp
	}

	Parameter struct {
		Code  string `json:"code"`
		Value string `json:"value"`
	}

	Source struct {
		Source  string  `json:"source"`
		Percent float32 `json:"percent"`
	}
)

func MakeColvirDetailedError(method string, text, description string, httpCode int) error {
	if text == "" && description == "" && httpCode == 0 {
		return nil
	}
	return fmt.Errorf(
		"colvir %s failed: %s – %s (code: %d)",
		method,
		text,
		description,
		httpCode,
	)
}
