package entity

import "encoding/xml"

type (
	LoadDomainHierarchyValuesRequest struct {
		XMLName xml.Name                             `xml:"soapenv:Envelope"`
		Soapenv string                               `xml:"xmlns:soapenv,attr"`
		V1      string                               `xml:"xmlns:v1,attr"`
		Body    LoadDomainHierarchyValuesRequestBody `xml:"soapenv:Body"`
	}

	LoadDomainHierarchyValuesRequestBody struct {
		DomainCodeRequestElem LoadDomainValuesRequestBodyElem `xml:"v1:DomainHierarchyValuesRequestElem"`
	}

	LoadDomainHierarchyValuesResponse struct {
		XMLName xml.Name `xml:"Envelope"`
		Body    struct {
			DomainValuesLoadResultElem struct {
				Code   int           `xml:"code"`
				Errors *ColvirErrors `xml:"errors,omitempty"`
				Value  []struct {
					Code        string  `xml:"code"`
					Name        string  `xml:"name"`
					Description string  `xml:"description"`
					IsArchived  *bool   `xml:"isArchived,omitempty"`
					IDHi        *string `xml:"idHi,omitempty"`
				} `xml:"value"`
			} `xml:"DomainHierarchyValuesLoadResult"`
		} `xml:"Body"`
	}
)

func NewLoadDomainHierarchyValuesRequest(domainCode string) LoadDomainHierarchyValuesRequest {
	return LoadDomainHierarchyValuesRequest{
		Soapenv: colvirXMLNs,
		V1:      colvirXMLNsDomainV1,
		Body: LoadDomainHierarchyValuesRequestBody{
			DomainCodeRequestElem: LoadDomainValuesRequestBodyElem{
				DomainCode: domainCode,
			},
		},
	}
}
