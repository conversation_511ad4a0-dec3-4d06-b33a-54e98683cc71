package entity

import (
	"fmt"

	"github.com/shopspring/decimal"
)

type (
	GetLoanScheduleReq struct {
		ColvirReferenceID string
	}

	GetLoanScheduleRespElementDetailsElement struct {
		Code   string          `json:"code"`
		Amount decimal.Decimal `json:"amount"`
		Status string          `json:"status"`
	}

	GetLoanScheduleRespElementDetailsSlice []GetLoanScheduleRespElementDetailsElement

	GetLoanScheduleRespElement struct {
		Amount       decimal.Decimal                        `json:"amount"`
		Date         string                                 `json:"date"`
		DebtAfterPay decimal.Decimal                        `json:"debtAfterPay"`
		Details      GetLoanScheduleRespElementDetailsSlice `json:"details"`
	}

	GetLoanScheduleResp = []*GetLoanScheduleRespElement

	GetLoanScheduleRespWrapper struct {
		Schedule    GetLoanScheduleResp
		ColvirError *ColvirErrResp
	}
)

func (req *GetLoanScheduleReq) Validate() error {
	if req.ColvirReferenceID == "" {
		return fmt.Errorf("colvirReferenceID is required")
	}

	return nil
}
