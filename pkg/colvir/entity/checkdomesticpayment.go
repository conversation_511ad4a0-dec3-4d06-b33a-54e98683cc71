package entity

import (
	"encoding/xml"
)

// https://rmrkz.atlassian.net/wiki/spaces/ZMNRET/pages/17563927/checkDomesticPayment
type (
	CheckDomesticPaymentRequest struct {
		XMLName xml.Name                        `xml:"soapenv:Envelope"`
		Soapenv string                          `xml:"xmlns:soapenv,attr"`
		V1      string                          `xml:"xmlns:v1,attr"`
		V11     string                          `xml:"xmlns:v11,attr"`
		V12     string                          `xml:"xmlns:v12,attr"`
		V13     string                          `xml:"xmlns:v13,attr"`
		Body    CheckDomesticPaymentRequestBody `xml:"soapenv:Body"`
	}

	CheckDomesticPaymentRequestBody struct {
		CheckDomesticPaymentElem DomesticPaymentElem `xml:"v1:checkDomesticPaymentElem"`
	}
)

func NewCheckDomesticPaymentRequest() *CheckDomesticPaymentRequest {
	const (
		colvirXMLNs    = "http://schemas.xmlsoap.org/soap/envelope/"
		colvirXMLNsV1  = "http://bus.colvir.com/service/payments/domestic/v1"
		colvirXMLNsV11 = "http://bus.colvir.com/common/support/v1"
		colvirXMLNsV12 = "http://bus.colvir.com/common/domain/v1"
		colvirXMLNsV13 = "http://bus.colvir.com/common/basis/v1"
	)

	return &CheckDomesticPaymentRequest{
		Soapenv: colvirXMLNs,
		V1:      colvirXMLNsV1,
		V11:     colvirXMLNsV11,
		V12:     colvirXMLNsV12,
		V13:     colvirXMLNsV13,
		Body: CheckDomesticPaymentRequestBody{
			CheckDomesticPaymentElem: DomesticPaymentElem{
				Head: DomesticPaymentElemHead{
					Params: DomesticPaymentElemHeadParams{
						InterfaceVersion: "1.0",
					},
				},
				Payment: DomesticPayment{
					ProcessingMethod: "normal",
					HandlingType:     0,
				},
			},
		},
	}
}

type (
	CheckDomesticPaymentResponse struct {
		XMLName xml.Name                         `xml:"Envelope"`
		Body    CheckDomesticPaymentResponseBody `xml:"Body"`
	}

	CheckDomesticPaymentResponseBody struct {
		CheckDomesticPaymentResponseElem CheckDomesticPaymentResponseElem `xml:"checkDomesticPaymentResponseElem"`
	}

	CheckDomesticPaymentResponseElem struct {
		Code   int                                     `xml:"code"`
		Result *CheckDomesticPaymentResponseElemResult `xml:"result"`
		Errors *CheckDomesticPaymentResponseErrors     `xml:"errors"`
	}

	CheckDomesticPaymentResponseElemResult struct {
		ValidationMessage *DomesticPaymentResponseValidationMessage `xml:"validationMessage"`
	}

	DomesticPaymentResponseValidationMessage struct {
		Code     string `xml:"code"`
		Message  string `xml:"message"`
		Details  string `xml:"details"`
		Context  string `xml:"context"`
		Severity string `xml:"severity"`
	}

	CheckDomesticPaymentResponseErrors struct {
		Code     string `xml:"code"`
		Message  string `xml:"message"`
		Severity string `xml:"severity"`
	}
)
