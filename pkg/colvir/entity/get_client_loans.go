package entity

type (
	GetClientLoansRequest struct {
		ClientCode string `json:"clientCode"`
	}

	GetClientLoansData struct {
		ColvirReferenceID string `json:"colvirReferenceId"`
		Product           Product
		Code              string `json:"code"`
		FromDate          string `json:"fromDate"`
		ToDate            string `json:"toDate"`
		Amount            int    `json:"amount"`
		Currency          string `json:"currency"`
		Status            string `json:"status"`
	}

	GetClientLoansResp struct {
		Data        []GetClientLoansData `json:"data"`
		ColvirError *ColvirErrResp
	}
)
