package colvir

import (
	"context"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/aws/smithy-go/time"
	"github.com/google/go-cmp/cmp"
	"github.com/google/uuid"
	"github.com/shopspring/decimal"
	"github.com/stretchr/testify/require"

	"git.redmadrobot.com/zaman/backend/zaman/pkg/colvir/entity"
)

var (
	GetLoansAmountSuccess = `{
    "mainDebt": 770000,
    "overduеDebt": 0,
    "amountInterest": 705.83,
    "amountInterestOverdebt": 0,
    "overdueInterest": 0,
    "overdueInterestOverdebt": 0,
    "fineDebt": 0,
    "fineInterest": 0,
    "allDebtToPay": 770000,
    "otherSums": [],
    "dateToPay": "2025-04-11T00:00:00",
    "amountToPay": 75826.85,
    "accountBalance": 75826.85
}`

	getLoansAmountErrorStr = `{
    "Code": 10413,
    "QCTXID": "************",
    "Text": "Ошибка сохранения клиента",
    "Description": "Код клиента не передан или не найден",
    "HTTPCode": 500,
    "Module": "loancreate",
    "Hint": {},
    "Level": {
        "Type": "CRITICAL",
        "Value": 0
    },
    "EDate": "2023-12-13T11:04:55"
}`

	respWrapper = &entity.GetLoansAmountsResp{
		MainDebt:                decimal.NewFromFloat(770000),
		OverdueDebt:             decimal.NewFromFloat(0),
		AmountInterest:          decimal.NewFromFloat(705.83),
		AmountInterestOverdebt:  decimal.NewFromFloat(0),
		OverdueInterestOverdebt: decimal.NewFromFloat(0),
		FineDebt:                decimal.NewFromFloat(0),
		FineInterest:            decimal.NewFromFloat(0),
		AllDebtToPay:            decimal.NewFromFloat(770000),
		OtherSums:               []*entity.Sum{},
		DateToPay:               "2025-04-11T00:00:00",
		AmountToPay:             decimal.NewFromFloat(75826.85),
		AccountBalance:          decimal.NewFromFloat(75826.85),
	}

	createExampleErrResponse = entity.ColvirErrResp{
		Code:        10612,
		QCTXID:      "************",
		Text:        "Запись с данным идентификатором request_id = 0ada77f7-98b1-4241-81e7-c1ebf986b2ad уже существует в журнале АБС",
		Description: "Запись с данным идентификатором request_id уже существует в журнале АБС.",
		HTTPCode:    500,
		Module:      "ibclients",
		Hint: entity.Hint{
			Reason: "ORA-00",
		},
		Level: entity.Level{
			Type:  "CRITICAL",
			Value: 0,
		},
		EDate: "2025-02-13T15:45:32",
	}
)

func TestColvirProviderImpl_GetLoansAmountsSuccess(t *testing.T) {
	_, err := time.ParseDateTime("2025-04-11T00:00:00")
	require.NoError(t, err)

	mockServer := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		if r.Header.Get("Content-Type") != "application/json;charset=UTF-8" {
			http.Error(w, "invalid content type", http.StatusUnsupportedMediaType)
			return
		}

		w.WriteHeader(http.StatusOK)
		w.Header().Set("Content-Type", "application/json;charset=UTF-8")
		_, err := w.Write([]byte(GetLoansAmountSuccess))
		require.NoError(t, err)
	}))
	defer mockServer.Close()

	colvir := &ColvirProviderImpl{
		HTTPClient: &http.Client{},
		V2BaseURL:  mockServer.URL,
	}

	t.Run("Get Loans Amounts", func(t *testing.T) {
		got, err := colvir.GetLoansAmounts(context.Background(), &entity.GetLoansAmountsReq{ColvirReferenceID: uuid.NewString()})
		if err != nil {
			t.Errorf("GetLoanSchedule() error = %v", err)
			return
		}

		diff := cmp.Diff(got, respWrapper)
		if diff != "" {
			t.Errorf("GetLoanSchedule() got = %+v, want %+v", got, respWrapper)
			t.Errorf("GetLoanSchedule() diff = %s", diff)
		}
	})
}
