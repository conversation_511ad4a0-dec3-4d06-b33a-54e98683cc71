package colvir

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"net/url"

	"git.redmadrobot.com/zaman/backend/zaman/pkg/colvir/entity"
)

// RequestCreateClientAgreement Метод создания договора на РКО (открытие счета)
// https://rmrkz.atlassian.net/wiki/spaces/ZMNRET/pages/17563893
func (p *ColvirProviderImpl) RequestCreateClientAgreement(
	ctx context.Context, request entity.CreateClientAgreementRequest) (*entity.CreateClientAgreementResp, error) {
	targetURL, err := p.getRequestCreateClientAgreementURL()
	if err != nil {
		return nil, err
	}

	var buf bytes.Buffer
	if err := json.NewEncoder(&buf).Encode(request); err != nil {
		return nil, fmt.Errorf("failed to encode request body for create client agreement request: %w", err)
	}

	req, err := http.NewRequestWithContext(ctx, http.MethodPost, targetURL, &buf)
	if err != nil {
		return nil, fmt.Errorf("failed to create request for create client agreement request: %w", err)
	}
	req.Header.Set("Content-Type", "application/json")

	resp, err := p.HTTPClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to do request for create client agreement request: %w", err)
	}
	defer resp.Body.Close()

	var respBody entity.CreateClientAgreementResp
	if resp.StatusCode != http.StatusOK {
		errResp, err := p.handleColvirRespErrStatusCode(ctx, resp)
		if err != nil {
			return nil, err
		}

		respBody.ColvirError = errResp
		return &respBody, ErrInternalServerError
	}

	if err = json.NewDecoder(resp.Body).Decode(&respBody); err != nil {
		return nil, fmt.Errorf("failed to decode response for create client agreement request: %w", err)
	}

	return &respBody, nil
}

func (p *ColvirProviderImpl) getRequestCreateClientAgreementURL() (string, error) {
	result, err := url.JoinPath(p.V2BaseURL, "/dearko/create")
	if err != nil {
		return "", fmt.Errorf("failed to join path for create client agreement request: %w", err)
	}

	return result, nil
}
