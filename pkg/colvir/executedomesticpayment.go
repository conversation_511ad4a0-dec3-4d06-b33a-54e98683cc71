package colvir

import (
	"bytes"
	"context"
	"errors"
	"fmt"
	"io"
	"net/http"

	"git.redmadrobot.com/backend-go/rmr-pkg/core/errs"
	"git.redmadrobot.com/backend-go/rmr-pkg/core/logs"
	"git.redmadrobot.com/backend-go/rmr-pkg/tools/serial"

	"git.redmadrobot.com/zaman/backend/zaman/pkg/colvir/entity"
)

const (
	colvirExecDomesticPaymentSoapAction = "payment-domestic-executeDomesticPayment"
	colvirExecDomesticPaymentEndpoint   = "/cxf/payments-domestic/v1"
)

func (p *ColvirProviderImpl) RequestExecuteDomesticPayment(
	ctx context.Context, r *entity.ExecuteDomesticPaymentRequest,
) (*entity.ExecuteDomesticPaymentResponse, error) {
	l := logs.FromContext(ctx)

	var buf bytes.Buffer
	if err := serial.XMLEncode(&buf, r); err != nil {
		return nil, errs.Wrapf(err, "failed to encode request body for RequestExecuteDomesticPayment")
	}

	l.Debug().Msgf("RequestExecuteDomesticPayment() request body: %s", buf.String())

	req, err := http.NewRequestWithContext(
		ctx,
		http.MethodPost,
		p.getRequestExecDomesticPaymentURL(),
		bytes.NewBuffer(buf.Bytes()),
	)
	if err != nil {
		return nil, errs.Wrapf(err, "failed to create new http request for RequestExecuteDomesticPayment")
	}

	req.Header.Set("Content-Type", "application/xml;charset=UTF-8")
	req.Header.Set("SOAPAction", colvirExecDomesticPaymentSoapAction)

	resp, err := p.HTTPClient.Do(req)
	if err != nil {
		l.Err(err).Msg("failed to do http request ExecuteDomesticPayment()")

		if errors.Is(err, context.Canceled) || errors.Is(err, context.DeadlineExceeded) {
			err = ErrStatusGatewayTimeout
		}

		return nil, err
	}

	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		bodyBytes, err := io.ReadAll(resp.Body)
		if err != nil {
			bodyBytes = []byte(err.Error())
		}

		l.Debug().
			Msgf("RequestExecuteDomesticPayment() unexpected status, response body: %s", bodyBytes)

		return nil, handleStatusCode(resp.StatusCode, bodyBytes)
	}

	executePaymentResponse, err := serial.XMLDecode[entity.ExecuteDomesticPaymentResponse](resp.Body)
	if err != nil {
		return nil, errs.Wrapf(err, "failed to decode http response body for RequestExecDomesticPayment")
	}

	return &executePaymentResponse, nil
}

func (p *ColvirProviderImpl) getRequestExecDomesticPaymentURL() string {
	return fmt.Sprintf("%s%s", p.BaseURL, colvirExecDomesticPaymentEndpoint)
}
