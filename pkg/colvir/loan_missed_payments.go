package colvir

import (
	"bytes"
	"context"
	"encoding/xml"
	"fmt"
	"io"
	"net/http"

	"git.redmadrobot.com/backend-go/rmr-pkg/core/errs"
	"git.redmadrobot.com/backend-go/rmr-pkg/core/logs"
	"github.com/google/uuid"

	"git.redmadrobot.com/zaman/backend/zaman/pkg/colvir/entity"
)

func (p *ColvirProviderImpl) GetMissedPayments(ctx context.Context, req *entity.GetMissedPaymentsReq) (*entity.GetMissedPaymentsResp, error) {
	l := logs.FromContext(ctx)

	reqSoap := makeReq(req.Code, req.Department)
	l.Debug().Msgf("LoansGetMissedPayments() request body: %s", reqSoap)
	httpReq, err := http.NewRequestWithContext(
		ctx,
		http.MethodPost,
		getRequestGetMissedPaymentsURL(p.BaseURL),
		bytes.NewBufferString(reqSoap),
	)
	if err != nil {
		return nil, errs.Wrapf(err, "failed to create new http request")
	}
	httpReq.Header.Set("Content-Type", "application/xml;charset=UTF-8")

	resp, err := p.HTTPClient.Do(httpReq)
	if err != nil {
		return nil, errs.Wrapf(err, "failed to do http request")
	}
	defer func(Body io.ReadCloser) {
		err := Body.Close()
		if err != nil {
			logs.FromContext(ctx).Error().Msgf("failed to close response body: %v", err)
		}
	}(resp.Body)

	if resp.StatusCode != http.StatusOK {
		errResp, err := p.handleColvirRespErrStatusCode(ctx, resp)
		if err != nil {
			return nil, err
		}

		return &entity.GetMissedPaymentsResp{
			ColvirError: errResp,
		}, ErrInternalServerError
	}

	content, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	var missedPaymentsResp *entity.GetMissedPaymentsResp
	err = xml.NewDecoder(bytes.NewReader(content)).Decode(&missedPaymentsResp)
	if err != nil {
		return nil, err
	}

	return missedPaymentsResp, nil
}

func makeReq(code, department string) string {
	req := `<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/"
                  xmlns:v1="http://bus.colvir.com/service/loans/v1"
                  xmlns:v11="http://bus.colvir.com/common/support/v1"
                  xmlns:v12="http://bus.colvir.com/common/basis/v1">
    <soapenv:HeaderRequest/>
    <soapenv:Body>
        <v1:LoadLoanDelaysListRequest>
            <v11:head>
                <v11:requestId>%s</v11:requestId>
                <v11:sessionId>1</v11:sessionId>
                <v11:processId>1</v11:processId>
                <v11:params>
                    <v11:clientType>OW4</v11:clientType>
                    <v11:interfaceVersion>1.0.0</v11:interfaceVersion>
                </v11:params>
                <v11:startRow>0</v11:startRow>
                <v11:maxRows>1000</v11:maxRows>
            </v11:head>
            <v1:code>%s</v1:code>
            <v1:department>%s</v1:department>
        </v1:LoadLoanDelaysListRequest>
    </soapenv:Body>
</soapenv:Envelope>`
	return fmt.Sprintf(req, uuid.NewString(), code, department)
}

func getRequestGetMissedPaymentsURL(baseURL string) string {
	return fmt.Sprintf("%s%s", baseURL, "/cxf/loans/v1")
}
