package colvir

import (
	"context"
	"fmt"
	"math/rand/v2"
	"net/http"
	"net/http/httptest"
	"os"
	"testing"
	"time"

	"git.redmadrobot.com/backend-go/rmr-pkg/tools/serial"
	"git.redmadrobot.com/zaman/backend/zaman/pkg/colvir/entity"
	"github.com/google/go-cmp/cmp"
	"github.com/google/uuid"
	"github.com/shopspring/decimal"
	"github.com/stretchr/testify/require"
)

func TestColvirImpl_GetLoanSchedule(t *testing.T) {

	resp := entity.GetLoanScheduleResp{
		{
			Amount:       decimal.NewFromFloat(rand.Float64()),
			Date:         time.Now().String(),
			DebtAfterPay: decimal.NewFromFloat(rand.Float64()),
			Details: []entity.GetLoanScheduleRespElementDetailsElement{
				{
					Code:   uuid.NewString(),
					Amount: decimal.NewFromFloat(rand.Float64()),
					Status: uuid.NewString(),
				},
			},
		},
		{
			Amount:       decimal.NewFromFloat(rand.Float64()),
			Date:         time.Now().String(),
			DebtAfterPay: decimal.NewFromFloat(rand.Float64()),
			Details: []entity.GetLoanScheduleRespElementDetailsElement{
				{
					Code:   uuid.NewString(),
					Amount: decimal.NewFromFloat(rand.Float64()),
					Status: uuid.NewString(),
				},
			},
		},
	}

	respWrapper := entity.GetLoanScheduleRespWrapper{
		Schedule: resp,
	}

	mockServer := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		if r.Header.Get("Content-Type") != "application/json;charset=UTF-8" {
			http.Error(w, "invalid content type", http.StatusUnsupportedMediaType)
			return
		}

		w.WriteHeader(http.StatusOK)
		w.Header().Set("Content-Type", "application/json;charset=UTF-8")
		err := serial.JSONEncode(w, resp)
		require.NoError(t, err)
	}))
	defer mockServer.Close()

	colvir := &ColvirProviderImpl{
		HTTPClient: &http.Client{},
		V2BaseURL:  mockServer.URL,
	}

	t.Run("GetLoanSchedule", func(t *testing.T) {
		got, err := colvir.GetLoanSchedule(context.Background(), &entity.GetLoanScheduleReq{ColvirReferenceID: uuid.NewString()})
		if err != nil {
			t.Errorf("GetLoanSchedule() error = %v", err)
			return
		}

		diff := cmp.Diff(got, &respWrapper)
		if diff != "" {
			t.Errorf("GetLoanSchedule() got = %+v, want %+v", got, &respWrapper)
			t.Errorf("GetLoanSchedule() diff = %s", diff)
		}
	})
}

func TestColvirImpl_GetLoanSchedule_Real(t *testing.T) {
	// comment when you want to run this test, don't forget to uncomment the line after testing
	// t.Skip("skip test")

	baseURL := os.Getenv("TEST_COLVIR_V2_BASE_URL")
	if baseURL == "" {
		t.Skip("TEST_COLVIR_V2_BASE_URL is empty")
	}

	colvir := &ColvirProviderImpl{
		HTTPClient: &http.Client{},
		V2BaseURL:  baseURL,
	}

	// Fill this request with your data
	req := &entity.GetLoanScheduleReq{
		ColvirReferenceID: "968_662949",
	}

	if err := req.Validate(); err != nil {
		t.Errorf("GetLoanSchedule() error = %v", err)
		return
	}

	t.Run("GetLoanSchedule", func(t *testing.T) {
		got, err := colvir.GetLoanSchedule(context.Background(), req)
		if err != nil {
			t.Errorf("GetLoanSchedule() error = %v", err)
			return
		}
		fmt.Printf("%+v\n", got)
	})
}
