package colvir

import (
	"bytes"
	"context"
	"errors"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strconv"

	"git.redmadrobot.com/backend-go/rmr-pkg/core/errs"
	"git.redmadrobot.com/backend-go/rmr-pkg/tools/serial"
	"git.redmadrobot.com/zaman/backend/zaman/pkg/colvir/entity"
)

const (
	colvirLoansEndpoint = "/cxf/loans/v1"
)

func (p *ColvirProviderImpl) LoadLoanAgreementDetails(ctx context.Context, request *entity.LoadLoanAgreementDetailsReq) (*entity.LoadLoanAgreementDetailsResp, error) {
	var buf bytes.Buffer
	if err := serial.XMLEncode(&buf, request); err != nil {
		return nil, errs.Wrapf(err, "failed to encode request body")
	}

	endpoint, err := p.getRequestLoadLoanAgreementDetailsURL()
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequestWithContext(
		ctx,
		http.MethodPost,
		endpoint,
		bytes.NewBuffer(buf.Bytes()),
	)
	if err != nil {
		return nil, errs.Wrapf(err, "failed to create new http request")
	}

	req.Header.Set("Content-Type", "application/xml;charset=UTF-8")

	resp, err := p.HTTPClient.Do(req)
	if err != nil {
		// Создаем объект ответа с информацией об ошибке
		var respBody entity.LoadLoanAgreementDetailsResp
		statusCode := "502"
		if resp != nil {
			statusCode = strconv.Itoa(resp.StatusCode)
		}
		respBody.Body.LoadLoanAgreementDetailsResponse.Errors = &entity.ColvirErrors{
			Code:     statusCode,
			Message:  fmt.Sprintf("failed to do request for LoadLoanAgreementDetails request: %s", err.Error()),
			Severity: "critical",
		}

		// Возвращаем объект ответа с пользовательской ошибкой
		return &respBody, ErrColvirServerNotResponding
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		bodyBytes, err := io.ReadAll(resp.Body)
		if err != nil {
			bodyBytes = []byte(err.Error())
		}
		return nil, handleStatusCode(resp.StatusCode, bodyBytes)
	}

	domainValuesResponse, err := serial.XMLDecode[entity.LoadLoanAgreementDetailsResp](resp.Body)
	if err != nil {
		decodeErr := fmt.Errorf("failed to decode response for LoadLoanAgreementDetails request: %w", err)
		bodyBytes, err := io.ReadAll(resp.Body)
		if err != nil {
			bodyBytes = []byte(err.Error())
		}
		// Если статус 200, но не смогли распарсить, то создаем ошибку
		// Данная ошибка нужна для тех поддержки
		parseErr := handleStatusCode(resp.StatusCode, bodyBytes)
		return nil, errors.Join(decodeErr, parseErr)
	}

	if domainValuesResponse.Body.LoadLoanAgreementDetailsResponse.Errors != nil {
		return nil, domainValuesResponse.Body.LoadLoanAgreementDetailsResponse.Errors
	}

	return &domainValuesResponse, nil
}

func (p *ColvirProviderImpl) getRequestLoadLoanAgreementDetailsURL() (string, error) {
	result, err := url.JoinPath(p.BaseURL, colvirLoansEndpoint)
	if err != nil {
		return "", fmt.Errorf("failed to join path for load agreement details request: %w", err)
	}

	return result, nil
}
