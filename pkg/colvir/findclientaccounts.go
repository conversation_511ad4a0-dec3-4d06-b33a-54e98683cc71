package colvir

import (
	"bytes"
	"context"
	"fmt"
	"io"
	"net/http"

	"git.redmadrobot.com/backend-go/rmr-pkg/core/errs"
	"git.redmadrobot.com/backend-go/rmr-pkg/tools/serial"

	"git.redmadrobot.com/zaman/backend/zaman/pkg/colvir/entity"
)

const (
	colvirFindClientAccountsListEndpoint = "/cxf/clients/v1"
)

func (p *ColvirProviderImpl) RequestFindClientAccountsList(ctx context.Context, req entity.RequestFindClientAccountsListReq) (*entity.FindClientAccountsListResponse, error) {
	reqBody := entity.NewFindClientAccountsListByClientCodeAndAccountNumberRequest(req.ClientCodes, req.AccountNumbers)

	var buf bytes.Buffer
	if err := serial.XMLEncode(&buf, reqBody); err != nil {
		return nil, errs.Wrapf(err, "failed to encode request body for RequestFindClientAccountsList")
	}

	request, err := http.NewRequestWithContext(
		ctx,
		http.MethodPost,
		p.getRequestFindClientAccountsListURL(),
		bytes.NewBuffer(buf.Bytes()),
	)
	if err != nil {
		return nil, errs.Wrapf(err, "failed to create new http request for RequestFindClientAccountsList")
	}

	request.Header.Set("Content-Type", "application/xml;charset=UTF-8")

	resp, err := p.HTTPClient.Do(request)
	if err != nil {
		return nil, errs.Wrapf(err, "failed to do http request for RequestFindClientAccountsList")
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		bodyBytes, err := io.ReadAll(resp.Body)
		if err != nil {
			bodyBytes = []byte(err.Error())
		}
		return nil, handleStatusCode(resp.StatusCode, bodyBytes)
	}

	accountsResponse, err := serial.XMLDecode[entity.FindClientAccountsListResponse](resp.Body)
	if err != nil {
		return nil, errs.Wrapf(err, "failed to decode http response body for RequestFindClientAccountsList")
	}

	return &accountsResponse, nil
}

func (p *ColvirProviderImpl) getRequestFindClientAccountsListURL() string {
	return fmt.Sprintf("%s%s", p.BaseURL, colvirFindClientAccountsListEndpoint)
}
