package colvir

import (
	"context"
	"fmt"
	"net/http"
	"net/http/httptest"
	"os"
	"testing"

	"git.redmadrobot.com/backend-go/rmr-pkg/tools/conversion"
	"git.redmadrobot.com/backend-go/rmr-pkg/tools/serial"
	"git.redmadrobot.com/zaman/backend/zaman/pkg/colvir/entity"
	"github.com/go-faker/faker/v4"
	"github.com/google/go-cmp/cmp"
	"github.com/google/go-cmp/cmp/cmpopts"
	"github.com/stretchr/testify/require"
)

func TestColvirImpl_GetCreditContractDetails(t *testing.T) {

	respEnvelope := entity.GetCreditContractDetailsRespEnvelope{}
	err := faker.FakeData(&respEnvelope)
	require.NoError(t, err)
	respEnvelope.Body.LoadLoanDetailsResponse.ColvirError = nil
	resp := respEnvelope.Body.LoadLoanDetailsResponse

	mockServer := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		if r.Header.Get("Content-Type") != "application/xml;charset=UTF-8" {
			http.Error(w, "invalid content type", http.StatusUnsupportedMediaType)
			return
		}

		w.WriteHeader(http.StatusOK)
		w.Header().Set("Content-Type", "application/xml;charset=UTF-8")
		err := serial.XMLEncode(w, respEnvelope)
		require.NoError(t, err)
	}))
	defer mockServer.Close()

	colvir := &ColvirProviderImpl{
		HTTPClient: &http.Client{},
		BaseURL:    mockServer.URL,
	}

	t.Run("GetCreditContractDetails", func(t *testing.T) {
		req := entity.GetCreditContractDetailsReqBodyLoadLoanDetailsRequest{}
		err := faker.FakeData(&req)
		require.NoError(t, err)

		require.NoError(t, err)
		got, err := colvir.GetCreditContractDetails(context.Background(), &req)
		if err != nil {
			t.Errorf("GetCreditContractDetails() error = %v", err)
			return
		}
		diff := cmp.Diff(got, resp, cmpopts.EquateEmpty())
		if diff != "" {
			t.Errorf("GetCreditContractDetails() got = %+v, want %+v", got, resp)
			t.Errorf("GetCreditContractDetails() diff = %s", diff)
		}
	})

	t.Run("ServerNotResponding", func(t *testing.T) {
		// Создаем клиент с несуществующим URL сервера
		nonRespondingColvir := &ColvirProviderImpl{
			HTTPClient: &http.Client{},
			BaseURL:    "http://non-existent-server",
		}

		// Создаем тестовый запрос
		req := entity.GetCreditContractDetailsReqBodyLoadLoanDetailsRequest{}
		err := faker.FakeData(&req)
		require.NoError(t, err)

		// Вызываем метод
		resp, err := nonRespondingColvir.GetCreditContractDetails(context.Background(), &req)

		// Проверяем, что ошибка соответствует ожидаемой
		require.Equal(t, ErrColvirServerNotResponding, err)

		// Проверяем, что ответ содержит ожидаемую информацию об ошибке
		require.NotNil(t, resp)
		require.NotNil(t, resp.ColvirError)
		require.Equal(t, "502", resp.ColvirError.Code)
		require.Contains(t, resp.ColvirError.Message,
			"failed to do request for GetCreditContractDetails request")
		require.Equal(t, "critical", resp.ColvirError.Severity)
	})
}

func TestColvirImpl_GetCreditContractDetails_Real(t *testing.T) {
	// comment when you want to run this test, don't forget to uncomment the line after testing
	// t.Skip("skip test")

	baseURL := os.Getenv("TEST_COLVIR_BASE_URL")
	if baseURL == "" {
		t.Skip("TEST_COLVIR_BASE_URL is empty")
	}

	colvir := &ColvirProviderImpl{
		HTTPClient: &http.Client{},
		BaseURL:    baseURL,
	}

	// Fill this request with your data
	req := &entity.GetCreditContractDetailsReqBodyLoadLoanDetailsRequest{
		ColvirReferenceID: "692_1715006",
		Detail: &entity.GetCreditContractDetailsReqBodyLoadLoanDetailsRequestDetail{
			Text:                   "",
			AnalyticalAccounts:     1,
			AnalyticalAccMovements: 1,
			BalanceAccounts:        1,
			SumTypes:               1,
			PaymentOptions:         1,
			ConsolidationGroups:    1,
			DeaParams:              1,
			CoBorrowers:            1,
			Classifiers:            conversion.Ptr(int32(1)),
		},
	}

	if err := req.Validate(); err != nil {
		t.Errorf("GetCreditContractDetails() error = %v", err)
		return
	}

	t.Run("GetCreditContractDetails", func(t *testing.T) {
		got, err := colvir.GetCreditContractDetails(context.Background(), req)
		if err != nil {
			t.Errorf("GetCreditContractDetails() error = %v", err)
			return
		}
		fmt.Printf("%+v\n", got)
	})
}
