package colvir

import (
	"context"
	"encoding/xml"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/stretchr/testify/assert"

	"git.redmadrobot.com/zaman/backend/zaman/pkg/colvir/entity"
)

var (
	mpTestSuccessResp = `<soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
    <soap:Body>
        <ln:LoadLoanDelaysListResponse xmlns:a="http://bus.colvir.com/common/about/v1"
                                       xmlns:q="http://bus.colvir.com/common/query/v1"
                                       xmlns:b="http://bus.colvir.com/common/basis/v1"
                                       xmlns:ln="http://bus.colvir.com/service/loans/v1"
                                       xmlns:s="http://bus.colvir.com/common/support/v1"
                                       xmlns:f="http://bus.colvir.com/common/forms/v1"
                                       xmlns:cl="http://bus.colvir.com/service/clients/v1"
                                       xmlns:dm="http://bus.colvir.com/common/domain/v1">
            <s:code>0</s:code>
            <s:responseTime>11</s:responseTime>
            <s:responseDbTime>7</s:responseDbTime>
            <s:requestId>5747020d-648b-4e3f-be21-d078a3627512</s:requestId>
            <s:route>cbs@93.177.106.6:6502</s:route>
            <ln:delays>
                <ln:delay>
                    <ln:external>0</ln:external>
                    <ln:agreement>ТМ-00-2025-00002</ln:agreement>
                    <ln:department>000</ln:department>
                    <ln:bankCode>000</ln:bankCode>
                    <ln:fromDate>2025-04-18</ln:fromDate>
                    <ln:hasDelay>0</ln:hasDelay>
                    <ln:delayDaysCount>0</ln:delayDaysCount>
                </ln:delay>
                <ln:delay>
                    <ln:external>0</ln:external>
                    <ln:agreement>ТМ-00-2025-00014</ln:agreement>
                    <ln:department>000</ln:department>
                    <ln:bankCode>000</ln:bankCode>
                    <ln:fromDate>2025-05-02</ln:fromDate>
                    <ln:hasDelay>0</ln:hasDelay>
                    <ln:delayDaysCount>0</ln:delayDaysCount>
                </ln:delay>
                <ln:delay>
                    <ln:external>0</ln:external>
                    <ln:agreement>ТМ-00-2025-00013</ln:agreement>
                    <ln:department>000</ln:department>
                    <ln:bankCode>000</ln:bankCode>
                    <ln:fromDate>2025-05-02</ln:fromDate>
                    <ln:hasDelay>0</ln:hasDelay>
                    <ln:delayDaysCount>0</ln:delayDaysCount>
                </ln:delay>
                <ln:delay>
                    <ln:external>0</ln:external>
                    <ln:agreement>ТМ-00-2025-00011</ln:agreement>
                    <ln:department>000</ln:department>
                    <ln:bankCode>000</ln:bankCode>
                    <ln:fromDate>2025-04-29</ln:fromDate>
                    <ln:hasDelay>0</ln:hasDelay>
                    <ln:delayDaysCount>0</ln:delayDaysCount>
                </ln:delay>
                <ln:delay>
                    <ln:external>0</ln:external>
                    <ln:agreement>ZAMAN-2025-01-08-23</ln:agreement>
                    <ln:department>000</ln:department>
                    <ln:bankCode>000</ln:bankCode>
                    <ln:fromDate>2025-01-08</ln:fromDate>
                    <ln:hasDelay>0</ln:hasDelay>
                    <ln:delayDaysCount>0</ln:delayDaysCount>
                </ln:delay>
            </ln:delays>
        </ln:LoadLoanDelaysListResponse>
    </soap:Body>
</soap:Envelope>`

	mpTestErrorsResp = `<soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
  <soap:Body>
    <ln:LoadLoanDelaysListResponse xmlns:a="http://bus.colvir.com/common/about/v1" xmlns:q="http://bus.colvir.com/common/query/v1" xmlns:b="http://bus.colvir.com/common/basis/v1" xmlns:ln="http://bus.colvir.com/service/loans/v1" xmlns:s="http://bus.colvir.com/common/support/v1" xmlns:f="http://bus.colvir.com/common/forms/v1" xmlns:cl="http://bus.colvir.com/service/clients/v1" xmlns:dm="http://bus.colvir.com/common/domain/v1">
      <s:code>1</s:code>
      <s:responseTime>312</s:responseTime>
      <s:responseDbTime>296</s:responseDbTime>
      <s:requestId>51a2027e-59e7-497b-821d-197357d3a274</s:requestId>
      <s:route>cbs@93.177.106.6:6502</s:route>
      <s:errors>
        <s:code>GF-00000</s:code>
        <s:message>Unexpected error: java.sql.SQLException - ORA-20000: ERR-00000: System message for module id=25(LN) and error id=37 could not be found in system messages dictionary</s:message>
        <s:severity>error</s:severity>
      </s:errors>
    </ln:LoadLoanDelaysListResponse>
  </soap:Body>
</soap:Envelope>`
)

func TestColvirProviderImpl_GetMissedPayments(t *testing.T) {
	type fields struct {
		HTTPClient *http.Client
		BaseURL    string
		V2BaseURL  string
	}
	type args struct {
		ctx context.Context
		req *entity.GetMissedPaymentsReq
	}
	mockServer := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		if r.Header.Get("Content-Type") != "application/xml;charset=UTF-8" {
			http.Error(w, "invalid content type", http.StatusUnsupportedMediaType)
			return
		}
		w.WriteHeader(http.StatusOK)
		w.Header().Set("Content-Type", "text/xml;charset=UTF-8")
		_, _ = w.Write([]byte(mpTestSuccessResp))

	}))
	defer mockServer.Close()

	tests := []struct {
		name    string
		fields  fields
		args    args
		want    string
		wantErr assert.ErrorAssertionFunc
	}{
		{
			name: "Success request",
			fields: fields{
				HTTPClient: http.DefaultClient,
				BaseURL:    mockServer.URL,
				V2BaseURL:  mockServer.URL,
			},
			args: args{
				ctx: context.Background(),
				req: &entity.GetMissedPaymentsReq{
					Code:       "00002351",
					Department: "694",
				},
			},
			want:    `<Envelope xmlns="http://schemas.xmlsoap.org/soap/envelope/"><Body><LoadLoanDelaysListResponse xmlns="http://bus.colvir.com/service/loans/v1"><code xmlns="http://bus.colvir.com/common/support/v1">0</code><responseTime xmlns="http://bus.colvir.com/common/support/v1">11</responseTime><responseDbTime xmlns="http://bus.colvir.com/common/support/v1">7</responseDbTime><requestId xmlns="http://bus.colvir.com/common/support/v1">5747020d-648b-4e3f-be21-d078a3627512</requestId><route xmlns="http://bus.colvir.com/common/support/v1">cbs@93.177.106.6:6502</route><delays xmlns="http://bus.colvir.com/service/loans/v1"><delay xmlns="http://bus.colvir.com/service/loans/v1"><external xmlns="http://bus.colvir.com/service/loans/v1">0</external><agreement xmlns="http://bus.colvir.com/service/loans/v1">ТМ-00-2025-00002</agreement><department xmlns="http://bus.colvir.com/service/loans/v1">000</department><bankCode xmlns="http://bus.colvir.com/service/loans/v1">000</bankCode><fromDate xmlns="http://bus.colvir.com/service/loans/v1">2025-04-18</fromDate><hasDelay xmlns="http://bus.colvir.com/service/loans/v1">0</hasDelay><delayDaysCount xmlns="http://bus.colvir.com/service/loans/v1">0</delayDaysCount></delay><delay xmlns="http://bus.colvir.com/service/loans/v1"><external xmlns="http://bus.colvir.com/service/loans/v1">0</external><agreement xmlns="http://bus.colvir.com/service/loans/v1">ТМ-00-2025-00014</agreement><department xmlns="http://bus.colvir.com/service/loans/v1">000</department><bankCode xmlns="http://bus.colvir.com/service/loans/v1">000</bankCode><fromDate xmlns="http://bus.colvir.com/service/loans/v1">2025-05-02</fromDate><hasDelay xmlns="http://bus.colvir.com/service/loans/v1">0</hasDelay><delayDaysCount xmlns="http://bus.colvir.com/service/loans/v1">0</delayDaysCount></delay><delay xmlns="http://bus.colvir.com/service/loans/v1"><external xmlns="http://bus.colvir.com/service/loans/v1">0</external><agreement xmlns="http://bus.colvir.com/service/loans/v1">ТМ-00-2025-00013</agreement><department xmlns="http://bus.colvir.com/service/loans/v1">000</department><bankCode xmlns="http://bus.colvir.com/service/loans/v1">000</bankCode><fromDate xmlns="http://bus.colvir.com/service/loans/v1">2025-05-02</fromDate><hasDelay xmlns="http://bus.colvir.com/service/loans/v1">0</hasDelay><delayDaysCount xmlns="http://bus.colvir.com/service/loans/v1">0</delayDaysCount></delay><delay xmlns="http://bus.colvir.com/service/loans/v1"><external xmlns="http://bus.colvir.com/service/loans/v1">0</external><agreement xmlns="http://bus.colvir.com/service/loans/v1">ТМ-00-2025-00011</agreement><department xmlns="http://bus.colvir.com/service/loans/v1">000</department><bankCode xmlns="http://bus.colvir.com/service/loans/v1">000</bankCode><fromDate xmlns="http://bus.colvir.com/service/loans/v1">2025-04-29</fromDate><hasDelay xmlns="http://bus.colvir.com/service/loans/v1">0</hasDelay><delayDaysCount xmlns="http://bus.colvir.com/service/loans/v1">0</delayDaysCount></delay><delay xmlns="http://bus.colvir.com/service/loans/v1"><external xmlns="http://bus.colvir.com/service/loans/v1">0</external><agreement xmlns="http://bus.colvir.com/service/loans/v1">ZAMAN-2025-01-08-23</agreement><department xmlns="http://bus.colvir.com/service/loans/v1">000</department><bankCode xmlns="http://bus.colvir.com/service/loans/v1">000</bankCode><fromDate xmlns="http://bus.colvir.com/service/loans/v1">2025-01-08</fromDate><hasDelay xmlns="http://bus.colvir.com/service/loans/v1">0</hasDelay><delayDaysCount xmlns="http://bus.colvir.com/service/loans/v1">0</delayDaysCount></delay></delays></LoadLoanDelaysListResponse></Body></Envelope>`,
			wantErr: nil,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			p := &ColvirProviderImpl{
				HTTPClient: tt.fields.HTTPClient,
				BaseURL:    tt.fields.BaseURL,
				V2BaseURL:  tt.fields.V2BaseURL,
			}
			got, err := p.GetMissedPayments(tt.args.ctx, tt.args.req)
			assert.NoError(t, err)
			assert.NotEmpty(t, got)

			gotContent, err := xml.Marshal(got)
			assert.NoError(t, err)
			assert.Equal(t, tt.want, string(gotContent))
		})
	}
}

func TestColvirProviderImpl_GetMissedPaymentsColvirErrors(t *testing.T) {
	mockServer := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		if r.Header.Get("Content-Type") != "application/xml;charset=UTF-8" {
			http.Error(w, "invalid content type", http.StatusUnsupportedMediaType)
			return
		}
		w.WriteHeader(http.StatusOK)
		w.Header().Set("Content-Type", "text/xml;charset=UTF-8")
		_, _ = w.Write([]byte(mpTestErrorsResp))

	}))
	defer mockServer.Close()

	t.Run("Request with errors", func(t *testing.T) {
		p := &ColvirProviderImpl{
			HTTPClient: http.DefaultClient,
			BaseURL:    mockServer.URL,
			V2BaseURL:  mockServer.URL,
		}
		got, err := p.GetMissedPayments(context.Background(),
			&entity.GetMissedPaymentsReq{
				Code:       "00002351",
				Department: "694",
			})
		assert.NoError(t, err)
		assert.NotEmpty(t, got)
		assert.NotNil(t, got.Body.LoadLoanDelaysListResponse.Errors)
	})
}
