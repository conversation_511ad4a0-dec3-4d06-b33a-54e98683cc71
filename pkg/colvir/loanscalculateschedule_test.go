package colvir

import (
	"context"
	"git.redmadrobot.com/zaman/backend/zaman/pkg/colvir/entity"
	"github.com/aws/smithy-go/ptr"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/stretchr/testify/require"

	"git.redmadrobot.com/backend-go/rmr-pkg/httpx"
)

const (
	loansCalculateScheduleRequestXml = `<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:v1="http://bus.colvir.com/service/loans/v1" xmlns:v11="http://bus.colvir.com/common/support/v1" xmlns:v12="http://bus.colvir.com/common/basis/v1">
   <soapenv:HeaderRequest/>
   <soapenv:Body>
      <v1:calculationScheduleRequest>
         <v11:head>
            <v11:params>
               <v11:clientType>CBS</v11:clientType>
               <v11:interfaceVersion>1.0</v11:interfaceVersion>
            </v11:params>
         </v11:head>
         <v12:colvirReferenceId>735_1599516</v12:colvirReferenceId>
      </v1:calculationScheduleRequest>
   </soapenv:Body>
</soapenv:Envelope>`

	loansCalculateScheduleResponseV1Xml = `<soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
   <soap:Body>
      <ln:calculationScheduleResponse xmlns:a="http://bus.colvir.com/common/about/v1" xmlns:q="http://bus.colvir.com/common/query/v1" xmlns:b="http://bus.colvir.com/common/basis/v1" xmlns:ln="http://bus.colvir.com/service/loans/v1" xmlns:s="http://bus.colvir.com/common/support/v1" xmlns:f="http://bus.colvir.com/common/forms/v1" xmlns:cl="http://bus.colvir.com/service/clients/v1" xmlns:dm="http://bus.colvir.com/common/domain/v1">
         <s:code>0</s:code>
         <s:responseTime>193</s:responseTime>
         <s:responseDbTime>183</s:responseDbTime>
         <s:requestId>683d9a08-0284-4282-8c2d-1e85f81b6b9f</s:requestId>
         <s:route>cbs@192.168.0.249:8181</s:route>
         <b:colvirReferenceId>735_1599516</b:colvirReferenceId>
         <b:agreementCode>TestProduct</b:agreementCode>
         <b:clientCode>00001052</b:clientCode>
         <b:executeResult>
            <dm:code>0</dm:code>
            <dm:name>success</dm:name>
         </b:executeResult>
      </ln:calculationScheduleResponse>
   </soap:Body>
</soap:Envelope>`
	loansCalculateScheduleResponseV2Xml = `<soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
   <soap:Body>
       <ns2:calculationScheduleResponse xmlns="http://bus.colvir.com/common/domain/v1" xmlns:ns2="http://bus.colvir.com/service/loans/v1" xmlns:ns3="http://bus.colvir.com/common/basis/v1" xmlns:ns4="http://bus.colvir.com/common/support/v1" xmlns:ns5="http://bus.colvir.com/common/forms/v1" xmlns:ns6="http://bus.colvir.com/common/query/v1" xmlns:ns7="http://bus.colvir.com/service/clients/v1">
         <ns4:code>0</ns4:code>
         <ns4:responseTime>20304</ns4:responseTime>
         <ns4:responseDbTime>17499</ns4:responseDbTime>
         <ns4:requestId>02e025c9-f2d8-4358-bc42-1714f751aded</ns4:requestId>
         <ns4:route>cbs@localhost:8181</ns4:route>
         <ns3:colvirReferenceId>1388_3279260</ns3:colvirReferenceId>
         <ns3:agreementCode>CNT/2015/F/L/01174</ns3:agreementCode>
         <ns3:clientCode>1314179</ns3:clientCode>
         <ns3:executeResult>
            <code>0</code>
            <name>success</name>
         </ns3:executeResult>
      </ns2:calculationScheduleResponse>
   </soap:Body>
</soap:Envelope>`

	loansCalculateScheduleResponseErrorXml = `<soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
    <soap:Body>
        <ln:calculationScheduleResponse xmlns:a="http://bus.colvir.com/common/about/v1" xmlns:q="http://bus.colvir.com/common/query/v1" xmlns:b="http://bus.colvir.com/common/basis/v1" xmlns:ln="http://bus.colvir.com/service/loans/v1" xmlns:s="http://bus.colvir.com/common/support/v1" xmlns:f="http://bus.colvir.com/common/forms/v1" xmlns:cl="http://bus.colvir.com/service/clients/v1" xmlns:dm="http://bus.colvir.com/common/domain/v1">
            <s:code>1</s:code>
            <s:responseTime>18</s:responseTime>
            <s:responseDbTime>4</s:responseDbTime>
            <s:requestId>02e025c9-f2d8-4358-bc42-1714f751aded</s:requestId>
            <s:route>cbs@45.8.116.220:6502</s:route>
            <s:errors>
                <s:code>DEA-00001</s:code>
                <s:message>No loan agreement (CS_CRED) could be found for provided value of ColvirReferenceID="1388_3279260".</s:message>
                <s:severity>error</s:severity>
            </s:errors>
        </ln:calculationScheduleResponse>
    </soap:Body>
</soap:Envelope>`
)

func TestColvirImpl_RequestLoansCalculateSchedule(t *testing.T) {
	newMockServer := func(response string, httpStatus int) *httptest.Server {
		return httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			if r.Header.Get("Content-Type") != "application/xml;charset=UTF-8" {
				http.Error(w, "invalid content type", http.StatusUnsupportedMediaType)
				return
			}

			w.WriteHeader(httpStatus)
			w.Header().Set("Content-Type", httpx.ContentTypeXML)
			_, err := w.Write([]byte(response))
			require.NoError(t, err)
		}))
	}

	t.Run("requestLoansCalculateSchedule success", func(t *testing.T) {
		mockServer := newMockServer(loansCalculateScheduleResponseV1Xml, http.StatusOK)
		colvir := &ColvirProviderImpl{
			HTTPClient: &http.Client{},
			BaseURL:    mockServer.URL,
		}

		got, err := colvir.RequestLoansCalculateSchedule(context.Background(), &entity.LoansCalculateScheduleReq{
			Body: entity.LoansCalculateScheduleReqBody{
				CalculationScheduleRequest: entity.CalculationScheduleRequest{
					ColvirReferenceID: ptr.String("735_1599516"),
				},
			},
		})

		require.NoError(t, err)
		require.Equal(t, *got.Body.CalculationScheduleResponse.ColvirReferenceID, "735_1599516")
		require.Equal(t, *got.Body.CalculationScheduleResponse.ExecuteResult, entity.ExecuteResult{
			Code: ptr.String("0"),
			Name: ptr.String("success"),
		})
	})

	t.Run("requestLoansCalculateSchedule success v2", func(t *testing.T) {
		mockServer := newMockServer(loansCalculateScheduleResponseV2Xml, http.StatusOK)
		colvir := &ColvirProviderImpl{
			HTTPClient: &http.Client{},
			BaseURL:    mockServer.URL,
		}

		got, err := colvir.RequestLoansCalculateSchedule(context.Background(), &entity.LoansCalculateScheduleReq{
			Body: entity.LoansCalculateScheduleReqBody{
				CalculationScheduleRequest: entity.CalculationScheduleRequest{
					ColvirReferenceID: ptr.String("1388_3279260"),
				},
			},
		})

		require.NoError(t, err)
		require.Equal(t, *got.Body.CalculationScheduleResponse.ColvirReferenceID, "1388_3279260")
		require.Equal(t, *got.Body.CalculationScheduleResponse.AgreementCode, "CNT/2015/F/L/01174")
		require.Equal(t, *got.Body.CalculationScheduleResponse.ClientCode, "1314179")
		require.Equal(t, got.Body.CalculationScheduleResponse.ExecuteResult, &entity.ExecuteResult{
			Code: ptr.String("0"),
			Name: ptr.String("success"),
		})
	})

	t.Run("requestLoansCalculateSchedule error", func(t *testing.T) {
		mockServer := newMockServer(loansCalculateScheduleResponseErrorXml, http.StatusOK)

		colvir := &ColvirProviderImpl{
			HTTPClient: &http.Client{},
			BaseURL:    mockServer.URL,
		}

		got, err := colvir.RequestLoansCalculateSchedule(context.Background(), &entity.LoansCalculateScheduleReq{
			Body: entity.LoansCalculateScheduleReqBody{
				CalculationScheduleRequest: entity.CalculationScheduleRequest{
					ColvirReferenceID: ptr.String("1388_3279260"),
				},
			},
		})

		require.NoError(t, err)

		require.Equal(t, *got.Body.CalculationScheduleResponse.Code, "1")
		require.Equal(t, got.Body.CalculationScheduleResponse.Errors, &entity.CalculationScheduleResponseErrors{
			Code:     "DEA-00001",
			Message:  `No loan agreement (CS_CRED) could be found for provided value of ColvirReferenceID="1388_3279260".`,
			Severity: ptr.String("error"),
		})
	})

	t.Run("ServerNotResponding", func(t *testing.T) {
		// Создаем клиент с несуществующим URL сервера
		nonRespondingColvir := &ColvirProviderImpl{
			HTTPClient: &http.Client{},
			V2BaseURL:  "http://non-existent-server",
		}

		// Вызываем метод
		resp, err := nonRespondingColvir.RequestLoansCalculateSchedule(context.Background(), &entity.LoansCalculateScheduleReq{
			Body: entity.LoansCalculateScheduleReqBody{
				CalculationScheduleRequest: entity.CalculationScheduleRequest{
					ColvirReferenceID: ptr.String("1388_3279260"),
				},
			},
		})

		// Проверяем, что ошибка соответствует ожидаемой
		require.Equal(t, ErrColvirServerNotResponding, err)

		// Проверяем, что ответ содержит ожидаемую информацию об ошибке
		require.NotNil(t, resp)
		require.NotNil(t, resp.ColvirError)
		require.Equal(t, "failed to do request for RequestLoansCalculateSchedule", resp.ColvirError.Text)
		require.NotEmpty(t, resp.ColvirError.Description)
	})
}
