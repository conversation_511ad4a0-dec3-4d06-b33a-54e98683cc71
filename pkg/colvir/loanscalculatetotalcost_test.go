package colvir

import (
	"context"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/aws/smithy-go/ptr"
	"github.com/stretchr/testify/require"

	"git.redmadrobot.com/backend-go/rmr-pkg/httpx"
	"git.redmadrobot.com/zaman/backend/zaman/pkg/colvir/entity"
)

var (
	loansCalculateTotalCostResponse = `<soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
   <soap:Body>
      <ns3:calculateTotalCostResponse xmlns="http://bus.colvir.com/common/support/v1" xmlns:ns2="http://bus.colvir.com/common/basis/v1" xmlns:ns3="http://bus.colvir.com/service/loans/v1" xmlns:ns4="http://bus.colvir.com/common/domain/v1" xmlns:ns5="http://bus.colvir.com/common/query/v1" xmlns:ns6="http://bus.colvir.com/common/forms/v1" xmlns:ns7="http://bus.colvir.com/service/clients/v1">
         <code>0</code>
         <responseTime>33820</responseTime>
         <responseDbTime>33773</responseDbTime>
         <requestId>1</requestId>
         <route>cbs@1.1.5.165:8181</route>
         <ns2:colvirReferenceId>1388_3871677</ns2:colvirReferenceId>
         <ns2:agreementCode>CNT/2015/F/L/01375</ns2:agreementCode>
         <ns2:clientCode>01277910</ns2:clientCode>
         <ns2:executeResult>
            <ns4:code>0</ns4:code>
            <ns4:name>success</ns4:name>
         </ns2:executeResult>
      </ns3:calculateTotalCostResponse>
   </soap:Body>
</soap:Envelope>`

	loansCalculateTotalCostErrorResponseXml = `<soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
    <soap:Body>
        <ln:calculateTotalCostResponse xmlns:a="http://bus.colvir.com/common/about/v1" xmlns:q="http://bus.colvir.com/common/query/v1" xmlns:b="http://bus.colvir.com/common/basis/v1" xmlns:ln="http://bus.colvir.com/service/loans/v1" xmlns:s="http://bus.colvir.com/common/support/v1" xmlns:f="http://bus.colvir.com/common/forms/v1" xmlns:cl="http://bus.colvir.com/service/clients/v1" xmlns:dm="http://bus.colvir.com/common/domain/v1">
            <s:code>1</s:code>
            <s:responseTime>19</s:responseTime>
            <s:responseDbTime>5</s:responseDbTime>
            <s:requestId>0827ec8e-09ab-47d4-913b-0af2989ad44f</s:requestId>
            <s:route>cbs@45.8.116.220:6502</s:route>
            <s:errors>
                <s:code>DEA-00001</s:code>
                <s:message>No loan agreement (CS_CRED) could be found for provided value of ColvirReferenceID="735_1599516".</s:message>
                <s:severity>error</s:severity>
            </s:errors>
        </ln:calculateTotalCostResponse>
    </soap:Body>
</soap:Envelope>`
)

func TestRequestLoansCalculateTotalCost(t *testing.T) {
	newMockServer := func(response string, httpStatus int) *httptest.Server {
		return httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			if r.Header.Get("Content-Type") != "application/xml;charset=UTF-8" {
				http.Error(w, "invalid content type", http.StatusUnsupportedMediaType)
				return
			}

			w.WriteHeader(httpStatus)
			w.Header().Set("Content-Type", httpx.ContentTypeXML)
			_, err := w.Write([]byte(response))
			require.NoError(t, err)
		}))
	}

	t.Run("requestLoansCalculateSchedule success", func(t *testing.T) {
		mockServer := newMockServer(loansCalculateTotalCostResponse, http.StatusOK)
		colvir := &ColvirProviderImpl{
			HTTPClient: &http.Client{},
			BaseURL:    mockServer.URL,
		}

		got, err := colvir.RequestLoansCalculateTotalCost(context.Background(), &entity.LoansCalculateTotalCostReq{
			Body: entity.LoansCalculateTotalCostBody{
				CalculateTotalCostRequest: entity.CalculateTotalCostRequest{
					ColvirReferenceID: ptr.String("1388_3871677"),
				},
			},
		})

		require.NoError(t, err)
		require.Equal(t, *got.Body.CalculateTotalCostResponse.ColvirReferenceID, "1388_3871677")
		require.Equal(t, *got.Body.CalculateTotalCostResponse.ExecuteResult, entity.ExecuteResult{
			Code: ptr.String("0"),
			Name: ptr.String("success"),
		})
	})

	t.Run("requestLoansCalculateTotalCost error", func(t *testing.T) {
		mockServer := newMockServer(loansCalculateTotalCostErrorResponseXml, http.StatusOK)

		colvir := &ColvirProviderImpl{
			HTTPClient: &http.Client{},
			BaseURL:    mockServer.URL,
		}

		got, err := colvir.RequestLoansCalculateTotalCost(context.Background(), &entity.LoansCalculateTotalCostReq{
			Body: entity.LoansCalculateTotalCostBody{
				CalculateTotalCostRequest: entity.CalculateTotalCostRequest{
					ColvirReferenceID: ptr.String("735_1599516"),
				},
			},
		})

		require.NoError(t, err)

		require.Equal(t, *got.Body.CalculateTotalCostResponse.Code, "1")
		require.Equal(t, got.Body.CalculateTotalCostResponse.Errors, &entity.CalculateTotalCostResponseErrors{
			Code:     "DEA-00001",
			Message:  `No loan agreement (CS_CRED) could be found for provided value of ColvirReferenceID="735_1599516".`,
			Severity: ptr.String("error"),
		})
	})
}
