package keycloak

import "errors"

const (
	invalidTokenDesc = "Invalid refresh token"
	userNotFoundDesc = "User not found"
	tokenExpiredDesc = "Refresh token expired" //nolint: gosec // not a token
)

var (
	ErrInvalidRefreshToken = errors.New("InvalidRefreshToken")
	ErrRefreshTokenExpired = errors.New("RefreshTokenExpired")
	ErrUserNotFound        = errors.New("UserNotFound")
)

type (
	UsersResp []User

	User struct {
		ID                         string         `json:"id"`
		Username                   string         `json:"username"`
		EmailVerified              bool           `json:"emailVerified"`
		Attributes                 UserAttributes `json:"attributes"`
		CreatedTimestamp           int64          `json:"createdTimestamp"`
		Enabled                    bool           `json:"enabled"`
		Totp                       bool           `json:"totp"`
		DisableableCredentialTypes []interface{}  `json:"disableableCredentialTypes"`
		RequiredActions            []interface{}  `json:"requiredActions"`
		NotBefore                  int            `json:"notBefore"`
		Access                     Access         `json:"access"`
	}

	Access struct {
		ManageGroupMembership bool `json:"manageGroupMembership"`
		View                  bool `json:"view"`
		MapRoles              bool `json:"mapRoles"`
		Impersonate           bool `json:"impersonate"`
		Manage                bool `json:"manage"`
	}

	UserAttributes map[string]interface{}

	RealmRole struct {
		ID   string `json:"id"`
		Name string `json:"name"`
	}

	ParamsUserCreate struct {
		ID         string              `json:"id"`
		Username   string              `json:"username"`
		Enabled    bool                `json:"enabled"`
		Attributes map[string][]string `json:"attributes"`
	}

	TokensResponse struct {
		AccessToken  string
		RefreshToken string
		SessionID    string
	}

	ErrResponse struct {
		Error            string `json:"error"`
		ErrorDescription string `json:"error_description"`
	}
)
