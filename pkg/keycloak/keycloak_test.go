package keycloak

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/http/httptest"
	"strings"
	"sync"
	"testing"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

const (
	expectedToken = "mocked-access-token"
	clientID      = "test-client-id"
	clientSecret  = "test-client-secret"
	adminUsername = "admin"
	adminPassword = "password"
	realm         = "test-realm"
	testUserPhone = "+77770001122"
)

type mockServerCase struct {
	StatusCode  int
	Resp        map[string]any
	URI         string
	Method      string
	ContentType string
	ReqString   *string
	ReqBody     map[string]any
}

func getTestKeycloakImpl(url string, client *http.Client) *KeycloakProviderImpl {
	return &KeycloakProviderImpl{
		adminTokenReqMutex: &adminTokenReqMutex{
			mu: sync.Mutex{},
		},
		ClientID:      clientID,
		ClientSecret:  clientSecret,
		AdminUsername: adminUsername,
		AdminPassword: adminPassword,
		BaseURL:       url,
		HTTPClient:    client,
		AdminToken:    make(map[string]string),
		RealmRoles:    NewRealmRolesManager(),
	}
}

func newMockKeycloakSrv(t *testing.T, m mockServerCase) *httptest.Server {
	mockServer := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		expectedURL := fmt.Sprintf("/realms/%s/%s", realm, m.URI)
		if r.URL.Path != expectedURL {
			http.Error(w, "not found", http.StatusNotFound)
			return
		}

		if r.Method != m.Method {
			http.Error(w, "invalid method", http.StatusMethodNotAllowed)
			return
		}

		if r.Header.Get("Content-Type") != m.ContentType {
			http.Error(w, "invalid content type", http.StatusUnsupportedMediaType)
			return
		}

		bodyBytes, err := io.ReadAll(r.Body)
		require.NoError(t, err)

		if m.ReqString != nil {
			assert.Equal(t, *m.ReqString, string(bodyBytes))
		}

		if m.ReqBody != nil {
			actualReqBody := map[string]any{}
			err = json.Unmarshal(bodyBytes, &actualReqBody)
			require.NoError(t, err)
			assert.Equal(t, m.ReqBody, actualReqBody)
		}

		w.Header().Set("Content-Type", "application/json")
		err = json.NewEncoder(w).Encode(m.Resp)
		require.NoError(t, err)
	}))

	return mockServer
}

func TestKeycloakProviderImpl_GetAdminTokenSuccess(t *testing.T) {
	reqString := fmt.Sprintf(
		"client_id=%s&client_secret=%s&grant_type=password&password=%s&username=%s",
		clientID, clientSecret, adminPassword, adminUsername,
	)
	m := mockServerCase{
		StatusCode: http.StatusOK,
		Resp: map[string]any{
			"access_token": expectedToken,
		},
		URI:         "protocol/openid-connect/token",
		Method:      http.MethodPost,
		ContentType: "application/x-www-form-urlencoded",
		ReqString:   &reqString,
		ReqBody:     nil,
	}
	mockServer := newMockKeycloakSrv(t, m)
	defer mockServer.Close()

	testKeycloakImpl := getTestKeycloakImpl(mockServer.URL, mockServer.Client())
	ctx := context.Background()

	token, err := testKeycloakImpl.GetAdminToken(ctx, realm)
	require.NoError(t, err)
	assert.Equal(t, expectedToken, token)
	assert.Equal(t, expectedToken, testKeycloakImpl.AdminToken[realm])
}

func TestKeycloakProviderImpl_GetAdminTokenBadRequest(t *testing.T) {
	reqString := fmt.Sprintf(
		"client_id=%s&client_secret=%s&grant_type=password&password=%s&username=%s",
		clientID, clientSecret, adminPassword, adminUsername,
	)
	m := mockServerCase{
		StatusCode: http.StatusBadRequest,
		Resp: map[string]any{
			"error":             "some error",
			"error_description": "some error desc",
		},
		URI:         "protocol/openid-connect/token",
		Method:      http.MethodPost,
		ContentType: "application/x-www-form-urlencoded",
		ReqString:   &reqString,
		ReqBody:     nil,
	}
	mockServer := newMockKeycloakSrv(t, m)
	defer mockServer.Close()

	testKeycloakImpl := getTestKeycloakImpl(mockServer.URL, mockServer.Client())
	ctx := context.Background()

	token, err := testKeycloakImpl.GetAdminToken(ctx, realm)
	assert.NotNil(t, err)
	assert.Empty(t, token)
	assert.Equal(t, "", testKeycloakImpl.AdminToken[realm])
}

func TestKeycloakProviderImpl_GetUserTokensSuccess(t *testing.T) {
	mockServer := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		bodyBytes, err := io.ReadAll(r.Body)
		require.NoError(t, err)

		// обработка кейса запроса токена админа
		if strings.Contains(string(bodyBytes), "password") {
			w.Header().Set("Content-Type", "application/json")
			err = json.NewEncoder(w).Encode(map[string]any{
				"access_token": expectedToken,
			})
			require.NoError(t, err)
			w.WriteHeader(http.StatusOK)
			return
		}

		expectedURL := fmt.Sprintf("/realms/%s/protocol/openid-connect/token", realm)
		if r.URL.Path != expectedURL {
			http.Error(w, "not found", http.StatusNotFound)
			return
		}

		if r.Method != http.MethodPost {
			http.Error(w, "invalid method", http.StatusMethodNotAllowed)
			return
		}

		if r.Header.Get("Content-Type") != "application/x-www-form-urlencoded" {
			http.Error(w, "invalid content type", http.StatusUnsupportedMediaType)
			return
		}

		w.Header().Set("Content-Type", "application/json")
		err = json.NewEncoder(w).Encode(map[string]any{
			"access_token":  expectedToken,
			"refresh_token": expectedToken,
			"session_state": uuid.New(),
		})
		require.NoError(t, err)
		w.WriteHeader(http.StatusOK)
	}))
	defer mockServer.Close()

	testKeycloakImpl := getTestKeycloakImpl(mockServer.URL, mockServer.Client())
	ctx := context.Background()

	resp, err := testKeycloakImpl.GetUserTokens(ctx, realm, testUserPhone)
	require.NoError(t, err)
	assert.Equal(t, expectedToken, resp.AccessToken)
	assert.Equal(t, expectedToken, resp.RefreshToken)
	assert.True(t, resp.SessionID != "")
	assert.Equal(t, expectedToken, testKeycloakImpl.AdminToken[realm])
}

func TestKeycloakProviderImpl_GetUserTokensSuccessBadReq(t *testing.T) {
	mockServer := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		bodyBytes, err := io.ReadAll(r.Body)
		require.NoError(t, err)

		// обработка кейса запроса токена админа
		if strings.Contains(string(bodyBytes), "password") {
			w.Header().Set("Content-Type", "application/json")
			err = json.NewEncoder(w).Encode(map[string]any{
				"access_token": expectedToken,
			})
			require.NoError(t, err)
			w.WriteHeader(http.StatusOK)
			return
		}

		w.Header().Set("Content-Type", "application/json")
		err = json.NewEncoder(w).Encode(map[string]any{
			"error":             "some bad req error",
			"error_description": "some bad req error desc",
		})
		require.NoError(t, err)
		w.WriteHeader(http.StatusBadRequest)
	}))
	defer mockServer.Close()

	testKeycloakImpl := getTestKeycloakImpl(mockServer.URL, mockServer.Client())
	ctx := context.Background()

	resp, err := testKeycloakImpl.GetUserTokens(ctx, realm, testUserPhone)
	assert.NotNil(t, err)
	assert.Empty(t, resp)
}

func TestKeycloakProviderImpl_RefreshTokensSuccess(t *testing.T) {
	reqString := `client_id=test-client-id&client_secret=test-client-secret&grant_type=refresh_token&refresh_token=test-refresh-token`
	mockServer := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		bodyBytes, err := io.ReadAll(r.Body)
		require.NoError(t, err)

		expectedURL := fmt.Sprintf("/realms/%s/protocol/openid-connect/token", realm)
		if r.URL.Path != expectedURL {
			http.Error(w, "not found", http.StatusNotFound)
			return
		}

		if r.Method != http.MethodPost {
			http.Error(w, "invalid method", http.StatusMethodNotAllowed)
			return
		}

		if r.Header.Get("Content-Type") != "application/x-www-form-urlencoded" {
			http.Error(w, "invalid content type", http.StatusUnsupportedMediaType)
			return
		}

		assert.Equal(t, reqString, string(bodyBytes))

		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusOK)
		err = json.NewEncoder(w).Encode(map[string]any{
			"access_token":  expectedToken,
			"refresh_token": expectedToken,
			"session_state": uuid.New(),
		})
		require.NoError(t, err)
	}))
	defer mockServer.Close()

	testKeycloakImpl := getTestKeycloakImpl(mockServer.URL, mockServer.Client())
	ctx := context.Background()

	resp, err := testKeycloakImpl.RefreshUserTokens(ctx, realm, "test-refresh-token")
	require.NoError(t, err)
	assert.Equal(t, expectedToken, resp.AccessToken)
	assert.Equal(t, expectedToken, resp.RefreshToken)
	assert.True(t, resp.SessionID != "")
}

func TestKeycloakProviderImpl_RefreshTokensInvalidTokenErr(t *testing.T) {
	mockServer := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusBadRequest)
		err := json.NewEncoder(w).Encode(map[string]any{
			"error":             "invalid_grant",
			"error_description": "Invalid refresh token",
		})
		require.NoError(t, err)
	}))
	defer mockServer.Close()

	testKeycloakImpl := getTestKeycloakImpl(mockServer.URL, mockServer.Client())
	ctx := context.Background()

	resp, err := testKeycloakImpl.RefreshUserTokens(ctx, realm, "test-refresh-token")
	assert.NotNil(t, err)
	assert.Empty(t, resp)
	assert.Equal(t, err, ErrInvalidRefreshToken)
}

func TestKeycloakProviderImpl_RefreshTokensUserNotFoundErr(t *testing.T) {
	mockServer := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusBadRequest)
		err := json.NewEncoder(w).Encode(map[string]any{
			"error":             "invalid_grant",
			"error_description": "User not found",
		})
		require.NoError(t, err)
	}))
	defer mockServer.Close()

	testKeycloakImpl := getTestKeycloakImpl(mockServer.URL, mockServer.Client())
	ctx := context.Background()

	resp, err := testKeycloakImpl.RefreshUserTokens(ctx, realm, "test-refresh-token")
	assert.NotNil(t, err)
	assert.Empty(t, resp)
	assert.Equal(t, err, ErrUserNotFound)
}

func TestKeycloakProviderImpl_RefreshTokensTokenExpiredErr(t *testing.T) {
	mockServer := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusBadRequest)
		err := json.NewEncoder(w).Encode(map[string]any{
			"error":             "invalid_grant",
			"error_description": "Refresh token expired",
		})
		require.NoError(t, err)
	}))
	defer mockServer.Close()

	testKeycloakImpl := getTestKeycloakImpl(mockServer.URL, mockServer.Client())
	ctx := context.Background()

	resp, err := testKeycloakImpl.RefreshUserTokens(ctx, realm, "test-refresh-token")
	assert.NotNil(t, err)
	assert.Empty(t, resp)
	assert.Equal(t, err, ErrRefreshTokenExpired)
}

func TestKeycloakProviderImpl_RefreshTokensTokenNotActive(t *testing.T) {
	mockServer := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusBadRequest)
		err := json.NewEncoder(w).Encode(map[string]any{
			"error":             "invalid_grant",
			"error_description": "Token is not active",
		})
		require.NoError(t, err)
	}))
	defer mockServer.Close()

	testKeycloakImpl := getTestKeycloakImpl(mockServer.URL, mockServer.Client())
	ctx := context.Background()

	resp, err := testKeycloakImpl.RefreshUserTokens(ctx, realm, "test-refresh-token")
	assert.NotNil(t, err)
	assert.Empty(t, resp)
	assert.Equal(t, err, ErrRefreshTokenExpired)
}

func TestKeycloakProviderImpl_GetRealmRolesSuccess(t *testing.T) {
	expectedRoles := []RealmRole{
		{
			ID:   uuid.New().String(),
			Name: "role_1",
		},
		{
			ID:   uuid.New().String(),
			Name: "role_2",
		},
	}

	expectedPath := fmt.Sprintf("/admin/realms/%s/roles", realm)
	mockServer := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		bodyBytes, err := io.ReadAll(r.Body)
		require.NoError(t, err)

		// Handle admin token request
		if strings.Contains(string(bodyBytes), "password") {
			w.Header().Set("Content-Type", "application/json")
			err = json.NewEncoder(w).Encode(map[string]any{
				"access_token": expectedToken,
			})
			require.NoError(t, err)
			w.WriteHeader(http.StatusOK)
			return
		}

		if r.URL.Path != expectedPath {
			http.Error(w, "not found err", http.StatusNotFound)
			return
		}

		if r.Method != http.MethodGet {
			http.Error(w, "invalid method", http.StatusMethodNotAllowed)
			return
		}

		if r.Header.Get("Authorization") == "" {
			http.Error(w, "Unauthorized req", http.StatusUnauthorized)
			return
		}

		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusOK)
		err = json.NewEncoder(w).Encode(expectedRoles)
		require.NoError(t, err)
	}))
	defer mockServer.Close()

	testKeycloakImpl := getTestKeycloakImpl(mockServer.URL, mockServer.Client())
	ctx := context.Background()

	err := testKeycloakImpl.GetRealmRoles(ctx, realm)
	assert.NoError(t, err)
	assert.NotEmpty(t, testKeycloakImpl.AdminToken[realm])

	// Verify roles were stored correctly
	for _, expectedRole := range expectedRoles {
		role, exists := testKeycloakImpl.RealmRoles.GetRole(realm, expectedRole.Name)
		assert.True(t, exists)
		assert.Equal(t, expectedRole.ID, role.ID)
		assert.Equal(t, expectedRole.Name, role.Name)
	}
}

func TestKeycloakProviderImpl_GetRealmRolesEmptyRolesList(t *testing.T) {
	expectedPath := fmt.Sprintf("/admin/realms/%s/roles", realm)
	mockServer := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		bodyBytes, err := io.ReadAll(r.Body)
		require.NoError(t, err)

		// обработка кейса запроса токена админа
		if strings.Contains(string(bodyBytes), "password") {
			w.Header().Set("Content-Type", "application/json")
			err = json.NewEncoder(w).Encode(map[string]any{
				"access_token": expectedToken,
			})
			require.NoError(t, err)
			w.WriteHeader(http.StatusOK)
			return
		}

		if r.URL.Path != expectedPath {
			http.Error(w, "not found err", http.StatusNotFound)
			return
		}

		if r.Method != http.MethodGet {
			http.Error(w, "invalid method", http.StatusMethodNotAllowed)
			return
		}

		if r.Header.Get("Authorization") == "" {
			http.Error(w, "Unauthorized req", http.StatusUnauthorized)
			return
		}

		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusOK)
		err = json.NewEncoder(w).Encode([]RealmRole{})
		require.NoError(t, err)
	}))
	defer mockServer.Close()

	testKeycloakImpl := getTestKeycloakImpl(mockServer.URL, mockServer.Client())
	ctx := context.Background()

	err := testKeycloakImpl.GetRealmRoles(ctx, realm)
	assert.NoError(t, err)
	assert.NotEmpty(t, testKeycloakImpl.AdminToken[realm])
	assert.Equal(t, testKeycloakImpl.RealmRoles.GetRoles(realm), []RealmRole{})
}

func TestKeycloakProviderImpl_GetRealmRolesInternalErr(t *testing.T) {
	expectedPath := fmt.Sprintf("/admin/realms/%s/roles", realm)
	mockServer := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		bodyBytes, err := io.ReadAll(r.Body)
		require.NoError(t, err)

		// обработка кейса запроса токена админа
		if strings.Contains(string(bodyBytes), "password") {
			w.Header().Set("Content-Type", "application/json")
			err = json.NewEncoder(w).Encode(map[string]any{
				"access_token": expectedToken,
			})
			require.NoError(t, err)
			w.WriteHeader(http.StatusOK)
			return
		}

		if r.URL.Path != expectedPath {
			http.Error(w, "not found err", http.StatusNotFound)
			return
		}

		if r.Method != http.MethodGet {
			http.Error(w, "invalid method", http.StatusMethodNotAllowed)
			return
		}

		if r.Header.Get("Authorization") == "" {
			http.Error(w, "Unauthorized req", http.StatusUnauthorized)
			return
		}

		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusInternalServerError)
		err = json.NewEncoder(w).Encode(map[string]any{
			"error":             "some internal error",
			"error_description": "some internal error desc",
		})
		require.NoError(t, err)
	}))
	defer mockServer.Close()

	testKeycloakImpl := getTestKeycloakImpl(mockServer.URL, mockServer.Client())
	ctx := context.Background()

	err := testKeycloakImpl.GetRealmRoles(ctx, realm)
	assert.NotNil(t, err)
	assert.NotEmpty(t, testKeycloakImpl.AdminToken[realm])
	assert.Equal(t, testKeycloakImpl.RealmRoles.GetRoles(realm), []RealmRole{})
}

func TestKeycloakProviderImpl_CreateUserSuccess(t *testing.T) {
	expectedPath := fmt.Sprintf("/admin/realms/%s/users", realm)
	mockServer := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		bodyBytes, err := io.ReadAll(r.Body)
		require.NoError(t, err)

		// обработка кейса запроса токена админа
		if strings.Contains(string(bodyBytes), "password") {
			w.Header().Set("Content-Type", "application/json")
			err = json.NewEncoder(w).Encode(map[string]any{
				"access_token": expectedToken,
			})
			require.NoError(t, err)
			w.WriteHeader(http.StatusOK)
			return
		}

		if r.URL.Path != expectedPath {
			http.Error(w, "not found err", http.StatusNotFound)
			return
		}

		if r.Method != http.MethodPost {
			http.Error(w, "invalid method", http.StatusMethodNotAllowed)
			return
		}

		if r.Header.Get("Authorization") == "" {
			http.Error(w, "Unauthorized req", http.StatusUnauthorized)
			return
		}

		if r.Header.Get("Content-Type") != "application/json" {
			http.Error(w, "bad req err - invalid content-type header", http.StatusBadRequest)
			return
		}

		reqBody := map[string]any{}
		err = json.Unmarshal(bodyBytes, &reqBody)
		require.NoError(t, err)
		assert.Equal(t, reqBody["username"], testUserPhone)
		assert.True(t, reqBody["enabled"].(bool))

		w.WriteHeader(http.StatusCreated)
		require.NoError(t, err)
	}))
	defer mockServer.Close()

	testKeycloakImpl := getTestKeycloakImpl(mockServer.URL, mockServer.Client())
	ctx := context.Background()

	err := testKeycloakImpl.CreateUser(ctx, realm, testUserPhone)
	assert.NoError(t, err)
}

func TestKeycloakProviderImpl_CreateUserAlreadyExitsCaseHandle(t *testing.T) {
	expectedPath := fmt.Sprintf("/admin/realms/%s/users", realm)
	mockServer := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		bodyBytes, err := io.ReadAll(r.Body)
		require.NoError(t, err)

		// обработка кейса запроса токена админа
		if strings.Contains(string(bodyBytes), "password") {
			w.Header().Set("Content-Type", "application/json")
			err = json.NewEncoder(w).Encode(map[string]any{
				"access_token": expectedToken,
			})
			require.NoError(t, err)
			w.WriteHeader(http.StatusOK)
			return
		}

		if r.URL.Path != expectedPath {
			http.Error(w, "not found err", http.StatusNotFound)
			return
		}

		if r.Method != http.MethodPost {
			http.Error(w, "invalid method", http.StatusMethodNotAllowed)
			return
		}

		if r.Header.Get("Authorization") == "" {
			http.Error(w, "Unauthorized req", http.StatusUnauthorized)
			return
		}

		if r.Header.Get("Content-Type") != "application/json" {
			http.Error(w, "bad req err - invalid content-type header", http.StatusBadRequest)
			return
		}

		reqBody := map[string]any{}
		err = json.Unmarshal(bodyBytes, &reqBody)
		require.NoError(t, err)
		assert.Equal(t, reqBody["username"], testUserPhone)
		assert.True(t, reqBody["enabled"].(bool))

		w.WriteHeader(http.StatusConflict)
		require.NoError(t, err)
	}))
	defer mockServer.Close()

	testKeycloakImpl := getTestKeycloakImpl(mockServer.URL, mockServer.Client())
	ctx := context.Background()

	err := testKeycloakImpl.CreateUser(ctx, realm, testUserPhone)
	assert.NoError(t, err)
}

func TestKeycloakProviderImpl_CreateUserBadReqErr(t *testing.T) {
	expectedPath := fmt.Sprintf("/admin/realms/%s/users", realm)
	mockServer := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		bodyBytes, err := io.ReadAll(r.Body)
		require.NoError(t, err)

		// обработка кейса запроса токена админа
		if strings.Contains(string(bodyBytes), "password") {
			w.Header().Set("Content-Type", "application/json")
			err = json.NewEncoder(w).Encode(map[string]any{
				"access_token": expectedToken,
			})
			require.NoError(t, err)
			w.WriteHeader(http.StatusOK)
			return
		}

		if r.URL.Path != expectedPath {
			http.Error(w, "not found err", http.StatusNotFound)
			return
		}

		if r.Method != http.MethodPost {
			http.Error(w, "invalid method", http.StatusMethodNotAllowed)
			return
		}

		if r.Header.Get("Authorization") == "" {
			http.Error(w, "Unauthorized req", http.StatusUnauthorized)
			return
		}

		if r.Header.Get("Content-Type") != "application/json" {
			http.Error(w, "bad req err - invalid content-type header", http.StatusBadRequest)
			return
		}

		reqBody := map[string]any{}
		err = json.Unmarshal(bodyBytes, &reqBody)
		require.NoError(t, err)
		assert.Equal(t, reqBody["username"], testUserPhone)
		assert.True(t, reqBody["enabled"].(bool))

		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusInternalServerError)
		err = json.NewEncoder(w).Encode(map[string]any{
			"error":             "some internal error",
			"error_description": "some internal error desc",
		})
		require.NoError(t, err)
	}))
	defer mockServer.Close()

	testKeycloakImpl := getTestKeycloakImpl(mockServer.URL, mockServer.Client())
	ctx := context.Background()

	err := testKeycloakImpl.CreateUser(ctx, realm, testUserPhone)
	assert.NotNil(t, err)
}
