package keycloak

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"sync"

	"git.redmadrobot.com/backend-go/rmr-pkg/core/logs"
)

const (
	KeycloakRoleActive        = "active"
	KeycloakRoleNotIdentified = "not_identified"
	KeycloakRoleBlocked       = "blocked"
)

type KeycloakProvider interface {
	GetAdminToken(ctx context.Context, realm string) (string, error)
	GetUserTokens(ctx context.Context, realm string, userPhone string) (TokensResponse, error)
	RefreshUserTokens(ctx context.Context, realm string, refreshToken string) (TokensResponse, error)
	GetRealmRoles(ctx context.Context, realm string) error
	CreateUser(ctx context.Context, realm string, userPhone string) error
	SetUserRoles(ctx context.Context, realm string, userID string, assign bool, roleNames ...string) error
	DeleteSessions(ctx context.Context, realm string, sessionIDs []string) error
	GetUserByPhone(ctx context.Context, realm string, userPhone string) (User, error)
	RevokeRefreshToken(ctx context.Context, realm string, token string) error
}

type (
	KeycloakProviderImpl struct {
		HTTPClient    *http.Client
		BaseURL       string
		Realms        []string
		ClientID      string
		ClientSecret  string
		AdminToken    map[string]string
		AdminUsername string
		AdminPassword string
		RealmRoles    *RealmRolesManager

		adminTokenReqMutex *adminTokenReqMutex
	}

	adminTokenReqMutex struct {
		mu     sync.Mutex
		locked bool
	}
)

func NewKeycloakProvider(cfg *Config, realms ...string) (KeycloakProvider, error) {
	provider := &KeycloakProviderImpl{
		adminTokenReqMutex: &adminTokenReqMutex{
			mu: sync.Mutex{},
		},
		HTTPClient:    &http.Client{},
		Realms:        realms,
		AdminToken:    make(map[string]string),
		BaseURL:       cfg.KcBaseURL,
		ClientID:      cfg.KcClientID,
		ClientSecret:  cfg.KcClientSecret,
		AdminUsername: cfg.KcAppAdminUsername,
		AdminPassword: cfg.KcAppAdminPassword,
		RealmRoles:    NewRealmRolesManager(),
	}
	for _, realm := range realms {
		err := provider.GetRealmRoles(context.Background(), realm)
		if err != nil {
			return nil, fmt.Errorf("failed to get realm roles for %s: %w", realm, err)
		}
	}

	return provider, nil
}

// GetAdminToken - Получает или возвращает активный токен администратора для дальнейшего использования в запросах.
func (kcp *KeycloakProviderImpl) GetAdminToken(ctx context.Context, realm string) (string, error) {
	// проверяем активность токена
	token, ok := kcp.AdminToken[realm]
	if !ok {
		// Блокируем и пытаемся под данный реалм получить админ токен
		kcp.adminTokenReqMutex.Lock()
		defer kcp.adminTokenReqMutex.Unlock()
		// получаем новый токен
		token, err := kcp.requestAdminToken(ctx, realm)
		if err != nil {
			return "", err
		}
		kcp.AdminToken[realm] = token

		return token, nil
	}
	isTokenActive, _ := checkIsTokenActive(token)
	if isTokenActive {
		return token, nil
	}

	// если нет - блокируемся и пытаемся обновить
	kcp.adminTokenReqMutex.Lock()
	defer kcp.adminTokenReqMutex.Unlock()

	// после блокировки проверяем снова активность токена — возможно, другой поток уже обновил токен
	isTokenActive, _ = checkIsTokenActive(token)
	if isTokenActive {
		return token, nil
	}

	// получаем новый токен
	token, err := kcp.requestAdminToken(ctx, realm)
	if err != nil {
		return "", err
	}

	kcp.AdminToken[realm] = token

	return token, nil
}

// GetUserTokens - Получает токены для пользователя по его телефону через обмен токенов (token exchange).
func (kcp *KeycloakProviderImpl) GetUserTokens(ctx context.Context, realm string, userPhone string) (TokensResponse, error) {
	adminToken, err := kcp.GetAdminToken(ctx, realm)
	if err != nil {
		return TokensResponse{}, fmt.Errorf("error getting admin token: %w", err)
	}

	form := url.Values{}
	form.Add("client_id", kcp.ClientID)
	form.Add("client_secret", kcp.ClientSecret)
	form.Add("grant_type", "urn:ietf:params:oauth:grant-type:token-exchange")
	form.Add("subject_token", adminToken)
	form.Add("requested_subject", userPhone)

	req, err := http.NewRequestWithContext(
		ctx,
		http.MethodPost,
		fmt.Sprintf("%s/realms/%s/protocol/openid-connect/token", kcp.BaseURL, realm),
		bytes.NewBufferString(form.Encode()),
	)
	if err != nil {
		return TokensResponse{}, fmt.Errorf("error creating new http request: %w", err)
	}
	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")

	resp, err := kcp.HTTPClient.Do(req)
	if err != nil {
		return TokensResponse{}, fmt.Errorf("error doing http request: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		bodyBytes, _ := io.ReadAll(resp.Body)
		return TokensResponse{}, fmt.Errorf("error status of http response [%d]: %s", resp.StatusCode, bodyBytes)
	}

	var result map[string]interface{}
	if err = json.NewDecoder(resp.Body).Decode(&result); err != nil {
		return TokensResponse{}, fmt.Errorf("error decoding http response body: %w", err)
	}

	accessToken, ok := result["access_token"].(string)
	if !ok {
		return TokensResponse{}, fmt.Errorf("no access_token in response body")
	}
	refreshToken, ok := result["refresh_token"].(string)
	if !ok {
		return TokensResponse{}, fmt.Errorf("no refresh_token in response body")
	}
	sessionState, ok := result["session_state"].(string)
	if !ok {
		return TokensResponse{}, fmt.Errorf("no session_state in response body")
	}
	// TODO: add tokens validity and ttl check, possibly 1 of 100

	return TokensResponse{
		AccessToken:  accessToken,
		RefreshToken: refreshToken,
		SessionID:    sessionState,
	}, nil
}

// RefreshUserTokens - Обновляет токены пользователя по refreshToken.
func (kcp *KeycloakProviderImpl) RefreshUserTokens(ctx context.Context, realm string, refreshToken string) (TokensResponse, error) {
	form := url.Values{}
	form.Add("client_id", kcp.ClientID)
	form.Add("client_secret", kcp.ClientSecret)
	form.Add("grant_type", "refresh_token")
	form.Add("refresh_token", refreshToken)

	req, err := http.NewRequestWithContext(
		ctx,
		http.MethodPost,
		fmt.Sprintf("%s/realms/%s/protocol/openid-connect/token", kcp.BaseURL, realm),
		bytes.NewBufferString(form.Encode()),
	)
	if err != nil {
		return TokensResponse{}, fmt.Errorf("error creating new http request: %w", err)
	}
	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")

	resp, err := kcp.HTTPClient.Do(req)
	if err != nil {
		return TokensResponse{}, fmt.Errorf("error doing http request: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		var errResp ErrResponse
		if err = json.NewDecoder(resp.Body).Decode(&errResp); err != nil {
			return TokensResponse{}, fmt.Errorf("error decoding http err response body: %w", err)
		}
		return TokensResponse{}, parseRefreshTokenErrResp(errResp, resp.StatusCode)
	}

	var result map[string]interface{}
	if err = json.NewDecoder(resp.Body).Decode(&result); err != nil {
		return TokensResponse{}, fmt.Errorf("error decoding http response body: %w", err)
	}

	accessToken, ok := result["access_token"].(string)
	if !ok {
		return TokensResponse{}, fmt.Errorf("no access_token in response body")
	}
	newRefreshToken, ok := result["refresh_token"].(string)
	if !ok {
		return TokensResponse{}, fmt.Errorf("no refresh_token in response body")
	}
	sessionState, ok := result["session_state"].(string)
	if !ok {
		return TokensResponse{}, fmt.Errorf("no session_state in response body")
	}
	// TODO: add tokens validity and ttl check, possibly 1 of 100

	return TokensResponse{
		AccessToken:  accessToken,
		RefreshToken: newRefreshToken,
		SessionID:    sessionState,
	}, nil
}

// GetRealmRoles - Загружает все роли реалма.
func (kcp *KeycloakProviderImpl) GetRealmRoles(ctx context.Context, realm string) error {
	adminToken, err := kcp.GetAdminToken(ctx, realm)
	if err != nil {
		return fmt.Errorf("error getting admin token: %w", err)
	}
	req, err := http.NewRequestWithContext(
		ctx,
		http.MethodGet,
		fmt.Sprintf("%s/admin/realms/%s/roles", kcp.BaseURL, realm),
		nil,
	)
	if err != nil {
		return fmt.Errorf("error creating new http request: %w", err)
	}
	req.Header.Set("Authorization", "Bearer "+adminToken)

	resp, err := kcp.HTTPClient.Do(req)
	if err != nil {
		return fmt.Errorf("error doing http request: %w", err)
	}
	defer resp.Body.Close()
	if resp.StatusCode != http.StatusOK {
		bodyBytes, _ := io.ReadAll(resp.Body)
		return fmt.Errorf("error status of http response [%d]: %s", resp.StatusCode, bodyBytes)
	}
	var result []RealmRole
	if err = json.NewDecoder(resp.Body).Decode(&result); err != nil {
		return fmt.Errorf("error decoding http response body: %w", err)
	}
	kcp.RealmRoles.SetRoles(realm, result)

	return nil
}

// CreateUser - Создает нового пользователя в keycloak.
func (kcp *KeycloakProviderImpl) CreateUser(ctx context.Context, realm string, userPhone string) error {
	adminToken, err := kcp.GetAdminToken(ctx, realm)
	if err != nil {
		return fmt.Errorf("error getting admin token: %w", err)
	}
	user := ParamsUserCreate{
		Username: userPhone,
		Enabled:  true,
		Attributes: map[string][]string{
			"phone": {userPhone},
		},
	}
	jsonData, err := json.Marshal(user)
	if err != nil {
		return fmt.Errorf("error marshalling JSON: %w", err)
	}
	req, err := http.NewRequestWithContext(
		ctx,
		http.MethodPost,
		fmt.Sprintf("%s/admin/realms/%s/users", kcp.BaseURL, realm),
		bytes.NewBuffer(jsonData),
	)
	if err != nil {
		return fmt.Errorf("error creating new http request: %w", err)
	}
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer "+adminToken)

	resp, err := kcp.HTTPClient.Do(req)
	if err != nil {
		return fmt.Errorf("error doing http request: %w", err)
	}
	defer resp.Body.Close()

	// если пользователь уже существует - вернется 409 код - мы его игнорируем
	if resp.StatusCode != http.StatusCreated && resp.StatusCode != http.StatusConflict {
		bodyBytes, _ := io.ReadAll(resp.Body)
		return fmt.Errorf("error status of http response [%d]: %s", resp.StatusCode, bodyBytes)
	}

	return nil
}

// SetUserRoles - Назначает или удаляет роли у пользователя.
func (kcp *KeycloakProviderImpl) SetUserRoles(ctx context.Context, realm string, userID string, assign bool, roleNames ...string) error {
	adminToken, err := kcp.GetAdminToken(ctx, realm)
	if err != nil {
		return fmt.Errorf("error getting admin token: %w", err)
	}
	var roles []RealmRole

	for _, roleName := range roleNames {
		role, exists := kcp.RealmRoles.GetRole(realm, roleName)
		if !exists {
			return fmt.Errorf("realm role %s does not exist in realm %s", roleName, realm)
		}
		roles = append(roles, role)
	}

	jsonData, err := json.Marshal(roles)
	if err != nil {
		return fmt.Errorf("error marshalling JSON: %w", err)
	}
	method := "POST"
	if !assign {
		method = "DELETE"
	}
	req, err := http.NewRequestWithContext(
		ctx,
		method,
		fmt.Sprintf("%s/admin/realms/%s/users/%s/role-mappings/realm", kcp.BaseURL, realm, userID),
		bytes.NewBuffer(jsonData),
	)
	if err != nil {
		return fmt.Errorf("error creating new http request: %w", err)
	}
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer "+adminToken)

	resp, err := kcp.HTTPClient.Do(req)
	if err != nil {
		return fmt.Errorf("error doing http request: %w", err)
	}
	defer resp.Body.Close()
	if resp.StatusCode != http.StatusNoContent {
		bodyBytes, _ := io.ReadAll(resp.Body)
		return fmt.Errorf("error status of http response [%d]: %s", resp.StatusCode, bodyBytes)
	}

	return nil
}

// DeleteSessions - Удаляет указанные сессии пользователей.
func (kcp *KeycloakProviderImpl) DeleteSessions(ctx context.Context, realm string, sessionIDs []string) error {
	adminToken, err := kcp.GetAdminToken(ctx, realm)
	if err != nil {
		return fmt.Errorf("error getting admin token: %w", err)
	}

	for i := range sessionIDs {
		req, err := http.NewRequestWithContext(
			ctx,
			http.MethodDelete,
			fmt.Sprintf("%s/admin/realms/%s/sessions/%s",
				kcp.BaseURL, realm, sessionIDs[i]), nil)
		if err != nil {
			return fmt.Errorf("error creating new http request: %w", err)
		}
		req.Header.Set("Content-Type", "application/json")
		req.Header.Set("Authorization", "Bearer "+adminToken)

		resp, err := kcp.HTTPClient.Do(req)
		if err != nil {
			return fmt.Errorf("error doing http request: %w", err)
		}
		defer resp.Body.Close()

		if resp.StatusCode != http.StatusNoContent {
			if resp.StatusCode == http.StatusNotFound {
				logs.FromContext(ctx).
					Warn().
					Msgf("status code: %d; session %s not found, seems it was already deleted",
						resp.StatusCode, sessionIDs[i])
				continue
			}

			bodyBytes, _ := io.ReadAll(resp.Body)
			return fmt.Errorf("error status of http response [%d]: %s for session %s",
				resp.StatusCode, bodyBytes, sessionIDs[i])
		}
	}

	return nil
}

// GetUserByPhone - Возвращает информацию по пользователю по его номеру телефона.
func (kcp *KeycloakProviderImpl) GetUserByPhone(ctx context.Context, realm string, userPhone string) (User, error) {
	adminToken, err := kcp.GetAdminToken(ctx, realm)
	if err != nil {
		return User{}, fmt.Errorf("error getting admin token: %w", err)
	}

	reqParam := url.QueryEscape(userPhone)
	req, err := http.NewRequestWithContext(
		ctx,
		http.MethodGet,
		fmt.Sprintf("%s/admin/realms/%s/users?username=%s", kcp.BaseURL, realm, reqParam),
		nil,
	)
	if err != nil {
		return User{}, fmt.Errorf("error creating new http request: %w", err)
	}
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer "+adminToken)

	resp, err := kcp.HTTPClient.Do(req)
	if err != nil {
		return User{}, fmt.Errorf("error doing http request: %w", err)
	}
	defer resp.Body.Close()
	if resp.StatusCode != http.StatusOK {
		bodyBytes, _ := io.ReadAll(resp.Body)
		return User{}, fmt.Errorf("error status of http response [%d]: %s", resp.StatusCode, bodyBytes)
	}

	var users UsersResp
	if err = json.NewDecoder(resp.Body).Decode(&users); err != nil {
		return User{}, fmt.Errorf("error decoding http response body: %w", err)
	}

	for i := range users {
		if users[i].Username == userPhone {
			return users[i], nil
		}
	}

	return User{}, fmt.Errorf("user with %s phone number not found in keycloak realm %s", userPhone, realm)
}

// RevokeRefreshToken - Обнуляет пользовательский токен и удаляет связанные с ним сессии.
func (kcp *KeycloakProviderImpl) RevokeRefreshToken(ctx context.Context, realm string, token string) error {
	form := url.Values{}
	form.Add("client_id", kcp.ClientID)
	form.Add("client_secret", kcp.ClientSecret)
	form.Add("token_type_hint", "refresh_token")
	form.Add("token", token)

	req, err := http.NewRequestWithContext(
		ctx,
		http.MethodPost,
		fmt.Sprintf("%s/realms/%s/protocol/openid-connect/revoke", kcp.BaseURL, realm),
		bytes.NewBufferString(form.Encode()),
	)
	if err != nil {
		return fmt.Errorf("error creating new http request: %w", err)
	}
	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")

	resp, err := kcp.HTTPClient.Do(req)
	if err != nil {
		return fmt.Errorf("error doing http request: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		bodyBytes, _ := io.ReadAll(resp.Body)
		return fmt.Errorf("error status of http response [%d]: %s", resp.StatusCode, bodyBytes)
	}

	return nil
}

func (kcp *KeycloakProviderImpl) requestAdminToken(ctx context.Context, realm string) (string, error) {
	form := url.Values{}
	form.Add("client_id", kcp.ClientID)
	form.Add("client_secret", kcp.ClientSecret)
	form.Add("username", kcp.AdminUsername)
	form.Add("password", kcp.AdminPassword)
	form.Add("grant_type", "password")

	req, err := http.NewRequestWithContext(
		ctx,
		http.MethodPost,
		fmt.Sprintf("%s/realms/%s/protocol/openid-connect/token", kcp.BaseURL, realm),
		bytes.NewBufferString(form.Encode()),
	)
	if err != nil {
		return "", fmt.Errorf("error creating new http request: %w", err)
	}
	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")

	resp, err := kcp.HTTPClient.Do(req)
	if err != nil {
		return "", fmt.Errorf("error doing http request: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		bodyBytes, _ := io.ReadAll(resp.Body)
		return "", fmt.Errorf("error status of http response [%d]: %s", resp.StatusCode, bodyBytes)
	}

	var result map[string]interface{}
	if err = json.NewDecoder(resp.Body).Decode(&result); err != nil {
		return "", fmt.Errorf("error decoding http response body: %w", err)
	}

	token, ok := result["access_token"].(string)
	if !ok {
		return "", fmt.Errorf("no access_token in response body")
	}

	return token, nil
}
