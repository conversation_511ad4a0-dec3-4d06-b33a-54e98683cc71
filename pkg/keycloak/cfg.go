package keycloak

import (
	"github.com/spf13/viper"

	"git.redmadrobot.com/backend-go/rmr-pkg/core/cfg"
	"git.redmadrobot.com/backend-go/rmr-pkg/core/cfg/viperx"
)

const (
	KeycloakproxyKcBaseURL          cfg.Key = "KC_BASE_URL"
	KeycloakproxyKcMobileGwRealm    cfg.Key = "KC_MOBILE_GW_REALM"
	KeycloakproxyKcSmeGwRealm       cfg.Key = "KC_SME_GW_REALM"
	KeycloakproxyKcClientID         cfg.Key = "KC_CLIENT_ID"
	KeycloakproxyKcClientSecret     cfg.Key = "KC_CLIENT_SECRET" // #nosec G101
	KeycloakproxyKcAppAdminUsername cfg.Key = "KC_APP_ADMIN_USERNAME"
	KeycloakproxyKcAppAdminPassword cfg.Key = "KC_APP_ADMIN_PASSWORD" // #nosec G101

	DefaultKeycloakproxyKcBaseURL          string = ""
	DefaultKeycloakproxyKcMobileGwRealm    string = ""
	DefaultKeycloakproxyKcSmeGwRealm       string = ""
	DefaultKeycloakproxyKcClientID         string = ""
	DefaultKeycloakproxyKcClientSecret     string = ""
	DefaultKeycloakproxyKcAppAdminUsername string = ""
	DefaultKeycloakproxyKcAppAdminPassword string = ""
)

type Config struct {
	KcBaseURL          string `env:"KC_BASE_URL"`
	KcMobileGwRealm    string `env:"KC_MOBILE_GW_REALM"`
	KcSmeGwRealm       string `env:"KC_SME_GW_REALM"`
	KcClientID         string `env:"KC_CLIENT_ID"`
	KcClientSecret     string `env:"KC_CLIENT_SECRET"`
	KcAppAdminUsername string `env:"KC_APP_ADMIN_USERNAME"`
	KcAppAdminPassword string `env:"KC_APP_ADMIN_PASSWORD"`
}

func CfgFromViper(loader *viper.Viper, keyMapping ...cfg.KeyMap) *Config {
	loader.SetDefault(KeycloakproxyKcBaseURL.String(), DefaultKeycloakproxyKcBaseURL)
	loader.SetDefault(KeycloakproxyKcMobileGwRealm.String(), DefaultKeycloakproxyKcMobileGwRealm)
	loader.SetDefault(KeycloakproxyKcSmeGwRealm.String(), DefaultKeycloakproxyKcSmeGwRealm)
	loader.SetDefault(KeycloakproxyKcClientID.String(), DefaultKeycloakproxyKcClientID)
	loader.SetDefault(KeycloakproxyKcClientSecret.String(), DefaultKeycloakproxyKcClientSecret)
	loader.SetDefault(KeycloakproxyKcAppAdminUsername.String(), DefaultKeycloakproxyKcAppAdminUsername)
	loader.SetDefault(KeycloakproxyKcAppAdminPassword.String(), DefaultKeycloakproxyKcAppAdminPassword)

	return &Config{
		KcBaseURL:          viperx.Get(loader, KeycloakproxyKcBaseURL.Map(keyMapping...), DefaultKeycloakproxyKcBaseURL),
		KcMobileGwRealm:    viperx.Get(loader, KeycloakproxyKcMobileGwRealm.Map(keyMapping...), DefaultKeycloakproxyKcMobileGwRealm),
		KcSmeGwRealm:       viperx.Get(loader, KeycloakproxyKcSmeGwRealm.Map(keyMapping...), DefaultKeycloakproxyKcSmeGwRealm),
		KcClientID:         viperx.Get(loader, KeycloakproxyKcClientID.Map(keyMapping...), DefaultKeycloakproxyKcClientID),
		KcClientSecret:     viperx.Get(loader, KeycloakproxyKcClientSecret.Map(keyMapping...), DefaultKeycloakproxyKcClientSecret),
		KcAppAdminUsername: viperx.Get(loader, KeycloakproxyKcAppAdminUsername.Map(keyMapping...), DefaultKeycloakproxyKcAppAdminUsername),
		KcAppAdminPassword: viperx.Get(loader, KeycloakproxyKcAppAdminPassword.Map(keyMapping...), DefaultKeycloakproxyKcAppAdminPassword),
	}
}
