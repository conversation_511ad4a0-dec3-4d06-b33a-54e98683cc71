package sms

import (
	"context"
	"encoding/xml"
	"fmt"
	"net/http"
	"net/http/httptest"
	"os"
	"reflect"
	"strconv"
	"testing"

	"git.redmadrobot.com/backend-go/rmr-pkg/tools/conversion"
	"git.redmadrobot.com/backend-go/rmr-pkg/tools/serial"
	"git.redmadrobot.com/zaman/backend/zaman/pkg/sms/entity"
	"github.com/stretchr/testify/require"
)

func TestSMSImpl_SendSMS(t *testing.T) {
	resp := &entity.Response{
		XMLName: xml.Name{Local: "package"},
		Text:    "",
		Message: entity.ResponseMessage{
			Text: "",
			Msg: []entity.ResponseMsg{
				{
					Text:     "Test Message",
					ID:       423,
					SmsID:    78,
					SmsCount: 1,
				},
			},
		},
	}

	mockServer := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		if r.<PERSON><PERSON>.Get("Content-Type") != "application/xml" {
			http.Error(w, "invalid content type", http.StatusUnsupportedMediaType)
			return
		}

		w.WriteHeader(http.StatusOK)
		w.Header().Set("Content-Type", "application/xml")
		err := serial.XMLEncode(w, resp)
		require.NoError(t, err)
	}))
	defer mockServer.Close()

	sms := &SMSProviderImpl{
		HTTPClient: &http.Client{},
		BaseURL:    mockServer.URL,
	}

	t.Run("SendSMS", func(t *testing.T) {
		got, err := sms.SendSMS(context.Background(), entity.SendSmsRequestData{
			ID:          conversion.Ptr(int32(78)),
			PhoneNumber: "**********",
			Text:        "Test Message",
		})
		if err != nil {
			t.Errorf("SendSMS() error = %v", err)
			return
		}
		if !reflect.DeepEqual(got, resp) {
			t.Errorf("SendSMS() got = %v, want %v", got, resp)
		}
	})
}

/*
SMS_TEST_BASE_URL=
SMS_TEST_ID=52532
SMS_TEST_PHONE_NUMBER=88005553535
SMS_TEST_TEXT=Test Message
*/

func TestSMSImpl_SendSMS_Real(t *testing.T) {
	baseURL := os.Getenv("SMS_TEST_BASE_URL")
	if baseURL == "" {
		t.Skip("Skipping test because SMS_TEST_BASE_URL env var is not set")
	}

	idStr := os.Getenv("SMS_TEST_ID")
	if idStr == "" {
		t.Skip("Skipping test because SMS_TEST_ID env var is not set")
	}
	id, err := strconv.ParseInt(idStr, 10, 32)
	if err != nil {
		t.Skip("Skipping test because SMS_TEST_ID env var is not valid int")
	}

	phoneNumber := os.Getenv("SMS_TEST_PHONE_NUMBER")
	if phoneNumber == "" {
		t.Skip("Skipping test because SMS_TEST_PHONE_NUMBER env var is not set")
	}

	text := os.Getenv("SMS_TEST_TEXT")
	if text == "" {
		t.Skip("Skipping test because SMS_TEST_TEXT env var is not set")
	}

	sms := &SMSProviderImpl{
		HTTPClient: &http.Client{},
		BaseURL:    baseURL,
	}

	t.Run("SendSMS", func(t *testing.T) {
		got, err := sms.SendSMS(context.Background(), entity.SendSmsRequestData{
			ID:          conversion.Ptr(int32(id)),
			PhoneNumber: phoneNumber,
			Text:        text,
		})
		if err != nil {
			t.Errorf("SendSMS() error = %v", err)
			return
		}
		fmt.Println(got)
	})
}
