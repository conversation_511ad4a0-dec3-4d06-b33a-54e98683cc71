package sms

import "git.redmadrobot.com/backend-go/rmr-pkg/core/errs"

const (
	ErrUnknownDesc          = "unknownError"
	ErrInvalidFormatDesc    = "invalidFormat"
	ErrAuthorizationDesc    = "authorizationError"
	ErrInvalidIDDesc        = "invalidMessageID"
	ErrInvalidSenderDesc    = "invalidSender"
	ErrInvalidRecipientDesc = "invalidRecipient"
	ErrInvalidLengthDesc    = "invalidMessageLength"
	ErrUserDisabledDesc     = "userDisabled"
	ErrBillingDesc          = "billingIssue"
	ErrOverLimitDesc        = "overLimit"
)

var (
	ErrUnknown          = errs.Reasons(ErrUnknownDesc, errs.TypeSystemFailure)
	ErrInvalidFormat    = errs.Reasons(ErrInvalidFormatDesc, errs.TypeIllegalArgument)
	ErrAuthorization    = errs.Reasons(ErrAuthorizationDesc, errs.TypePermissionDenied)
	ErrInvalidID        = errs.Reasons(ErrInvalidIDDesc, errs.TypeIllegalArgument)
	ErrInvalidSender    = errs.Reasons(ErrInvalidSenderDesc, errs.TypeIllegalArgument)
	ErrInvalidRecipient = errs.Reasons(ErrInvalidRecipientDesc, errs.TypeIllegalArgument)
	ErrInvalidLength    = errs.Reasons(ErrInvalidLengthDesc, errs.TypeIllegalArgument)
	ErrUserDisabled     = errs.Reasons(ErrUserDisabledDesc, errs.TypeForbidden)
	ErrBilling          = errs.Reasons(ErrBillingDesc, errs.TypeResourceExhausted)
	ErrOverLimit        = errs.Reasons(ErrOverLimitDesc, errs.TypeResourceExhausted)
)
