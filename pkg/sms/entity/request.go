package entity

import "encoding/xml"

type (
	RequestPackage struct {
		XMLName  xml.Name       `xml:"package"`
		Login    string         `xml:"login,attr"`
		Password string         `xml:"password,attr"`
		Message  RequestMessage `xml:"message"`
	}

	RequestMessage struct {
		Default RequestDefault `xml:"default"`
		Msgs    []RequestMsg   `xml:"msg"`
	}

	RequestDefault struct {
		Sender string `xml:"sender,attr"`
	}

	RequestMsg struct {
		ID        *int32 `xml:"id,attr,omitempty"`
		Recipient string `xml:"recipient,attr"`
		Sender    string `xml:"sender,attr,omitempty"`
		DateBeg   string `xml:"date_beg,attr,omitempty"`
		DateEnd   string `xml:"date_end,attr,omitempty"`
		URL       string `xml:"url,attr,omitempty"`
		Type      int    `xml:"type,attr,omitempty"`
		Priority  string `xml:"priority,attr,omitempty"`
		Text      string `xml:",chardata"`
	}

	SendSmsRequestData struct {
		ID          *int32
		PhoneNumber string
		Text        string
		DateBeg     *string
	}
)
