package survey

import (
	"git.redmadrobot.com/backend-go/rmr-pkg/tools/conversion"

	"git.redmadrobot.com/zaman/backend/zaman/services/loans/entity"
)

func GetAddress(survey *entity.GetSurveyResult) *entity.GetSurveyAddress {
	if survey != nil {
		a := survey.Address
		if a != nil {
			return a
		}
	}

	return &entity.GetSurveyAddress{
		SettlementArea: SettlementArea(nil),
		Settlement:     Settlement(nil),
		Locality:       Locality(nil),
		Street:         conversion.Ptr(""),
		Building:       conversion.Ptr(""),
		Flat:           conversion.Ptr(""),
		IsFull:         false,
	}
}

func GetKatoData(data *entity.KatoData) *entity.KatoData {
	if data != nil {
		return data
	}
	return &entity.KatoData{}
}

func SettlementArea(address *entity.GetSurveyAddress) *entity.KatoData {
	return GetKatoData(address.SettlementArea)
}

func Settlement(address *entity.GetSurveyAddress) *entity.KatoData {
	return GetKatoData(address.Settlement)
}

func Locality(address *entity.GetSurveyAddress) *entity.KatoData {
	return GetKatoData(address.Locality)
}

func GetRegin(address *entity.GetSurveyAddress) *entity.KatoData {
	return GetKatoData(address.Region)
}

func GetDistrict(address *entity.GetSurveyAddress) *entity.KatoData {
	return GetKatoData(address.District)
}
