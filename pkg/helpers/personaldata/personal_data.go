package personaldata

import "git.redmadrobot.com/zaman/backend/zaman/specs/proto/users"

const KZ = "KZ"

func GetBirthPlace(personalData *users.GetPersonalDataResp) *users.BirthPlace {
	if personalData != nil {
		bp := personalData.GetBirthPlace()
		if bp != nil {
			return bp
		}
	}
	return &users.BirthPlace{}
}

func GetCountry(place *users.BirthPlace) *users.SubjectDataParams {
	if place != nil {
		c := place.GetCountry()
		if c != nil {
			return c
		}
	}
	return &users.SubjectDataParams{}
}

func GetCitizenship(personalData *users.GetPersonalDataResp) *users.SubjectDataParams {
	if personalData != nil {
		c := personalData.GetCitizenship()
		return c
	}

	return &users.SubjectDataParams{}
}

func GetCountryRu(personalData *users.GetPersonalDataResp) string {
	place := GetBirthPlace(personalData)
	c := GetCountry(place)

	return c.NameRu
}

func GetCountryKz(personalData *users.GetPersonalDataResp) string {
	place := GetBirthPlace(personalData)
	c := GetCountry(place)

	return c.NameRu
}

func GetCity(personalData *users.GetPersonalDataResp) string {
	place := GetBirthPlace(personalData)
	return place.GetCity()
}

func CodeToStringCode(code int64) string {
	if code == 398 {
		return KZ
	}

	return ""
}
