#!/bin/bash

# <PERSON>ript to update GitLab CI configuration for parallel test execution
# This script removes the resource_group from test job and adds GOMAXPROCS=4

set -e

CI_FILE="deploy/.gitlab-ci.gen.yml"

if [ ! -f "$CI_FILE" ]; then
    echo "Error: CI file $CI_FILE not found!"
    exit 1
fi

echo "Updating GitLab CI configuration..."

# Check if resource_group already exists in the file
if grep -q "resource_group: tests" "$CI_FILE"; then
    # Remove resource_group from test job
    sed -i '' '/^test:/,/^  script:/ {
        s/resource_group: tests//
    }' "$CI_FILE"
    echo "Removed resource_group constraint from test job"
else
    echo "No resource_group constraint found (already removed or not present)"
fi

# Check if GOMAXPROCS variable already exists
if ! grep -q "GOMAXPROCS: \"4\"" "$CI_FILE"; then
    # Add GOMAXPROCS environment variable for parallel execution
    sed -i '' '/^test:/,/^  script:/ {
        /variables:/a\
      GOMAXPROCS: "4"
    }' "$CI_FILE"
    echo "Added GOMAXPROCS=4 environment variable for parallel execution"
else
    echo "GOMAXPROCS environment variable already exists"
fi

echo "GitLab CI configuration updated for parallel test execution!"
echo "Make sure to commit and push these changes to apply them in CI." 