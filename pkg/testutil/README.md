# Тестовые утилиты

Этот пакет содержит утилиты для улучшения работы тестов в микросервисной архитектуре.

## Проблемы, которые решают эти утилиты

1. **Конфликты портов** - При одновременном запуске нескольких тестов сервисы используют фиксированные порты, что приводит к конфликтам.
2. **Конфликты баз данных** - Все тесты используют одинаковые имена баз данных, что создает блокировки при параллельном запуске.
3. **Медленное выполнение тестов** - Из-за проблем 1 и 2 тесты выполняются последовательно и медленно.

## Решения

### Скрипты для исправления тестов

- **apply-dynamic-ports.sh** - Применяет конфигурацию динамических портов для gRPC-серверов
- **apply-dynamic-db.sh** - Применяет конфигурацию уникальных имен баз данных
- **fix-gitlab-ci.sh** - Обновляет конфигурацию GitLab CI для параллельного запуска
- **fix-tests.sh** - Запускает все указанные выше скрипты

## Как использовать

1. Выполните скрипт для обновления всех тестов:

```bash
chmod +x pkg/testutil/*.sh
./pkg/testutil/fix-tests.sh
```

2. Запустите тесты с помощью meroving:

```bash
GOMAXPROCS=4 meroving test --tags dynamic
```

## Что делают скрипты

### apply-dynamic-ports.sh

- Добавляет в `suite_test.go` код для получения свободного порта с помощью `testutil.FindFreePort()`
- Обновляет `grpc_test.go` для использования динамически назначенного порта вместо фиксированного

### apply-dynamic-db.sh

- Добавляет в `suite_test.go` код для генерации уникального имени базы данных с UUID
- Модифицирует `database_test.go` для удаления проверки на суффикс '_test'

### fix-gitlab-ci.sh

- Удаляет параметр `resource_group: tests` из конфигурации CI для разрешения параллельного запуска
- Добавляет переменную окружения `GOMAXPROCS: "4"` для параллельного выполнения

## Полезные функции

- `FindFreePort()` - Находит свободный сетевой порт для использования в тестах
- `FindFreePorts(count int)` - Находит несколько свободных портов

## Примечания по устранению проблем

1. Если скрипты применены, но тесты не проходят, проверьте:
   - Доступность PostgreSQL и Redis при локальном запуске
   - Синтаксис в изменённых файлах
   - Возможные конфликты с другими изменениями
   
2. При запуске в CI могут потребоваться дополнительные настройки:
   - Убедитесь, что CI использует образ с поддержкой параллельного запуска
   - Проверьте, что параллелизм не создает других проблем с ресурсами 