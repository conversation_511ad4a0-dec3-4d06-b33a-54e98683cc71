#!/bin/bash

# Script to update all service tests to use unique database names
# This script modifies suite_test.go and database_test.go files in all services
# to create uniquely named test databases for each test run to avoid conflicts

set -e

# Find all services with tests
SERVICES_DIR="services"
SERVICES=$(find "$SERVICES_DIR" -type d -name "tests" | sed "s|/tests||" | sort)

for SERVICE in $SERVICES; do
    SERVICE_NAME=$(basename "$SERVICE")
    SUITE_TEST_FILE="$SERVICE/tests/suite_test.go"
    DB_TEST_FILE="$SERVICE/tests/database_test.go"
    
    if [ ! -f "$SUITE_TEST_FILE" ] || [ ! -f "$DB_TEST_FILE" ]; then
        echo "Skipping $SERVICE_NAME: Missing required test files"
        continue
    fi

    echo "Processing $SERVICE_NAME..."
    
    # Add uuid import to suite_test.go if not already present
    grep -q "github.com/google/uuid" "$SUITE_TEST_FILE" || sed -i '' '/import (/a\
\	"github.com/google/uuid"' "$SUITE_TEST_FILE"
    
    # Add fmt import if not already present (needed for Sprintf)
    grep -q '"fmt"' "$SUITE_TEST_FILE" || sed -i '' '/import (/a\
\	"fmt"' "$SUITE_TEST_FILE"
    
    # Check if the unique suffix code already exists
    if ! grep -q "Add a unique suffix to the database name" "$SUITE_TEST_FILE"; then
        # Modify suite_test.go to add unique suffix to database name
        sed -i '' '/s.cfg, _ = .*/a\
\	// Add a unique suffix to the database name to avoid conflicts\
\	uniqueSuffix := uuid.New().String()[:8]\
\	s.cfg.PostgresDB.Database = fmt.Sprintf("%s_%s", s.cfg.PostgresDB.Database, uniqueSuffix)' "$SUITE_TEST_FILE"
    fi
    
    # Process database_test.go file to update CreateTestDB function
    echo "Processing $DB_TEST_FILE for CreateTestDB function..."
    
    # Check if the test suffix check exists and remove it
    if grep -q "if !strings.HasSuffix" "$DB_TEST_FILE"; then
        # Update the CreateTestDB function to remove the test suffix check
        sed -i '' '/CreateTestDB/,/pg.DatabaseCreate/ {
            /if !strings.HasSuffix/,/}/d
        }' "$DB_TEST_FILE"
        echo "Removed test suffix check from $DB_TEST_FILE"
    else
        echo "No test suffix check found in $DB_TEST_FILE, skipping this modification"
    fi
    
    echo "Updated $SERVICE_NAME database configuration to use unique names"
done

echo "All test files have been updated to use unique database names!" 