package pkb

import (
	"git.redmadrobot.com/backend-go/rmr-pkg/core/cfg"
	"git.redmadrobot.com/backend-go/rmr-pkg/core/cfg/viperx"
	"github.com/spf13/viper"
)

const (
	authEndpoint                    = "/gov-auth-proxy-service/api/v1/login"
	refreshEndpoint                 = "/gov-auth-proxy-service/api/v1/refresh"
	createReportEndpoint            = "/xdata-api/v1/report/create"
	getReportStatusEndpoint         = "/xdata-api/v1/report/status"
	getReportEndpoint               = "/xdata-api/v1/report/get"
	getFamilyInfoByIinEndpoint      = "/family-info/v1/b2b/get-full-info"
	getPersonalInfoEndpoint         = "/gbdflinfo/v1/clients/%s"
	GetSusnSubjectEndpoint          = "/susn-status-v2/v1/subject"
	GetArmyStatusEndpoint           = "/recruit/v1/%s"
	sendLoanApplicationInfoEndpoint = "/credit-app-v2/credit/loan"
	stopCreditStatusEndpoint        = "/frozencheck/v1/public/isFrozenCheck"
	sendJurSearchByIINEndpoint      = "/gov-jursearch-service/v1/external/send-request"
	getPermitDocumentsByIinEndpoint = "/gbdel-universal/v1/external/send-request"
	CheckIinPhoneMatchEndpoint      = "/bmg/v2/service"
	GetReportPdfServiceEndpoint     = "/pdf_service/get_report"
	GetIncomeCompanyEndpoint        = "/incomesv4/v1/company/sync"
	GetIncomeIndividualEndpoint     = "/incomesv4/v1/individual/sync"
	GetAspStatysByIin               = "/aspv2/asp"
	getReportPdfServiceReportType   = 6
	getReportPdfServiceDocType      = 14
	defaultConsentTokenTerm         = 120
	minConsentTokenTerm             = 120
	maxConsentTokenTerm             = 94672799
	getGbdulbybinEndpoint           = "/gbdulbybin/v1/users/companies/%s"
	GetBeScoreEndpoint              = "/ScoreService/ScoreService"
	getSoHoEntrepreneurEndpoint     = "/soho-v2/v1/scoring/get"

	citsDebtsEndpoint = "/cits-debts/v1/outer/debts-info"
)

const (
	CfgPKBBaseURL       cfg.Key = "PKB_BASE_URL"
	CfgPKBAdditionalURL cfg.Key = "PKB_ADDITIONAL_URL"
	CfgPKBLogin         cfg.Key = "PKB_LOGIN"
	CfgPKBPassword      cfg.Key = "PKB_PASSWORD"
	CfgPKBUseMock       cfg.Key = "PKB_USE_MOCK"

	defaultPKBBaseURL       = "https://api.pkb.kz"
	defaultPKBAdditionalURL = "https://api.pkb.kz"
	defaultPKBLogin         = "login"
	defaultPKBPassword      = "password"
	deafultPKBUseMock       = false
)

type (
	Config struct {
		// BaseURL базовый URL сервиса PKB.
		BaseURL string
		// AdditionalURL дополнительный URL сервиса PKB.
		AdditionalURL string
		// Login логин сервиса PKB.
		Login string
		// Password пароль сервиса PKB.
		Password string
		// UseMock флаг использования mock-сервиса и вкладывания user_id.
		UseMock bool
	}
)

func CfgFromViper(loader *viper.Viper, keyMapping ...cfg.KeyMap) *Config {
	loader.SetDefault(CfgPKBBaseURL.String(), defaultPKBBaseURL)
	loader.SetDefault(CfgPKBAdditionalURL.String(), defaultPKBAdditionalURL)
	loader.SetDefault(CfgPKBLogin.String(), defaultPKBLogin)
	loader.SetDefault(CfgPKBPassword.String(), defaultPKBPassword)
	loader.SetDefault(CfgPKBUseMock.String(), deafultPKBUseMock)

	return &Config{
		BaseURL:       viperx.Get(loader, CfgPKBBaseURL.Map(keyMapping...), ""),
		AdditionalURL: viperx.Get(loader, CfgPKBAdditionalURL.Map(keyMapping...), ""),
		Login:         viperx.Get(loader, CfgPKBLogin.Map(keyMapping...), ""),
		Password:      viperx.Get(loader, CfgPKBPassword.Map(keyMapping...), ""),
		UseMock:       viperx.Get(loader, CfgPKBUseMock.Map(keyMapping...), deafultPKBUseMock),
	}
}
