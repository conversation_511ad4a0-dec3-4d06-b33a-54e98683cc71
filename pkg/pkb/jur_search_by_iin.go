package pkb

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"

	"git.redmadrobot.com/zaman/backend/zaman/pkg/pkb/entity"
)

// Получение юридической информаций по ИИН
func (c *Client) JurSearchByIIN(ctx context.Context, iin string) (*entity.SendJurSearchResponse, error) {
	token, err := c.GetValidToken(ctx)
	if err != nil {
		return nil, fmt.Errorf("error getting valid token: %w", err)
	}

	url := fmt.Sprintf("%s%s", c.BaseURL, sendJurSearchByIINEndpoint)

	requestBody := entity.ParamSendJurSearch{Uin: iin}
	reqBody, err := json.Marshal(requestBody)
	if err != nil {
		return nil, fmt.Errorf("error marshaling request body: %w", err)
	}

	httpReq, err := http.NewRequestWithContext(ctx, http.MethodPost, url, bytes.NewBuffer(reqBody))
	if err != nil {
		return nil, fmt.Errorf("error creating new http request: %w", err)
	}

	httpReq.Header.Set("Authorization", "Bearer "+token)
	httpReq.Header.Set("Content-Type", "application/json")
	httpReq.Header.Set("X-Client-Type", "API")

	resp, err := c.HTTPClient.Do(httpReq)
	if err != nil {
		return nil, fmt.Errorf("error doing http request: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		bodyBytes, _ := io.ReadAll(resp.Body)

		var errResp struct {
			Code    string `json:"code"`
			Message string `json:"message"`
		}
		if err := json.Unmarshal(bodyBytes, &errResp); err == nil {
			if errResp.Code == "JURSEARCH_UIN_NOT_FOUND" {
				return nil, ErrJurSearchUinNotFound
			}
		}

		return nil, fmt.Errorf("error status of http response [%d]: %s", resp.StatusCode, bodyBytes)
	}

	var result entity.SendJurSearchResponse
	if err := json.NewDecoder(resp.Body).Decode(&result); err != nil {
		return nil, fmt.Errorf("error decoding http response body: %w", err)
	}

	return &result, nil
}
