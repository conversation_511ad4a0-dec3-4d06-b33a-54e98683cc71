package pkb

import (
	"context"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/stretchr/testify/require"

	"git.redmadrobot.com/zaman/backend/zaman/pkg/pkb/entity"
)

func TestClient_GetSoHoEntrepreneur(t *testing.T) {
	successHandler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		_ = json.NewEncoder(w).Encode(entity.GetSoHoEntrepreneurResponse{
			ID:        123,
			RiskClass: "A",
			Ball:      100,
			Iin:       "123456789012",
		})
	})

	notFoundHandler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(410)
		_ = json.NewEncoder(w).Encode(entity.ErrWithResultDescription{
			Result:      -1,
			Description: "субъект не найден в БДКИ",
		})
	})

	badRequestHandler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusBadRequest)
		_ = json.NewEncoder(w).Encode(entity.ErrWithResultDescription{
			Result:      3004,
			Description: "ИИН/БИН введен некорректно",
		})
	})

	forbiddenHandler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusForbidden)
		_ = json.NewEncoder(w).Encode(entity.ErrWithResultDescription{
			Result:      4001,
			Description: "нет прав на получение данных",
		})
	})

	internalErrorHandler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusInternalServerError)
		_ = json.NewEncoder(w).Encode(entity.ErrWithResultDescription{
			Result:      -1,
			Description: "ошибка при расчете значения скоринга",
		})
	})

	badJSONHandler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		_, _ = w.Write([]byte(`{"results": "bad`))
	})

	testCases := []struct {
		name            string
		handler         http.Handler
		iin             string
		expectedResp    *entity.GetSoHoEntrepreneurResult
		expectedErrType string
		expectedErr     string
	}{
		{
			name:    "Success",
			handler: successHandler,
			iin:     "123456789012",
			expectedResp: &entity.GetSoHoEntrepreneurResult{
				Results: &entity.GetSoHoEntrepreneurResponse{
					ID:        123,
					RiskClass: "A",
					Ball:      100,
					Iin:       "123456789012",
				},
			},
		},
		{
			name:        "Subject not found - 410",
			handler:     notFoundHandler,
			iin:         "not-found",
			expectedErr: "субъект не найден в БДКИ",
		},
		{
			name:        "IIN format error - 400",
			handler:     badRequestHandler,
			iin:         "bad-iin",
			expectedErr: "ИИН/БИН введен некорректно",
		},
		{
			name:        "Forbidden - 403",
			handler:     forbiddenHandler,
			iin:         "forbidden",
			expectedErr: "нет прав на получение данных",
		},
		{
			name:        "Internal error - 500",
			handler:     internalErrorHandler,
			iin:         "internal-error",
			expectedErr: "ошибка при расчете значения скоринга",
		},
		{
			name:            "Bad JSON response",
			handler:         badJSONHandler,
			iin:             "bad-json",
			expectedErrType: "sys",
			expectedErr:     "error decoding http response body",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			server := httptest.NewServer(tc.handler)
			defer server.Close()

			client := getTestPKBClient(server.URL, server.Client())

			resp, err := client.GetSoHoEntrepreneur(context.Background(), tc.iin)
			t.Logf("resp: %+v", resp)
			if tc.expectedErr != "" {
				if tc.expectedErrType == "resp" || tc.expectedErrType == "" {
					require.NotNil(t, resp)
					require.NotNil(t, resp.Error)
					require.Nil(t, resp.Results)
					require.Equal(t, tc.expectedErr, resp.Error.Description)
				} else if tc.expectedErrType == "sys" {
					require.Nil(t, resp)
					require.NotNil(t, err)
					require.Contains(t, err.Error(), tc.expectedErr)
				}
			} else {
				require.NoError(t, err)
				require.NotNil(t, resp)
				require.NotNil(t, resp.Results)
				require.Equal(t, tc.expectedResp.Results.ID, resp.Results.ID)
				require.Equal(t, tc.expectedResp.Results.RiskClass, resp.Results.RiskClass)
				require.Equal(t, tc.expectedResp.Results.Ball, resp.Results.Ball)
				require.Equal(t, tc.expectedResp.Results.Iin, resp.Results.Iin)
			}
		})
	}
}
