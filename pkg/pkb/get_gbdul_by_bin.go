package pkb

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"

	"git.redmadrobot.com/zaman/backend/zaman/pkg/pkb/entity"
)

// Метод получения информаций организаций по БИН
func (c *Client) GetGbdulbybin(ctx context.Context, bin string) (*entity.GetOrganizationInfoResponse, error) {
	token, err := c.GetValidToken(ctx)
	if err != nil {
		return nil, fmt.Errorf("error getting valid token: %w", err)
	}

	req, err := http.NewRequestWithContext(ctx, http.MethodGet, c.BaseURL+fmt.Sprintf(getGbdulbybinEndpoint, bin), nil)
	if err != nil {
		return nil, fmt.Errorf("error creating HTTP request: %w", err)
	}

	req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", token))
	req.Header.Set("X-Client-Type", "API")

	resp, err := c.HTTPClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("error sending HTTP request: %w", err)
	}
	defer resp.Body.Close()

	bodyBytes, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("error reading response body: %w", err)
	}

	if resp.StatusCode != http.StatusOK {
		if resp.StatusCode == http.StatusInternalServerError {
			return nil, ErrInternalServerError
		}
		var errResp entity.ErrWithCodeResponse
		if err := json.Unmarshal(bodyBytes, &errResp); err != nil {
			return nil, fmt.Errorf("error unmarshaling error response: %w", err)
		}
		return nil, fmt.Errorf("error status of http response [%d]: %s", resp.StatusCode, errResp.Message)
	}

	var orgInfo entity.GetOrganizationInfoResponse
	if err := json.Unmarshal(bodyBytes, &orgInfo); err != nil {
		return nil, fmt.Errorf("error unmarshaling response body: %w", err)
	}

	return &orgInfo, nil
}
