// Code generated by MockGen. DO NOT EDIT.
// Source: ./pkg/pkb/pkb_ko.go

// Package mock_pkb is a generated GoMock package.
package mock_pkb

import (
	context "context"
	pkb "git.redmadrobot.com/zaman/backend/zaman/pkg/pkb/entity"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
)

// MockPKBProvider is a mock of PKBProvider interface.
type MockPKBProvider struct {
	ctrl     *gomock.Controller
	recorder *MockPKBProviderMockRecorder
}

// MockPKBProviderMockRecorder is the mock recorder for MockPKBProvider.
type MockPKBProviderMockRecorder struct {
	mock *MockPKBProvider
}

// NewMockPKBProvider creates a new mock instance.
func NewMockPKBProvider(ctrl *gomock.Controller) *MockPKBProvider {
	mock := &MockPKBProvider{ctrl: ctrl}
	mock.recorder = &MockPKBProviderMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockPKBProvider) EXPECT() *MockPKBProviderMockRecorder {
	return m.recorder
}

// Auth mocks base method.
func (m *MockPKBProvider) Auth(ctx context.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Auth", ctx)
	ret0, _ := ret[0].(error)
	return ret0
}

// Auth indicates an expected call of Auth.
func (mr *MockPKBProviderMockRecorder) Auth(ctx interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Auth", reflect.TypeOf((*MockPKBProvider)(nil).Auth), ctx)
}

// CheckIinPhoneMatch mocks base method.
func (m *MockPKBProvider) CheckIinPhoneMatch(ctx context.Context, iin, phoneNumber string) (*pkb.IinPhoneMatchResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckIinPhoneMatch", ctx, iin, phoneNumber)
	ret0, _ := ret[0].(*pkb.IinPhoneMatchResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckIinPhoneMatch indicates an expected call of CheckIinPhoneMatch.
func (mr *MockPKBProviderMockRecorder) CheckIinPhoneMatch(ctx, iin, phoneNumber interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckIinPhoneMatch", reflect.TypeOf((*MockPKBProvider)(nil).CheckIinPhoneMatch), ctx, iin, phoneNumber)
}

// CreateReport mocks base method.
func (m *MockPKBProvider) CreateReport(ctx context.Context, req pkb.ReportRequest) (*pkb.CreateReportResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateReport", ctx, req)
	ret0, _ := ret[0].(*pkb.CreateReportResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateReport indicates an expected call of CreateReport.
func (mr *MockPKBProviderMockRecorder) CreateReport(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateReport", reflect.TypeOf((*MockPKBProvider)(nil).CreateReport), ctx, req)
}

// GetArmyStatusByIIN mocks base method.
func (m *MockPKBProvider) GetArmyStatusByIIN(ctx context.Context, iin string) (*pkb.ArmyStatus, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetArmyStatusByIIN", ctx, iin)
	ret0, _ := ret[0].(*pkb.ArmyStatus)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetArmyStatusByIIN indicates an expected call of GetArmyStatusByIIN.
func (mr *MockPKBProviderMockRecorder) GetArmyStatusByIIN(ctx, iin interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetArmyStatusByIIN", reflect.TypeOf((*MockPKBProvider)(nil).GetArmyStatusByIIN), ctx, iin)
}

// GetFamilyInfoByIIN mocks base method.
func (m *MockPKBProvider) GetFamilyInfoByIIN(ctx context.Context, iin string) (*pkb.FamilyInfoResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetFamilyInfoByIIN", ctx, iin)
	ret0, _ := ret[0].(*pkb.FamilyInfoResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetFamilyInfoByIIN indicates an expected call of GetFamilyInfoByIIN.
func (mr *MockPKBProviderMockRecorder) GetFamilyInfoByIIN(ctx, iin interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetFamilyInfoByIIN", reflect.TypeOf((*MockPKBProvider)(nil).GetFamilyInfoByIIN), ctx, iin)
}

// GetGbdulbybin mocks base method.
func (m *MockPKBProvider) GetGbdulbybin(ctx context.Context, bin string) (*pkb.GetOrganizationInfoResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGbdulbybin", ctx, bin)
	ret0, _ := ret[0].(*pkb.GetOrganizationInfoResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGbdulbybin indicates an expected call of GetGbdulbybin.
func (mr *MockPKBProviderMockRecorder) GetGbdulbybin(ctx, bin interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGbdulbybin", reflect.TypeOf((*MockPKBProvider)(nil).GetGbdulbybin), ctx, bin)
}

// GetIncomeCompany mocks base method.
func (m *MockPKBProvider) GetIncomeCompany(ctx context.Context, req *pkb.IncomeCompanyRequest) (*pkb.GetIncomeCompanyResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetIncomeCompany", ctx, req)
	ret0, _ := ret[0].(*pkb.GetIncomeCompanyResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetIncomeCompany indicates an expected call of GetIncomeCompany.
func (mr *MockPKBProviderMockRecorder) GetIncomeCompany(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetIncomeCompany", reflect.TypeOf((*MockPKBProvider)(nil).GetIncomeCompany), ctx, req)
}

// GetIncomeIndividual mocks base method.
func (m *MockPKBProvider) GetIncomeIndividual(ctx context.Context, req pkb.IncomeIndividualRequest) (*pkb.GetIncomeIndividualResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetIncomeIndividual", ctx, req)
	ret0, _ := ret[0].(*pkb.GetIncomeIndividualResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetIncomeIndividual indicates an expected call of GetIncomeIndividual.
func (mr *MockPKBProviderMockRecorder) GetIncomeIndividual(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetIncomeIndividual", reflect.TypeOf((*MockPKBProvider)(nil).GetIncomeIndividual), ctx, req)
}

// GetPermitDocumentsByIin mocks base method.
func (m *MockPKBProvider) GetPermitDocumentsByIin(ctx context.Context, iin string) (*pkb.GetPermitDocumentsByIinResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPermitDocumentsByIin", ctx, iin)
	ret0, _ := ret[0].(*pkb.GetPermitDocumentsByIinResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPermitDocumentsByIin indicates an expected call of GetPermitDocumentsByIin.
func (mr *MockPKBProviderMockRecorder) GetPermitDocumentsByIin(ctx, iin interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPermitDocumentsByIin", reflect.TypeOf((*MockPKBProvider)(nil).GetPermitDocumentsByIin), ctx, iin)
}

// GetPersonalInfoByIin mocks base method.
func (m *MockPKBProvider) GetPersonalInfoByIin(ctx context.Context, iin string) (*pkb.GetPersonalInfoResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPersonalInfoByIin", ctx, iin)
	ret0, _ := ret[0].(*pkb.GetPersonalInfoResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPersonalInfoByIin indicates an expected call of GetPersonalInfoByIin.
func (mr *MockPKBProviderMockRecorder) GetPersonalInfoByIin(ctx, iin interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPersonalInfoByIin", reflect.TypeOf((*MockPKBProvider)(nil).GetPersonalInfoByIin), ctx, iin)
}

// GetReport mocks base method.
func (m *MockPKBProvider) GetReport(ctx context.Context, reportID string) (*pkb.ReportResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetReport", ctx, reportID)
	ret0, _ := ret[0].(*pkb.ReportResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetReport indicates an expected call of GetReport.
func (mr *MockPKBProviderMockRecorder) GetReport(ctx, reportID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetReport", reflect.TypeOf((*MockPKBProvider)(nil).GetReport), ctx, reportID)
}

// GetReportPdfService mocks base method.
func (m *MockPKBProvider) GetReportPdfService(ctx context.Context, params pkb.ParamsGetReportPdfService) (*pkb.GetReportPdfServiceResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetReportPdfService", ctx, params)
	ret0, _ := ret[0].(*pkb.GetReportPdfServiceResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetReportPdfService indicates an expected call of GetReportPdfService.
func (mr *MockPKBProviderMockRecorder) GetReportPdfService(ctx, params interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetReportPdfService", reflect.TypeOf((*MockPKBProvider)(nil).GetReportPdfService), ctx, params)
}

// GetReportStatus mocks base method.
func (m *MockPKBProvider) GetReportStatus(ctx context.Context, reportID string) (*pkb.CreateReportResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetReportStatus", ctx, reportID)
	ret0, _ := ret[0].(*pkb.CreateReportResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetReportStatus indicates an expected call of GetReportStatus.
func (mr *MockPKBProviderMockRecorder) GetReportStatus(ctx, reportID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetReportStatus", reflect.TypeOf((*MockPKBProvider)(nil).GetReportStatus), ctx, reportID)
}

// GetStopCreditStatusByIin mocks base method.
func (m *MockPKBProvider) GetStopCreditStatusByIin(ctx context.Context, iin string) (*pkb.StopCreditStatusResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetStopCreditStatusByIin", ctx, iin)
	ret0, _ := ret[0].(*pkb.StopCreditStatusResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetStopCreditStatusByIin indicates an expected call of GetStopCreditStatusByIin.
func (mr *MockPKBProviderMockRecorder) GetStopCreditStatusByIin(ctx, iin interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetStopCreditStatusByIin", reflect.TypeOf((*MockPKBProvider)(nil).GetStopCreditStatusByIin), ctx, iin)
}

// GetSusnSubject mocks base method.
func (m *MockPKBProvider) GetSusnSubject(ctx context.Context, params pkb.ParamsGetSusnSubject) (*pkb.GetSusnSubjectResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSusnSubject", ctx, params)
	ret0, _ := ret[0].(*pkb.GetSusnSubjectResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSusnSubject indicates an expected call of GetSusnSubject.
func (mr *MockPKBProviderMockRecorder) GetSusnSubject(ctx, params interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSusnSubject", reflect.TypeOf((*MockPKBProvider)(nil).GetSusnSubject), ctx, params)
}

// GetValidToken mocks base method.
func (m *MockPKBProvider) GetValidToken(ctx context.Context) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetValidToken", ctx)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetValidToken indicates an expected call of GetValidToken.
func (mr *MockPKBProviderMockRecorder) GetValidToken(ctx interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetValidToken", reflect.TypeOf((*MockPKBProvider)(nil).GetValidToken), ctx)
}

// JurSearchByIIN mocks base method.
func (m *MockPKBProvider) JurSearchByIIN(ctx context.Context, iin string) (*pkb.SendJurSearchResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "JurSearchByIIN", ctx, iin)
	ret0, _ := ret[0].(*pkb.SendJurSearchResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// JurSearchByIIN indicates an expected call of JurSearchByIIN.
func (mr *MockPKBProviderMockRecorder) JurSearchByIIN(ctx, iin interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "JurSearchByIIN", reflect.TypeOf((*MockPKBProvider)(nil).JurSearchByIIN), ctx, iin)
}

// RefreshToken mocks base method.
func (m *MockPKBProvider) RefreshToken(ctx context.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RefreshToken", ctx)
	ret0, _ := ret[0].(error)
	return ret0
}

// RefreshToken indicates an expected call of RefreshToken.
func (mr *MockPKBProviderMockRecorder) RefreshToken(ctx interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RefreshToken", reflect.TypeOf((*MockPKBProvider)(nil).RefreshToken), ctx)
}

// SendLoanApplicationInfo mocks base method.
func (m *MockPKBProvider) SendLoanApplicationInfo(ctx context.Context, params pkb.ParamsSendLoanApplicationInfo) (*pkb.SendLoanApplicationInfoResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SendLoanApplicationInfo", ctx, params)
	ret0, _ := ret[0].(*pkb.SendLoanApplicationInfoResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SendLoanApplicationInfo indicates an expected call of SendLoanApplicationInfo.
func (mr *MockPKBProviderMockRecorder) SendLoanApplicationInfo(ctx, params interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendLoanApplicationInfo", reflect.TypeOf((*MockPKBProvider)(nil).SendLoanApplicationInfo), ctx, params)
}
