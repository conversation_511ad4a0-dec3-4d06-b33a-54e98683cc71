package pkb

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"

	"git.redmadrobot.com/zaman/backend/zaman/pkg/pkb/entity"
)

// Метод получения полного кредитного отчета
func (c *Client) GetReportPdfService(
	ctx context.Context,
	params entity.ParamsGetReportPdfService,
) (*entity.GetReportPdfServiceResp, error) {
	requestBody := entity.GetReportPdfServiceReq{
		Username:         c.Login,
		Password:         c.Password,
		ReportType:       getReportPdfServiceReportType,
		DocType:          getReportPdfServiceDocType,
		DocNumber:        params.Iin,
		ConsentConfirmed: true,
	}

	reqBody, err := json.Marshal(requestBody)
	if err != nil {
		return nil, fmt.Errorf("error marshaling request body: %w", err)
	}

	req, err := http.NewRequestWithContext(
		ctx, http.MethodPost, c.BaseURL+GetReportPdfServiceEndpoint, bytes.NewBuffer(reqBody),
	)
	if err != nil {
		return nil, fmt.Errorf("error creating request: %w", err)
	}

	req.Header.Set("Content-Type", "application/json")

	resp, err := c.HTTPClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("error sending request: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		if resp.StatusCode == http.StatusInternalServerError {
			return nil, ErrInternalServerError
		}
		bodyBytes, _ := io.ReadAll(resp.Body)
		return nil, fmt.Errorf("error status of http response [%d]: %s", resp.StatusCode, string(bodyBytes))
	}

	// Читаем тело ответа
	bodyBytes, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("error reading response body: %w", err)
	}

	err = verifyZipFile(bodyBytes)
	if err != nil {
		return nil, err
	}

	report, err := processGetReportPdfServiceZipResponse(bodyBytes)
	if err != nil {
		return nil, err
	}

	return report, nil
}
