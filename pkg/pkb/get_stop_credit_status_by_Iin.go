package pkb

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"

	"git.redmadrobot.com/zaman/backend/zaman/pkg/pkb/entity"
)

// Получение статуса по признаку стоп кредит по ИИН
func (c *Client) GetStopCreditStatusByIin(ctx context.Context, iin string) (*entity.StopCreditStatusResponse, error) {
	token, err := c.GetValidToken(ctx)
	if err != nil {
		return nil, fmt.Errorf("error getting valid token: %w", err)
	}

	queryParams := url.Values{}
	queryParams.Set("Consent-Confirmed", "1")
	queryParams.Set("IIN", iin)

	url := fmt.Sprintf("%s%s?%s", c.BaseURL, stopCreditStatusEndpoint, queryParams.Encode())

	httpReq, err := http.NewRequestWithContext(ctx, http.MethodGet, url, nil)
	if err != nil {
		return nil, fmt.Errorf("error creating new http request: %w", err)
	}

	httpReq.Header.Set("Authorization", "Bearer "+token)

	// Выполняем запрос
	resp, err := c.HTTPClient.Do(httpReq)
	if err != nil {
		return nil, fmt.Errorf("error doing http request: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		if resp.StatusCode == http.StatusInternalServerError {
			return nil, ErrInternalServerError
		}
		bodyBytes, _ := io.ReadAll(resp.Body)
		return nil, fmt.Errorf("error status of http response [%d]: %s", resp.StatusCode, string(bodyBytes))
	}

	var frozenStatus entity.StopCreditStatusResponse
	if err := json.NewDecoder(resp.Body).Decode(&frozenStatus); err != nil {
		return nil, fmt.Errorf("error decoding http response body: %w", err)
	}

	return &frozenStatus, nil
}
