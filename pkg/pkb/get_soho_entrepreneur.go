package pkb

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"

	"git.redmadrobot.com/zaman/backend/zaman/pkg/pkb/entity"
)

func (c *Client) GetSoHoEntrepreneur(ctx context.Context, iin string) (*entity.GetSoHoEntrepreneurResult, error) {
	token, err := c.GetValidToken(ctx)
	if err != nil {
		return nil, fmt.Errorf("error getting token: %w", err)
	}

	reqHTTP, err := http.NewRequestWithContext(
		ctx,
		http.MethodGet,
		c.BaseURL+getSoHoEntrepreneurEndpoint+"?IIN_BIN="+iin,
		bytes.NewBuffer(nil),
	)
	if err != nil {
		return nil, fmt.Errorf("error creating request: %w", err)
	}

	reqHTTP.Header.Set("Authorization", "Bearer "+token)
	reqHTTP.Header.Set("Content-Type", "application/json")

	resp, err := c.HTTPClient.Do(reqHTTP)
	if err != nil {
		return nil, fmt.Errorf("error sending request: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		var errResp entity.ErrWithResultDescription
		bodyBytes, _ := io.ReadAll(resp.Body)
		if err = json.Unmarshal(bodyBytes, &errResp); err != nil {
			return nil, fmt.Errorf("error decoding http err response body: %w", err)
		}
		return &entity.GetSoHoEntrepreneurResult{Error: errResp}, nil
	}

	var result entity.GetSoHoEntrepreneurResponse
	if err = json.NewDecoder(resp.Body).Decode(&result); err != nil {
		return nil, fmt.Errorf("error decoding http response body: %w", err)
	}

	return &entity.GetSoHoEntrepreneurResult{Results: &result}, nil
}
