package pkb

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"

	"git.redmadrobot.com/zaman/backend/zaman/pkg/pkb/entity"

	"github.com/google/uuid"
)

// Получение информацию о статусах СУСН
func (c *Client) GetSusnSubject(ctx context.Context, param entity.ParamsGetSusnSubject) (*entity.GetSusnSubjectResponse, error) {
	token, err := c.GetValidToken(ctx)
	if err != nil {
		return nil, fmt.Errorf("error getting token: %w", err)
	}

	_, err = uuid.Parse(param.RequestID)
	if err != nil {
		return nil, fmt.Errorf("error parsing request id: %w", err)
	}

	body, err := json.Marshal(param.GetSusnSubjectRequest())
	if err != nil {
		return nil, fmt.Errorf("error marshalling body: %w", err)
	}

	req, err := http.NewRequestWithContext(
		ctx,
		http.MethodPost,
		c.AdditionalURL+GetSusnSubjectEndpoint,
		bytes.NewBuffer(body),
	)
	if err != nil {
		return nil, fmt.Errorf("error creating request: %w", err)
	}

	req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", token))
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("fcbrequestid", param.RequestID)

	resp, err := c.HTTPClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("error sending request: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		if resp.StatusCode == http.StatusInternalServerError {
			return nil, ErrInternalServerError
		}
		var errResp entity.ErrWithCodeResponse
		bodyBytes, _ := io.ReadAll(resp.Body)
		if err = json.Unmarshal(bodyBytes, &errResp); err != nil {
			return nil, fmt.Errorf("error decoding http err response body: %w", err)
		}
		return nil, fmt.Errorf("error status of http response [%d]: %s", resp.StatusCode, errResp.Message)
	}

	var result entity.GetSusnSubjectResponse
	if err = json.NewDecoder(resp.Body).Decode(&result); err != nil {
		return nil, fmt.Errorf("error decoding http response body: %w", err)
	}

	return &result, nil
}
