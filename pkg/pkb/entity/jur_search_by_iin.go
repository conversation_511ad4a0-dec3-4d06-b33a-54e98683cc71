package entity

type ParamSendJurSearch struct {
	Uin string `json:"uin"` // ИИН/БИН пользователя.
}

type SendJurSearchResponse struct {
	RequestNumber string        `json:"requestNumber"` // Уникальный номер запроса.
	Code          string        `json:"code"`          // Код ответа ("OK" для успешного запроса).
	Message       string        `json:"message"`       // Сообщение ответа.
	Data          JurSearchData `json:"data"`          // Данные ответа.
}

type JurSearchData struct {
	BinIin       string               `json:"binIin"`                 // ИИН/БИН, по которому запрашивалась информация.
	Name         string               `json:"name"`                   // Полное наименование ИП/ЮЛ.
	RegisterDate string               `json:"registerDate,omitempty"` // Дата регистрации (опционально, присутствует для ЮЛ).
	Oked         JurSearchCodeAndName `json:"oked"`                   // ОКЭД.
	Krp          JurSearchCodeAndName `json:"krp"`                    // КРП с филиалами.
	Kfc          JurSearchCodeAndName `json:"kfc,omitempty"`          // КФС (опционально, присутствует для ЮЛ).
	Kse          JurSearchCodeAndName `json:"kse"`                    // Код сектора экономики.
	KatoCode     string               `json:"katoCode"`               // Код КАТО.
	KatoID       int                  `json:"katoId"`                 // ID КАТО.
	KatoAddress  string               `json:"katoAddress"`            // Местонахождение ИП или юридический адрес ЮЛ.
	Fio          string               `json:"fio"`                    // ФИО руководителя.
	IP           bool                 `json:"ip"`                     // Признак ИП ("true" для ИП, "false" для ЮЛ).
	KrpBf        JurSearchCodeAndName `json:"krpBf"`                  // КРП без филиалов.
}

type JurSearchCodeAndName struct {
	Code string `json:"code"` // Код.
	Name string `json:"name"` // Наименование.
}
