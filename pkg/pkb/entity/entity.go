package entity

const (
	PresetIndividual = "individual"
	PresetSme        = "Zaman-onboarding"
	ConsentGranted   = "1" // Признак наличия согласия на обработку персональных данных
)

type Token struct {
	Hash      string `json:"hash"`
	ExpiresAt string `json:"expires_at"`
	TTL       int    `json:"ttl"`
	IssueDate string `json:"issue_date"`
}

type Tokens struct {
	Access  Token `json:"access"`
	Refresh Token `json:"refresh"`
}

type RefreshRequest struct {
	TokenHash string `json:"token_hash"`
}
