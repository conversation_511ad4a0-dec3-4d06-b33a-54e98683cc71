package entity

type GetPersonalInfoResponse struct {
	Code    string     `json:"code"`
	Message string     `json:"message"`
	Data    PersonData `json:"data"`
}

type PersonData struct {
	RequestID  string          `json:"request_id"`
	StatusCode string          `json:"status_code"`
	PersonData Person          `json:"person_data"`
	PhotoData  PersonPhotoData `json:"person_photo_data"`
}

type Person struct {
	IIN          string      `json:"iin"`
	Surname      string      `json:"surname"`
	EngSurname   string      `json:"engSurname"`
	Name         string      `json:"name"`
	EngFirstName string      `json:"engFirstName"`
	Patronymic   string      `json:"patronymic"`
	DOB          string      `json:"dob"`
	Gender       NameCode    `json:"gender"`
	Nationality  NameCode    `json:"nationality"`
	Citizenship  NameCode    `json:"citizenship"`
	LifeStatus   NameCode    `json:"life_status"`
	BirthPlace   BirthPlace  `json:"birth_place"`
	Address      Address     `json:"address"`
	AddressTemp  AddressTemp `json:"addressTemp"`
	Documents    Documents   `json:"documents"`
}

type NameCode struct {
	Code       string `json:"code"`
	NameKZ     string `json:"name_kz"`
	NameRU     string `json:"name_ru"`
	ChangeDate string `json:"change_date"`
}

type BirthPlace struct {
	City     string   `json:"city"`
	Country  NameCode `json:"country"`
	District NameCode `json:"district"`
	Region   NameCode `json:"region"`
}

type Address struct {
	Street    string   `json:"street"`
	Building  string   `json:"building"`
	Flat      string   `json:"flat"`
	BeginDate string   `json:"beginDate"`
	Country   NameCode `json:"country"`
	District  NameCode `json:"district"`
	Region    NameCode `json:"region"`
}

type AddressTemp struct {
	Address []TempAddress `json:"Address"`
}

type TempAddress struct {
	Type      NameCode `json:"type"`
	Country   NameCode `json:"country"`
	District  NameCode `json:"district"`
	Region    NameCode `json:"region"`
	City      string   `json:"city"`
	Street    string   `json:"street"`
	Building  string   `json:"building"`
	Corpus    string   `json:"corpus"`
	Flat      string   `json:"flat"`
	BeginDate string   `json:"beginDate"`
	EndDate   string   `json:"endDate"`
	ArCode    string   `json:"arCode"`
}

type Documents struct {
	Document []Document `json:"document"`
}

type Document struct {
	Number            string   `json:"number"`
	BeginDate         string   `json:"begin_date"`
	EndDate           string   `json:"end_date"`
	Surname           string   `json:"surname"`
	Name              string   `json:"name"`
	Patronymic        string   `json:"patronymic"`
	BirthDate         string   `json:"birth_date"`
	Type              NameCode `json:"type"`
	IssueOrganization NameCode `json:"issue_organization"`
	Status            NameCode `json:"status"`
}

type PersonPhotoData struct {
	Response PhotoResponse `json:"response"`
}

type PhotoResponse struct {
	ResponseData PhotoResponseData `json:"responseData"`
}

type PhotoResponseData struct {
	Data PhotoData `json:"data"`
}

type PhotoData struct {
	MessageResult MessageResult `json:"message_result"`
	PersonList    PersonList    `json:"person_list"`
}

type MessageResult struct {
	Code       string `json:"code"`
	NameRU     string `json:"name_ru"`
	NameKZ     string `json:"name_kz"`
	ChangeDate string `json:"change_date,omitempty"`
}

type PersonList struct {
	Person []PhotoPerson `json:"person,omitempty"`
}

type PhotoPerson struct {
	IIN         string `json:"iin"`
	CodeTypeDoc string `json:"code_type_doc"`
	NumDoc      string `json:"num_doc"`
	Photo       string `json:"photo"`
}
