package entity

import "github.com/google/uuid"

type IncomeCompanyRequest struct {
	Bin              string                `json:"iin"`
	RequestType      IncomeRequestTypeEnum `json:"request_type"`
	RequestID        uuid.UUID             `json:"requestId"`
	Consent          IncomeConsentTypeEnum `json:"consent"`
	EmployeeName     string                `json:"employee_name"`
	ConsentTokenTerm int                   `json:"consent_token_term"`
}

type GetIncomeCompanyResp struct {
	Result GetIncomeResult `json:"result"`
}
