package entity

type AspReq struct {
	Iin              string `json:"iin,omitempty"`
	EmployeeName     string `json:"employee_name,omitempty"`
	Consent          int    `json:"consent,omitempty"`
	ConsentTokenTerm int    `json:"consent_token_term,omitempty"`
	RequestID        string `json:"request_id,omitempty"`
}

type AspResp struct {
	Result ApsResult `json:"result,omitempty"`
}

type ApsResult struct {
	Iin                 string  `json:"iin,omitempty"`
	Surname             string  `json:"surname,omitempty"`
	FirstName           string  `json:"firstname,omitempty"`
	SecondName          string  `json:"secondname,omitempty"`
	Sex                 string  `json:"sex,omitempty"`
	BirthDate           string  `json:"birthDate,omitempty"`
	State               string  `json:"state,omitempty"`
	SumAsp              float64 `json:"summAsp,omitempty"`
	FirstPaymentDate    string  `json:"firstPaymentDate,omitempty"`
	LastPaymentDate     string  `json:"lastPaymentDate,omitempty"`
	NumberOfChildren    int8    `json:"numberOfChildren,omitempty"`
	NumberFamilyMembers int8    `json:"numberFamilyMembers,omitempty"`
	SddFamilyMembers    float64 `json:"sddFamilyMembers,omitempty"`
}
