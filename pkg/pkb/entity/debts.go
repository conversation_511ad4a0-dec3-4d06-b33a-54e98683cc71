package entity

type (
	CitsDebtsResp struct {
		RequestNumber string    `json:"requestNumber"`
		Code          string    `json:"code"`
		Message       string    `json:"message"`
		Data          DebtsData `json:"data"`
	}

	DebtsData struct {
		IinBin                      string       `json:"iinBin"`
		NameKz                      string       `json:"nameKz"`
		NameRu                      string       `json:"nameRu"`
		TotalArrear                 string       `json:"totalArrear"`
		TotalTaxArrear              string       `json:"totalTaxArrear"`
		PensionContributionArrear   string       `json:"pensionContributionArrear"`
		SocialContributionArrear    string       `json:"socialContributionArrear"`
		SocialHealthInsuranceArrear string       `json:"socialHealthInsuranceArrear"`
		TaxOrgInfo                  []TaxOrgInfo `json:"taxOrgInfo"`
	}

	BccArrearsInfo struct {
		Bcc           string `json:"bcc"`
		BccNameRu     string `json:"bccNameRu"`
		BccNameKz     string `json:"bccNameKz"`
		TaxArrear     string `json:"taxArrear"`
		PoenaArrear   string `json:"poenaArrear"`
		PercentArrear string `json:"percentArrear"`
		FineArrear    string `json:"fineArrear"`
		TotalArrear   string `json:"totalArrear"`
	}

	TaxPayerInfo struct {
		BccArrearsInfo []BccArrearsInfo `json:"bccArrearsInfo"`
		TaxArrear      string           `json:"taxArrear"`
		PoenaArrear    string           `json:"poenaArrear"`
		PercentArrear  string           `json:"percentArrear"`
		FineArrear     string           `json:"fineArrear"`
		TotalArrear    string           `json:"totalArrear"`
	}

	TaxOrgInfo struct {
		CharCode                    string       `json:"charCode"`
		NameRu                      string       `json:"nameRu"`
		NameKz                      string       `json:"nameKz"`
		ReportAcrualDate            string       `json:"reportAcrualDate"`
		TotalArrear                 string       `json:"totalArrear"`
		TotalTaxArrear              string       `json:"totalTaxArrear"`
		PensionContributionArrear   string       `json:"pensionContributionArrear"`
		SocialContributionArrear    string       `json:"socialContributionArrear"`
		SocialHealthInsuranceArrear string       `json:"socialHealthInsuranceArrear"`
		TaxPayerInfo                TaxPayerInfo `json:"taxPayerInfo"`
	}
)
