package entity

import "github.com/google/uuid"

type IncomeIndividualRequest struct {
	Iin              string                `json:"iin"`
	RequestType      IncomeRequestTypeEnum `json:"request_type"`
	RequestID        uuid.UUID             `json:"requestId"`
	Consent          IncomeConsentTypeEnum `json:"consent"`
	EmployeeName     string                `json:"employee_name"`
	ConsentTokenTerm int                   `json:"consent_token_term"`
}

type IncomeRequestTypeEnum string

const (
	IncomeCompanyRequestTypeSixMonth           IncomeRequestTypeEnum = "SIX_MONTH"
	IncomeCompanyRequestTypeOneYear            IncomeRequestTypeEnum = "ONE_YEAR"
	IncomeCompanyRequestTypeDeduction3         IncomeRequestTypeEnum = "DEDUCTION_3"
	IncomeCompanyRequestTypeDeduction6         IncomeRequestTypeEnum = "DEDUCTION_6"
	IncomeCompanyRequestTypeDeduction12        IncomeRequestTypeEnum = "DEDUCTION_12"
	IncomeCompanyRequestTypeDeduction36        IncomeRequestTypeEnum = "DEDUCTION_36"
	IncomeCompanyRequestTypeSocial6            IncomeRequestTypeEnum = "SOCIAL_6"
	IncomeCompanyRequestTypeSocial12           IncomeRequestTypeEnum = "SOCIAL_12"
	IncomeCompanyRequestTypePension6           IncomeRequestTypeEnum = "PENSION_6"
	IncomeCompanyRequestTypePension12          IncomeRequestTypeEnum = "PENSION_12"
	IncomeCompanyRequestTypeEmploymentContract IncomeRequestTypeEnum = "EMPLOYMENT_CONTRACT"
	IncomeCompanyRequestWageFund               IncomeRequestTypeEnum = "WAGE_FUND"
)

type IncomeConsentTypeEnum int

const (
	IncomeConsentPaper IncomeConsentTypeEnum = iota + 1
	IncomeConsentBio
	IncomeConsentACP
	IncomeConsentDigitalID
	IncomeConsentDigitalOTP
)

type GetIncomeIndividualResp struct {
	ConsentToken string          `json:"consentToken"`
	Result       GetIncomeResult `json:"result"`
}

type GetIncomeResult struct {
	Response GetIncomeResponse `json:"response"`
}

type GetIncomeResponse struct {
	ResponseData GetIncomeResponseData `json:"responseData"`
	ResponseInfo GetIncomeResponseInfo `json:"responseInfo"`
}

type GetIncomeResponseData struct {
	Data GetIncomeData `json:"data"`
}

type GetIncomeData struct {
	ResponseCode   string             `json:"responseCode"`
	ResponseString string             `json:"responseString"`
	Signature      GetIncomeSignature `json:"signature"`
}

type GetIncomeSignature struct {
	KeyInfo        GetIncomeSignatureKeyInfo    `json:"keyInfo"`
	SignatureValue string                       `json:"signatureValue"`
	SignedInfo     GetIncomeSignatureSignedInfo `json:"signedInfo"`
}

type GetIncomeResponseInfo struct {
	CorrelationID string       `json:"correlationId"`
	MessageID     string       `json:"messageId"`
	ResponseDate  string       `json:"responseDate"`
	SessionID     string       `json:"sessionId"`
	Status        StatusIncome `json:"status"`
}

type StatusIncome struct {
	Code    string `json:"code"`
	Message string `json:"message"`
}

type GetIncomeSignatureKeyInfo struct {
	X509Data GetIncomeSignatureKeyInfoX509Data `json:"x509Data"`
}
type GetIncomeSignatureKeyInfoX509Data struct {
	X509Certificate string `json:"X509Certificate"`
}

type GetIncomeSignatureSignedInfo struct {
	CanonicalizationMethod AlgorithmField
	Reference              GetIncomeSignatureSignedInfoReference
	SignatureMethod        AlgorithmField
}

type GetIncomeSignatureSignedInfoReference struct {
	DigestMethod AlgorithmField  `json:"digestMethod"`
	DigestValue  string          `json:"digestValue"`
	Transforms   TransformsField `json:"Transforms"`
	URI          string          `json:"URI"`
}

type TransformsField struct {
	Transform []AlgorithmField `json:"transform"`
}

type AlgorithmField struct {
	Algorithm string `json:"Algorithm"`
}
