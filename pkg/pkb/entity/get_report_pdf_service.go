package entity

import "encoding/xml"

// ParamsGetReportPdfService - структура для запроса отчета
type ParamsGetReportPdfService struct {
	Iin string
}

// GetReportPdfServiceReq - структура для запроса отчета
type GetReportPdfServiceReq struct {
	Username         string `json:"username"`
	Password         string `json:"password"`
	ReportType       int    `json:"report_type" default:"6"` // тип отчета выбираем 6
	DocType          int    `json:"doc1_type" default:"14"`  // тип документа мы выбираем это вариант иин
	DocNumber        string `json:"doc1_number"`             // иин
	ConsentConfirmed bool   `json:"consent_confirmed" default:"true"`
}

// GetReportPdfServiceStatus - структура для разбора XML ответа
type GetReportPdfServiceStatus struct {
	Status       xml.Name `xml:"Status"`
	Error        bool     `xml:"Error"`
	ErrorCode    int      `xml:"ErrorCode"`
	ErrorMessage string   `xml:"ErrorMessage"`
	ReportID     int      `xml:"ReportId"` // reference_id - Идентификатор запроса
	UsageID      int      `xml:"UsageId"`
}

// GetReportPdfServiceResp - структура для ответа отчета
type GetReportPdfServiceResp struct {
	XMLName      xml.Name       `xml:"Report"`
	Root         RootPdfService `xml:"Root,omitempty"`
	Status       *GetReportPdfServiceStatus
	Signature    *SignaturePdfService `xml:"Signature,omitempty"` // Цифровая подпись (как сосед <Root>)
	RequestLogID int64
}

type GetReportRespHeader struct {
	Text        string               `xml:",chardata"`
	CigWsHeader GetReportCigWsHeader `xml:"CigWsHeader"`
}

type GetReportCigWsHeader struct {
	Text     string `xml:",chardata"`
	Xmlns    string `xml:"xmlns,attr"`
	Ns3      string `xml:"ns3,attr"`
	Culture  string `xml:"Culture"`
	Password string `xml:"ClientSecret"`
	UserID   string `xml:"UserId"`
	UserName string `xml:"UserName"`
}

type GetReportRespBody struct {
	Text              string            `xml:",chardata"`
	GetReportResponse GetReportResponse `xml:"GetReportResponse"`
}

type GetReportResponse struct {
	Text            string          `xml:",chardata"`
	Xmlns           string          `xml:"xmlns,attr"`
	GetReportResult GetReportResult `xml:"GetReportResult"`
}

type GetReportResult struct {
	Text      string    `xml:",chardata"`
	SigResult CigResult `xml:"SigResult"`
}

type CigResult struct {
	Text              string              `xml:",chardata"`
	Xmlns             string              `xml:"xmlns,attr"`
	Ns2               string              `xml:"ns2,attr"`
	Version           string              `xml:"Version,attr"`
	DateTime          string              `xml:"DateTime"`
	ReferenceID       string              `xml:"ReferenceId"`
	ServiceName       string              `xml:"ServiceName"`
	ResultCulture     string              `xml:"ResultCulture"`
	ResultCode        string              `xml:"ResultCode"`
	ResultDescription string              `xml:"ResultDescription"`
	UsageIdentity     string              `xml:"UsageIdentity"`
	Result            GetReportResultRoot `xml:"Result"`
}

type GetReportResultRoot struct {
	Text string         `xml:",chardata"`
	Root RootPdfService `xml:"Root,omitempty"`
}

// RootPdfService представляет корневой элемент <Root> внутри <Report>.
// Используем типы-значения для полей, представляющих секции.
type RootPdfService struct {
	XMLName                  xml.Name                                `xml:"Root"`                     // Явно указываем имя тега
	Title                    TitlePdfService                         `xml:"Title"`                    // Заголовок отчета
	Header                   HeaderPdfService                        `xml:"Header"`                   // Шапка с данными субъекта
	SubjectDetails           SubjectDetailsPdfService                `xml:"SubjectDetails"`           // Дополнительные данные субъекта
	SubjectsAddress          SubjectsAddressPdfService               `xml:"SubjectsAddress"`          // Адреса субъекта
	IdentificationDocuments  IdentificationDocumentsPdfService       `xml:"IdentificationDocuments"`  // Документы субъекта
	ClassificationOfBorrower ClassificationOfBorrowerPdfService      `xml:"ClassificationOfBorrower"` // Классификация заемщика
	TazalauBankruptInfo      TazalauBankruptInfoPdfService           `xml:"TazalauBankruptInfo"`      // Информация о банкротстве
	NegativeData             NegativeDataPdfService                  `xml:"NegativeData"`             // Негативные статусы
	SummaryInformation       SummaryInformationPdfService            `xml:"SummaryInformation"`       // Общая информация (сводка)
	ExistingContracts        ContractListPdfService                  `xml:"ExistingContracts"`        // Детализация: Действующие договора
	TerminatedContracts      ContractListPdfService                  `xml:"TerminatedContracts"`      // Детализация: Завершенные договора
	ExistingApplications     ApplicationListPdfService               `xml:"ExistingApplications"`     // Детализация: Действующие заявки
	RejectedApplications     ApplicationListPdfService               `xml:"RejectedApplications"`     // Детализация: Отклоненные заявки
	WithdrawnApplications    ApplicationListPdfService               `xml:"WithdrawnApplications"`    // Детализация: Отозванные заявки
	InterconnectedSubjects   InterconnectedSubjectsSectionPdfService `xml:"InterconnectedSubjects"`   // Детализация: Связанные объекты
	NumberOfQueries          NumberOfQueriesSectionPdfService        `xml:"NumberOfQueries"`          // Детализация: Количество запросов
	RelatedCompanies         RelatedCompaniesPdfService              `xml:"RelatedCompanies"`         // Аффилированность
	PublicSources            PublicSourcesPdfService                 `xml:"PublicSources"`            // Информация из публичных источников
	Sems                     string                                  `xml:"Sems"`                     // Неясный элемент
	RWA170                   RWA170PdfService                        `xml:"RWA170"`                   // Задолженность по бланковым займам
	Footer                   FooterPdfService                        `xml:"Footer"`                   // Подвал отчета
	StopCredit               StopCreditPdfService                    `xml:"StopCredit"`               // Флаг блокировки кредитования
	Gamblers                 GamblersPdfService                      `xml:"Gamblers"`                 // Флаг игорной зависимости
}

type RelatedCompaniesPdfService struct {
	Text        string                      `xml:",chardata"`
	Title       string                      `xml:"title,attr"`
	SubjectRole RelatedCompaniesSubjectRole `xml:"SubjectRole"`
}

type RelatedCompaniesSubjectRole struct {
	Text    string                  `xml:",chardata"`
	ID      string                  `xml:"id,attr"`
	Title   string                  `xml:"title,attr"`
	Value   string                  `xml:"value,attr"`
	Company []RelatedCompanyCompany `xml:"Company"`
}

type RelatedCompanyCompany struct {
	Text        string           `xml:",chardata"`
	Title       string           `xml:"title,attr"`
	CompanyID   CompanyInfoData  `xml:"CompanyID"`
	CompanyName CompanyInfoData  `xml:"CompanyName"`
	Documents   CompanyDocuments `xml:"Documents"`
}

type CompanyInfoData struct {
	Text  string `xml:",chardata"`
	Title string `xml:"title,attr"`
	Value string `xml:"value,attr"`
}

type CompanyDocuments struct {
	Text     string            `xml:",chardata"`
	Title    string            `xml:"title,attr"`
	Document []CompanyDocument `xml:"Document"`
}

type CompanyDocument struct {
	Text           string          `xml:",chardata"`
	Title          string          `xml:"title,attr"`
	DocumentType   CompanyInfoData `xml:"DocumentType"`
	DocumentNumber CompanyInfoData `xml:"DocumentNumber"`
}

// SignaturePdfService представляет элемент <ds:Signature> (Цифровая подпись XML).
type SignaturePdfService struct {
	XMLName  xml.Name `xml:"Signature"` // Имя элемента 'Signature'
	InnerXML string   `xml:",innerxml"` // Захватывает все внутреннее содержимое XML подписи как строку
}

// --- Структуры, используемые внутри RootPdfService ---

// TitlePdfService представляет элемент <Title> в XML.
type TitlePdfService struct {
	XMLName xml.Name `xml:"Title"`                  // Явно указываем имя тега
	Intitle string   `xml:"intitle,attr,omitempty"` // Атрибут 'intitle'
	Name    string   `xml:"name,attr,omitempty"`    // Атрибут 'name' / 'title_name'
	Title   string   `xml:"title,attr,omitempty"`   // Атрибут 'title'
}

// HeaderPdfService представляет элемент <HeaderRequest> (Заголовок/Шапка отчета).
type HeaderPdfService struct {
	XMLName          xml.Name                 `xml:"Header"`                    // Явно указываем имя тега
	EntityType       string                   `xml:"EntityType,attr,omitempty"` // Тип субъекта - header_entity_type
	ReportCode       string                   `xml:"ReportCode,attr,omitempty"` // Код отчета - header_report_code
	Intitle          string                   `xml:"intitle,attr,omitempty"`    // Внутренний заголовок - header_intitle
	NameAttr         string                   `xml:"name,attr,omitempty"`       // Атрибут 'name' (переименовано) - header_name
	STitle           string                   `xml:"stitle,attr,omitempty"`     // Дополнительный заголовок - header_stitle
	Subject          string                   `xml:"subject,attr,omitempty"`    // Тип субъекта (текст) - header_subject
	Title            string                   `xml:"title,attr,omitempty"`      // Заголовок отчета - header_title
	FIUniqueNumberID ValueAttributePdfService `xml:"FIUniqueNumberID"`          // Уникальный номер фин. учреждения // fi_
	RegistrationID   ValueAttributePdfService `xml:"RegistrationID"`            // Регистрационный ИД субъекта // registration_
	RNN              ValueAttributePdfService `xml:"RNN"`                       // РНН
	SIC              ValueAttributePdfService `xml:"SIC"`                       // СИК
	IIN              ValueAttributePdfService `xml:"IIN"`                       // ИИН
	DateOfBirth      ValueAttributePdfService `xml:"DateOfBirth"`               // Дата рождения
	Gender           ValueAttributePdfService `xml:"Gender"`                    // Пол
	Surname          ValueAttributePdfService `xml:"Surname"`                   // Фамилия
	Name             ValueAttributePdfService `xml:"Name"`                      // Имя (как дочерний элемент)
	FathersName      ValueAttributePdfService `xml:"FathersName"`               // Отчество
	BirthName        ValueAttributePdfService `xml:"BirthName"`                 // Фамилия при рождении
	CityOfBirth      ValueAttributePdfService `xml:"CityOfBirth"`               // Город рождения
	Education        ValueAttributePdfService `xml:"Education"`                 // Образование
	MatrialStatus    ValueAttributePdfService `xml:"MatrialStatus"`             // Семейное положение
	RegionOfBirth    ValueAttributePdfService `xml:"RegionOfBirth"`             // Область/район рождения
	CountryOfBirth   ValueAttributePdfService `xml:"CountryOfBirth"`            // Страна рождения
}

// SubjectDetailsPdfService представляет элемент <SubjectDetails> (Доп. информация о субъекте).
type SubjectDetailsPdfService struct {
	XMLName            xml.Name                   `xml:"SubjectDetails"`        // Явно указываем имя тега
	NameAttr           string                     `xml:"name,attr,omitempty"`   // Атрибут 'name'
	STitle             string                     `xml:"stitle,attr,omitempty"` // Дополнительный заголовок
	Title              string                     `xml:"title,attr,omitempty"`  // Заголовок
	NumberOfDependents []ValueAttributePdfService `xml:"NumberOfDependents"`    // Кол-во иждивенцев
	NumberOfChildern   []ValueAttributePdfService `xml:"NumberOfChildern"`      // Кол-во детей
	StreetAddress      ValueAttributePdfService   `xml:"StreetAddress"`         // Адрес улицы
	Street             ValueAttributePdfService   `xml:"Street"`                // Улица
	City               ValueAttributePdfService   `xml:"City"`                  // Город
	ZipCode            ValueAttributePdfService   `xml:"ZipCode"`               // Почтовый индекс
	Region             ValueAttributePdfService   `xml:"Region"`                // Область/район
	Country            ValueAttributePdfService   `xml:"Country"`               // Страна
	Number             ValueAttributePdfService   `xml:"Number"`                // Номер дома
	CellularPhone      ValueAttributePdfService   `xml:"CellularPhone"`         // Сотовый телефон
	HomePhone          ValueAttributePdfService   `xml:"HomePhone"`             // Домашний телефон
	OfficePhone        ValueAttributePdfService   `xml:"OfficePhone"`           // Рабочий телефон
	Fax                ValueAttributePdfService   `xml:"Fax"`                   // Факс
	Email              ValueAttributePdfService   `xml:"Email"`                 // E-mail
	EmployeesSalary    ValueAttributePdfService   `xml:"EmployeesSalary"`       // Зарплата
}

// SubjectsAddressPdfService представляет элемент <SubjectsAddress> (Список адресов субъекта).
type SubjectsAddressPdfService struct {
	XMLName        xml.Name                  `xml:"SubjectsAddress"`        // Явно указываем имя тега
	STitle         string                    `xml:"stitle,attr,omitempty"`  // Дополнительный заголовок 1
	STitle2        string                    `xml:"stitle2,attr,omitempty"` // Дополнительный заголовок 2
	Title          string                    `xml:"title,attr,omitempty"`   // Заголовок
	AddressDetails []AddressDetailPdfService `xml:"Address"`                // Список адресов (тег <Address>)
}

// IdentificationDocumentsPdfService представляет элемент <IdentificationDocuments> (Список документов).
type IdentificationDocumentsPdfService struct {
	XMLName         xml.Name                   `xml:"IdentificationDocuments"` // Явно указываем имя тега
	STitle          string                     `xml:"stitle,attr,omitempty"`   // Дополнительный заголовок
	Title           string                     `xml:"title,attr,omitempty"`    // Заголовок
	DocumentDetails []DocumentDetailPdfService `xml:"Document"`                // Список документов (тег <Document>)
}

// ClassificationOfBorrowerPdfService представляет элемент <ClassificationOfBorrower> (Классификация заемщика).
type ClassificationOfBorrowerPdfService struct {
	XMLName                xml.Name                 `xml:"ClassificationOfBorrower"` // Явно указываем имя тега
	NameAttr               string                   `xml:"name,attr,omitempty"`      // Атрибут 'name'
	Title                  string                   `xml:"title,attr,omitempty"`     // Заголовок
	BorrowerClassification ValueAttributePdfService `xml:"BorrowerClassification"`   // Классификация субъекта
	Patent                 ValueAttributePdfService `xml:"Patent"`                   // Номер патента
	Resident               ValueAttributePdfService `xml:"Resident"`                 // Резидент/Нерезидент
	SubjectsPosition       ValueAttributePdfService `xml:"SubjectsPosition"`         // Род занятий
	Citizenship            ValueAttributePdfService `xml:"Citizenship"`              // Гражданство
	SubjectsEmployment     ValueAttributePdfService `xml:"SubjectsEmployment"`       // Характер занятости
	ForeignersCitizenship  ValueAttributePdfService `xml:"ForeignersCitizenship"`    // Гражданство иностранца
	EconomicActivityGroup  ValueAttributePdfService `xml:"EconomicActivityGroup"`    // Отрасль экономики
}

type TazalauInfoValueAttribute struct {
	Text  string `xml:",chardata"`
	Title string `xml:"title,attr"`
	Value string `xml:"value,attr"`
}

type BankruptInfoDataDesc struct {
	Text                 string                    `xml:",chardata"`
	ProceedingsStartDate TazalauInfoValueAttribute `xml:"ProceedingsStartDate"`
	CourtDecisionDate    TazalauInfoValueAttribute `xml:"CourtDecisionDate"`
	TerminationDate      TazalauInfoValueAttribute `xml:"TerminationDate"`
	TerminationReason    TazalauInfoValueAttribute `xml:"TerminationReason"`
	TerminationInitiator TazalauInfoValueAttribute `xml:"TerminationInitiator"`
	BankruptStartDate    TazalauInfoValueAttribute `xml:"BankruptStartDate"`
	BankruptEndDate      TazalauInfoValueAttribute `xml:"BankruptEndDate"`
	BankruptEndReason    TazalauInfoValueAttribute `xml:"BankruptEndReason"`
}

type BankruptInfoData struct {
	Name                     string                    `xml:"_name,attr"`
	Title                    string                    `xml:"title,attr"`
	Text                     string                    `xml:",chardata"`
	BankruptcyType           TazalauInfoValueAttribute `xml:"BankruptcyType"`
	StatementNumber          TazalauInfoValueAttribute `xml:"StatementNumber"`
	StatementStatus          TazalauInfoValueAttribute `xml:"StatementStatus"`
	LastModificationDate     TazalauInfoValueAttribute `xml:"LastModificationDate"`
	StatementApplicationDate TazalauInfoValueAttribute `xml:"StatementApplicationDate"`
	Desc                     BankruptInfoDataDesc      `xml:"Desc"`
}

type TazalauBankruptInfoPdfService struct {
	XMLName xml.Name                  `xml:"TazalauBankruptInfo"`
	Text    string                    `xml:",chardata"`
	Name    string                    `xml:"name,attr"`
	Title   string                    `xml:"title,attr"`
	Status  ValueAttributePdfService  `xml:"Status"`
	Iin     TazalauInfoValueAttribute `xml:"Iin"`
	FIO     TazalauInfoValueAttribute `xml:"FIO"`
	Item    BankruptInfoData          `xml:"Item"`
}

// NegativeDataPdfService представляет элемент <NegativeData> (Блок с негативными статусами).
type NegativeDataPdfService struct {
	XMLName        xml.Name                   `xml:"NegativeData"`         // Явно указываем имя тега
	NameAttr       string                     `xml:"name,attr,omitempty"`  // Атрибут 'name'
	Title          string                     `xml:"title,attr,omitempty"` // Заголовок
	NegativeStatus []NegativeStatusPdfService `xml:"NegativeStatus"`       // Список групп статусов
}

// SummaryInformationPdfService представляет элемент <SummaryInformation> (Общая информация - сводка).
type SummaryInformationPdfService struct {
	XMLName                      xml.Name                       `xml:"SummaryInformation"`           // Явно указываем имя тега
	Title                        string                         `xml:"title,attr,omitempty"`         // Заголовок
	ExistingContracts            ContractsSummaryPdfService     `xml:"ExistingContracts"`            // Сводка по действующим
	TerminatedContracts          ContractsSummaryPdfService     `xml:"TerminatedContracts"`          // Сводка по завершенным
	TerminatedContractsDateLimit TerminatedContractsDateLimit   `xml:"TerminatedContractsDateLimit"` // Завершенные > 5 лет
	WithdrawnApplications        TerminatedContractsDateLimit   `xml:"WithdrawnApplications"`        // Отозванные - данные приходят в том же формате что и в TerminatedContractsDateLimit
	NumberOfApplications         NumberOfApplicationsPdfService `xml:"NumberOfApplications"`         // Кол-во отказанных/отозванных
	NumberOfInquiries            NumberOfInquiriesPdfService    `xml:"NumberOfInquiries"`            // Кол-во запросов
}

type TerminatedContractsDateLimit struct {
	Text        string                         `xml:",chardata"`
	Title       string                         `xml:"title,attr"`
	SubjectRole TerminatedContractsSubjectRole `xml:"SubjectRole"`
}

type TerminatedContractsSubjectRole struct {
	Text                 string                   `xml:",chardata"`
	ID                   string                   `xml:"id,attr"`
	Title                string                   `xml:"title,attr"`
	Type                 string                   `xml:"type,attr"`
	NumberOfContracts    ValueAttributePdfService `xml:"NumberOfContracts"`
	NumberOfCreditLines  ValueAttributePdfService `xml:"NumberOfCreditLines"`
	TotalOutstandingDebt ValueAttributePdfService `xml:"TotalOutstandingDebt"`
	TotalDebtOverdue     ValueAttributePdfService `xml:"TotalDebtOverdue"`
	TotalFine            ValueAttributePdfService `xml:"TotalFine"`
	TotalPenalty         ValueAttributePdfService `xml:"TotalPenalty"`
	ContractStatuses     ContractStatuses         `xml:"ContractStatuses"`
}

type ContractStatuses struct {
	Text           string         `xml:",chardata"`
	ContractStatus ContractStatus `xml:"ContractStatus"`
}

type ContractStatus struct {
	Text  string `xml:",chardata"`
	Count string `xml:"count,attr"`
	Value string `xml:"value,attr"`
}

type NumberOfApplicationsPdfService struct {
	Text            string          `xml:",chardata"`
	Title           string          `xml:"title,attr"`
	Value           string          `xml:"value,attr"`
	ApplicationType ApplicationType `xml:"ApplicationType"`
}

type ApplicationType struct {
	Text          string              `xml:",chardata"`
	Title         string              `xml:"title,attr"`
	Type          string              `xml:"type,attr"`
	Value         string              `xml:"value,attr"`
	Week          ApplicationTypeItem `xml:"Week"`
	Month         ApplicationTypeItem `xml:"Month"`
	Quarter       ApplicationTypeItem `xml:"Quarter"`
	Year          ApplicationTypeItem `xml:"Year"`
	LastThreeYear ApplicationTypeItem `xml:"LastThreeYear"`
}

type ApplicationTypeItem struct {
	Text  string `xml:",chardata"`
	Title string `xml:"title,attr"`
	Value string `xml:"value,attr"`
}

// ContractListPdfService представляет разделы <ExistingContracts> или <TerminatedContracts>, содержащие список контрактов.
type ContractListPdfService struct {
	NameAttr  string               `xml:"name,attr,omitempty"`   // Атрибут 'name'
	STitle    string               `xml:"stitle,attr,omitempty"` // Доп. заголовок
	Title     string               `xml:"title,attr,omitempty"`  // Заголовок
	Contracts []ContractPdfService `xml:"Contract"`              // Список контрактов (тег <Contract>)
}

// ApplicationListPdfService представляет разделы заявок (<ExistingApplications>, <RejectedApplications>, <WithdrawnApplications>).
type ApplicationListPdfService struct {
	NameAttr string `xml:"name,attr,omitempty"`   // Атрибут 'name'
	STitle   string `xml:"stitle,attr,omitempty"` // Доп. заголовок
	Title    string `xml:"title,attr,omitempty"`  // Заголовок
}

// InterconnectedSubjectsSectionPdfService представляет основной раздел <InterconnectedSubjects> (Связанные объекты).
type InterconnectedSubjectsSectionPdfService struct {
	XMLName               xml.Name                                `xml:"InterconnectedSubjects"` // Явно указываем имя тега
	NameAttr              string                                  `xml:"name,attr,omitempty"`    // Атрибут 'name'
	Title                 string                                  `xml:"title,attr,omitempty"`   // Заголовок
	InterconnectedSubject []InterconnectedSubjectDetailPdfService `xml:"InterconnectedSubject"`  // Список связанных (тег <InterconnectedSubject>)
}

// NumberOfQueriesSectionPdfService представляет раздел <NumberOfQueries> (Количество запросов).
type NumberOfQueriesSectionPdfService struct {
	XMLName             xml.Name                `xml:"NumberOfQueries"`      // Явно указываем имя тега
	Title               string                  `xml:"title,attr,omitempty"` // Заголовок
	Value               string                  `xml:"value,attr"`           // Общее количество
	DetailsQueries7days DetailsQueries7days     `xml:"DetailsQueries7days"`  // Детали за 7 дней
	Days30              QueriesPeriodPdfService `xml:"Days30"`               // За 30 дней
	Days90              QueriesPeriodPdfService `xml:"Days90"`               // За 90 дней
	Days120             QueriesPeriodPdfService `xml:"Days120"`              // За 120 дней
	Days180             QueriesPeriodPdfService `xml:"Days180"`              // За 180 дней
	Days360             QueriesPeriodPdfService `xml:"Days360"`              // За 360 дней
}

type DetailsQueries7days struct {
	Text  string  `xml:",chardata"`
	Title string  `xml:"title,attr"`
	Value string  `xml:"value,attr"`
	Query []Query `xml:"query"`
}

type Query struct {
	Text  string `xml:",chardata"`
	Value string `xml:"value,attr"`
}

// PublicSourcesPdfService представляет раздел <PublicSources> (Доп. информация из публичных источников).
type PublicSourcesPdfService struct {
	XMLName       xml.Name                     `xml:"PublicSources"`        // Явно указываем имя тега
	Title         string                       `xml:"title,attr,omitempty"` // Заголовок
	QamqorList    PublicSourceDetailPdfService `xml:"QamqorList"`           // Розыск Комитетом правовой статистики
	QamqorAlimony PublicSourceDetailPdfService `xml:"QamqorAlimony"`        // Розыск алиментщиков
	RNUGosZakup   PublicSourceDetailPdfService `xml:"RNUGosZakup"`          // Реестр недобросовестных участников госзакупок
	FalseBusi     PublicSourceDetailPdfService `xml:"FalseBusi"`            // Лжепредпринимательство
	TerrorList    PublicSourceDetailPdfService `xml:"TerrorList"`           // Финансирование терроризма/экстремизма
	Areears       PublicSourceDetailPdfService `xml:"Areears"`              // Задолженность по таможенным платежам/налогам
	Bankruptcy    PublicSourceDetailPdfService `xml:"Bankruptcy"`           // Списки банкротов
	L150o10       PublicSourceDetailPdfService `xml:"L150o10"`              // Задолженность по налогам > 150/10 МРП
	KgdWanted     PublicSourceDetailPdfService `xml:"KgdWanted"`            // Розыск КГД
	Ksk           Ksk                          `xml:"Ksk"`                  // Задолженность КСК
}

// RWA170PdfService представляет раздел <RWA170> (Задолженность по бланковым займам).
type RWA170PdfService struct {
	XMLName      xml.Name               `xml:"RWA170"`                      // Явно указываем имя тега
	NotFoundText string                 `xml:"NotFoundText,attr,omitempty"` // Текст, если информация не найдена
	Title        string                 `xml:"title,attr,omitempty"`        // Заголовок
	Contracts    RWAContractsPdfService `xml:"Contracts"`                   // Контракты
}

// FooterPdfService представляет элемент <Footer> (Подвал отчета).
type FooterPdfService struct {
	XMLName                        xml.Name                     `xml:"Footer"`                     // Явно указываем имя тега
	EndOfReport                    string                       `xml:"EndOfReport,attr,omitempty"` // Атрибут "КОНЕЦ КРЕДИТНОГО ОТЧЕТА"
	NameAttr                       string                       `xml:"name,attr,omitempty"`        // Атрибут 'name'
	DateOfIssue                    ValueAttributePdfService     `xml:"DateOfIssue"`                // Дата выдачи отчета
	ContactInfo                    ValueAttributePdfService     `xml:"ContactInfo"`                // Контактная информация бюро
	Contracts                      FooterContracts              `xml:"Contracts"`                  // Контракты
	FiveYearsWarning               ValueAttributePdfService     `xml:"FiveYearsWarning"`
	AuthenticityWarning            ValueAttributePdfService     `xml:"AuthenticityWarning"`
	Footer                         ValueAttributePdfService     `xml:"Footer"`
	ExistingContracts              ValueAttributePdfService     `xml:"ExistingContracts"`
	TerminatedContracts            ValueAttributePdfService     `xml:"TerminatedContracts"`
	WithdrawnApplications          ValueAttributePdfService     `xml:"WithdrawnApplications"`
	NoAddresses                    ValueAttributePdfService     `xml:"NoAddresses"`
	NoContacts                     ValueAttributePdfService     `xml:"NoContacts"`
	Guarant                        ValueAttributePdfService     `xml:"Guarant"`
	Borrower                       ValueAttributePdfService     `xml:"Borrower"`
	SubjectRole                    ValueAttributePdfService     `xml:"SubjectRole"`
	NoDocuments                    ValueAttributePdfService     `xml:"NoDocuments"`
	NoExistingContracts            ValueAttributePdfService     `xml:"NoExistingContracts"`
	NoNegativeStatus               ValueAttributePdfService     `xml:"NoNegativeStatus"`
	NoTerminatedContracts          ValueAttributePdfService     `xml:"NoTerminatedContracts"`
	NoTerminatedContractsDateLimit ValueAttributePdfService     `xml:"NoTerminatedContractsDateLimit"`
	NoWithdrawnApplications        ValueAttributePdfService     `xml:"NoWithdrawnApplications"`
	TitleDetailedInformation       ValueAttributePdfService     `xml:"TitleDetailedInformation"`
	TitleAdditionalInformation     ValueAttributePdfService     `xml:"TitleAdditionalInformation"`
	TitleLiabilities               FooterTitleLiabilities       `xml:"TitleLiabilities"`
	InterconnectedSubjects         FooterInterconnectedSubjects `xml:"InterconnectedSubjects"`
	TitleSummaryInformation        ValueAttributePdfService     `xml:"TitleSummaryInformation"`
	Contract                       ValueAttributePdfService     `xml:"Contract"`
	SubjectDetails2                ValueAttributePdfService     `xml:"SubjectDetails2"`
	AmountOverdue                  ValueAttributePdfService     `xml:"AmountOverdue"`
	MaxOverdueAmount               ValueAttributePdfService     `xml:"MaxOverdueAmount"`
	MaxOverdueCount                ValueAttributePdfService     `xml:"MaxOverdueCount"`
	PaymentsCalendar               FooterPaymentsCalendar       `xml:"PaymentsCalendar"`
	NoSubjects                     ValueAttributePdfService     `xml:"NoSubjects"`
	NoViolations                   ValueAttributePdfService     `xml:"NoViolations"`
	InformationFromDB              ValueAttributePdfService     `xml:"InformationFromDB"`
	InformationFromPS              ValueAttributePdfService     `xml:"InformationFromPS"`
	InformationFromPSS             ValueAttributePdfService     `xml:"InformationFromPSS"`
	InformationFromPSS3            ValueAttributePdfService     `xml:"InformationFromPSS3"`
	InformationFromPCR             ValueAttributePdfService     `xml:"InformationFromPCR"`
	InformationFromGDB             ValueAttributePdfService     `xml:"InformationFromGDB"`
	Tranches                       ValueAttributePdfService     `xml:"Tranches"`
	TestWarning                    ValueAttributePdfService     `xml:"TestWarning"`
	AdvCC                          ValueAttributePdfService     `xml:"AdvCC"`
	IncludingRehabilitation        ValueAttributePdfService     `xml:"IncludingRehabilitation"`
	BlockList1                     ValueAttributePdfService     `xml:"BlockList1"`
	BlockList2                     ValueAttributePdfService     `xml:"BlockList2"`
	GamblingTrue                   ValueAttributePdfService     `xml:"GamblingTrue"`
	GamblingFalse                  ValueAttributePdfService     `xml:"GamblingFalse"`
	Sign                           ValueAttributePdfService     `xml:"Sign"`
	Note                           ValueAttributePdfService     `xml:"Note"`
	Note1                          ValueAttributePdfService     `xml:"Note1"`
	Note2                          ValueAttributePdfService     `xml:"Note2"`
}

type FooterPaymentsCalendar struct {
	Text         string                   `xml:",chardata"`
	Title        string                   `xml:"title,attr"`
	Description1 ValueAttributePdfService `xml:"Description1"`
	Description2 ValueAttributePdfService `xml:"Description2"`
}

type FooterTitleLiabilities struct {
	Text                string                   `xml:",chardata"`
	Stitle              string                   `xml:"stitle,attr"`
	ContractsNumber     ValueAttributePdfService `xml:"ContractsNumber"`
	ContractsPhase      ValueAttributePdfService `xml:"ContractsPhase"`
	ContractStatus      ValueAttributePdfService `xml:"ContractStatus"`
	OutstandingAmount   ValueAttributePdfService `xml:"OutstandingAmount"`
	OverdueAmount       ValueAttributePdfService `xml:"OverdueAmount"`
	NumberOfCreditLines ValueAttributePdfService `xml:"NumberOfCreditLines"`
	TotalFine           ValueAttributePdfService `xml:"TotalFine"`
	TotalPenalty        ValueAttributePdfService `xml:"TotalPenalty"`
}

type FooterInterconnectedSubjects struct {
	Text        string                   `xml:",chardata"`
	TypeOfLink  ValueAttributePdfService `xml:"TypeOfLink"`
	SubjectCode ValueAttributePdfService `xml:"SubjectCode"`
	SubjectName ValueAttributePdfService `xml:"SubjectName"`
	SubjectRNN  ValueAttributePdfService `xml:"SubjectRNN"`
	SubjectIIN  ValueAttributePdfService `xml:"SubjectIIN"`
}

type FooterContracts struct {
	Text                  string                   `xml:",chardata"`
	AdditionalInformation ValueAttributePdfService `xml:"AdditionalInformation"`
	Balance               ValueAttributePdfService `xml:"Balance"`
	Collaterals           ValueAttributePdfService `xml:"Collaterals"`
	Contract              ValueAttributePdfService `xml:"Contract"`
	DaysOverdue           ValueAttributePdfService `xml:"DaysOverdue"`
	Payment               ValueAttributePdfService `xml:"Payment"`
	RelatedSubjects       FooterRelatedSubjects    `xml:"RelatedSubjects"`
	ParentContractList    FooterParentContractList `xml:"ParentContractList"`
	Status                ValueAttributePdfService `xml:"Status"`
	Settlement            ValueAttributePdfService `xml:"Settlement"`
	TypeOfGuarantee       ValueAttributePdfService `xml:"TypeOfGuarantee"`
	ValueOfGuarantee      ValueAttributePdfService `xml:"ValueOfGuarantee"`
	ProlongationStartDate ValueAttributePdfService `xml:"ProlongationStartDate"`
	ProlongationEndDate   ValueAttributePdfService `xml:"ProlongationEndDate"`
	Collateral            FooterCollateral         `xml:"Collateral"`
}

type FooterRelatedSubjects struct {
	Text        string                   `xml:",chardata"`
	Stitle      string                   `xml:"stitle,attr"`
	Documents   ValueAttributePdfService `xml:"Documents"`
	FIO         ValueAttributePdfService `xml:"FIO"`
	SubjectRole ValueAttributePdfService `xml:"SubjectRole"`
}

type FooterParentContractList struct {
	Text                   string                   `xml:",chardata"`
	ParentProvider         ValueAttributePdfService `xml:"ParentProvider"`
	ParentProviderBIN      ValueAttributePdfService `xml:"ParentProviderBIN"`
	ParentContractDate     ValueAttributePdfService `xml:"ParentContractDate"`
	ParentContractCode     ValueAttributePdfService `xml:"ParentContractCode"`
	ParentOperationDate    ValueAttributePdfService `xml:"ParentOperationDate"`
	ParentOperationSum     ValueAttributePdfService `xml:"ParentOperationSum"`
	ParentOperationRealSum ValueAttributePdfService `xml:"ParentOperationRealSum"`
}

type FooterCollateral struct {
	Text                   string                   `xml:",chardata"`
	TypeOfGuarantee        ValueAttributePdfService `xml:"TypeOfGuarantee"`
	ValueOfGuarantee       ValueAttributePdfService `xml:"ValueOfGuarantee"`
	TypeOfValueOfGuarantee ValueAttributePdfService `xml:"TypeOfValueOfGuarantee"`
	PlaceOfGuarantee       ValueAttributePdfService `xml:"PlaceOfGuarantee"`
	CollateralStatus       ValueAttributePdfService `xml:"CollateralStatus"`
	CollateralName         ValueAttributePdfService `xml:"CollateralName"`
	TypeOfCollateral       ValueAttributePdfService `xml:"TypeOfCollateral"`
}

// StopCreditPdfService представляет элемент <StopCredit> (Блокировка получения кредита).
type StopCreditPdfService struct {
	XMLName xml.Name `xml:"StopCredit"` // Явно указываем имя тега
	Value   bool     `xml:",chardata"`  // Ожидается 'true' или 'false'
}

// GamblersPdfService представляет элемент <Gamblers> (Признаки вовлеченности в игорный бизнес).
type GamblersPdfService struct {
	XMLName                   xml.Name                  `xml:"Gamblers"`       // Явно указываем имя тега
	GamblerFeature            bool                      `xml:"GamblerFeature"` // Содержит 'true' или 'false'
	GamblerMonthsTransactions GamblerMonthsTransactions `xml:"GamblerMonthsTransactions"`
}

type GamblerMonthsTransactions struct {
	GamblerCount string `xml:"GamblerCount"`
	GamblerSumm  string `xml:"GamblerSumm"`
}

// --- Структуры, используемые внутри секций ---

// AddressDetailPdfService представляет элемент <Address> внутри <SubjectsAddress> (Детали адреса).
type AddressDetailPdfService struct {
	XMLName         xml.Name                 `xml:"Address"`              // Явно указываем имя тега
	TitleAttr       string                   `xml:"title,attr,omitempty"` // Атрибут 'title'
	Type            string                   `xml:"type,attr,omitempty"`  // Атрибут 'type'
	AddressType     ValueAttributePdfService `xml:"AddressType"`          // Тип адреса
	Street          ValueAttributePdfService `xml:"Street"`               // Улица
	Number          ValueAttributePdfService `xml:"Number"`               // Номер дома/квартиры
	ZipCode         ValueAttributePdfService `xml:"ZipCode"`              // Почтовый индекс
	City            ValueAttributePdfService `xml:"City"`                 // Город
	Country         ValueAttributePdfService `xml:"Country"`              // Страна
	Region          ValueAttributePdfService `xml:"Region"`               // Область/район/населенный пункт
	HomePhone       ValueAttributePdfService `xml:"HomePhone"`            // Домашний телефон
	OfficePhone     ValueAttributePdfService `xml:"OfficePhone"`          // Рабочий телефон
	Fax             ValueAttributePdfService `xml:"Fax"`                  // Факс
	CellularPhone   ValueAttributePdfService `xml:"CellularPhone"`        // Сотовый телефон
	EmailAddress    ValueAttributePdfService `xml:"EmailAddress"`         // E-mail адрес
	WebPageAddress  ValueAttributePdfService `xml:"WebPageAddress"`       // Веб-сайт
	AddressInserted ValueAttributePdfService `xml:"AddressInserted"`      // Дата добавления адреса в систему
	PostBox         ValueAttributePdfService `xml:"PostBox"`              // Почтовый ящик
	AdditionalInfo  ValueAttributePdfService `xml:"AdditionalInfo"`       // Дополнительная информация
}

// DocumentDetailPdfService представляет элемент <Document> внутри <IdentificationDocuments> (Детали документа).
type DocumentDetailPdfService struct {
	XMLName            xml.Name                 `xml:"Document"`              // Явно указываем имя тега
	Rank               string                   `xml:"rank,attr,omitempty"`   // Ранг оценки
	STitle             string                   `xml:"stitle,attr,omitempty"` // Доп. заголовок
	Type               string                   `xml:"type,attr,omitempty"`   // Тип
	Name               ValueAttributePdfService `xml:"Name"`                  // Вид документа
	DateOfRegistration ValueAttributePdfService `xml:"DateOfRegistration"`    // Дата записи в систему
	DateOfIssuance     ValueAttributePdfService `xml:"DateOfIssuance"`        // Дата выдачи
	DateOfExpiration   ValueAttributePdfService `xml:"DateOfExpiration"`      // Срок действия
	Number             ValueAttributePdfService `xml:"Number"`                // Номер документа
	IssuanceLocation   ValueAttributePdfService `xml:"IssuanceLocation"`      // Кем выдан
	DateOfInserted     ValueAttributePdfService `xml:"DateOfInserted"`        // Дата получения бюро
}

// NegativeStatusPdfService представляет элемент <NegativeStatus> (группа негативных статусов).
type NegativeStatusPdfService struct {
	XMLName                  xml.Name                           `xml:"NegativeStatus"`           // Явно указываем имя тега
	Title                    string                             `xml:"title,attr,omitempty"`     // Заголовок
	Type                     string                             `xml:"type,attr,omitempty"`      // Тип (current, historical, withdrawn)
	TypeTitle                string                             `xml:"typeTitle,attr,omitempty"` // Тип заголовка
	NegativeStatusOfClient   NegativeStatusClientPdfService     `xml:"NegativeStatusOfClient"`   // Статус клиента
	NegativeStatusOfContract []NegativeStatusContractPdfService `xml:"NegativeStatusOfContract"` // Статусы контрактов
}

// ContractsSummaryPdfService представляет <ExistingContracts> или <TerminatedContracts> в сводке.
type ContractsSummaryPdfService struct {
	Title       string                         `xml:"title,attr,omitempty"` // Заголовок
	SubjectRole []SubjectRoleSummaryPdfService `xml:"SubjectRole"`          // Детализация по ролям
}

// type SubjectRoleList struct {
//	SubjectRole []SubjectRoleSummaryPdfService `xml:"SubjectRole"` // Детализация по ролям
// }

// func (s *SubjectRoleList) UnmarshalXML(d *xml.Decoder, start xml.StartElement) error {
//	// Создаём временный тип для обхода рекурсии при вызове DecodeElement
//	type alias SubjectRoleList
//	var a alias
//
//	err := d.DecodeElement(&a, &start)
//	if err == nil {
//		s.SubjectRole = a.SubjectRole
//		return nil
//	}
//
//	// Попробуем как одиночный элемент
//	var single SubjectRoleSummaryPdfService
//	if err = d.DecodeElement(&single, &start); err != nil {
//		return err
//	}
//
//	s.SubjectRole = []SubjectRoleSummaryPdfService{single}
//	return nil
// }

// NumberOfInquiriesPdfService представляет <NumberOfInquiries> в сводке.
type NumberOfInquiriesPdfService struct {
	XMLName       xml.Name                 `xml:"NumberOfInquiries"`    // Явно указываем имя тега
	Title         string                   `xml:"title,attr,omitempty"` // Заголовок
	Value         string                   `xml:"value,attr"`           // Общее количество
	FirstQuarter  QuarterInquiryPdfService `xml:"FirstQuarter"`         // 1-ый квартал
	SecondQuarter QuarterInquiryPdfService `xml:"SecondQuarter"`        // 2-ой квартал
	ThirdQuarter  QuarterInquiryPdfService `xml:"ThirdQuarter"`         // 3-ий квартал
	FourthQuarter QuarterInquiryPdfService `xml:"FourthQuarter"`        // 4-ый квартал
}

// ContractPdfService представляет <Contract> в детальных разделах.
type ContractPdfService struct {
	XMLName                             xml.Name                     `xml:"Contract"`                        // Явно указываем имя тега
	ContractType                        string                       `xml:"ContractType,attr,omitempty"`     // Тип контракта
	ContractTypeCode                    string                       `xml:"ContractTypeCode,attr,omitempty"` // Код типа
	Currency                            string                       `xml:"Currency,attr,omitempty"`         // Валюта
	CodeOfContract                      ValueAttributePdfService     `xml:"CodeOfContract"`                  // Код контракта
	AgreementNumber                     ValueAttributePdfService     `xml:"AgreementNumber"`                 // Номер договора
	AgreementNumberGuarantee            ValueAttributePdfService     `xml:"AgreementNumberGuarantee"`        // Номер договора гарантии
	TypeOfFounding                      ValueAttributePdfService     `xml:"TypeOfFounding"`                  // Вид финансирования
	PurposeOfCredit                     ValueAttributePdfService     `xml:"PurposeOfCredit"`                 // Цель кредита
	CurrencyCode                        ValueAttributePdfService     `xml:"CurrencyCode"`                    // Код валюты
	ContractStatus                      ValueAttributePdfService     `xml:"ContractStatus"`                  // Статус договора
	DateOfApplication                   ValueAttributePdfService     `xml:"DateOfApplication"`               // Дата заявки
	DateOfCreditStart                   ValueAttributePdfService     `xml:"DateOfCreditStart"`               // Дата начала
	DateOfCreditEnd                     ValueAttributePdfService     `xml:"DateOfCreditEnd"`                 // Дата окончания
	DateOfRealRepayment                 ValueAttributePdfService     `xml:"DateOfRealRepayment"`             // Дата факт. завершения
	DateAgreementGuarantee              ValueAttributePdfService     `xml:"DateAgreementGuarantee"`          // Дата договора гарантии
	GuaranteeEvent                      ValueAttributePdfService     `xml:"GuaranteeEvent"`                  // Событие прекращения
	LastUpdate                          ValueAttributePdfService     `xml:"LastUpdate"`                      // По состоянию на
	CollateralList                                                   // Обеспечение
	Settlement                          SettlementPdfService         `xml:"Settlement"`                          // Урегулирование
	ParentContractList                  ParentContractListPdfService `xml:"ParentContractList"`                  // Связанные контракты
	ClassificationOfContract            ValueAttributePdfService     `xml:"ClassificationOfContract"`            // Классификация
	TotalAmount                         ValueAttributePdfService     `xml:"TotalAmount"`                         // Общая сумма
	Amount                              ValueAttributePdfService     `xml:"Amount"`                              // Исп. сумма
	CreditUsage                         ValueAttributePdfService     `xml:"CreditUsage"`                         // Исп. кредита
	ResidualAmount                      ValueAttributePdfService     `xml:"ResidualAmount"`                      // Ост. сумма к погаш.
	CreditLimit                         ValueAttributePdfService     `xml:"CreditLimit"`                         // Кредитный лимит
	NumberOfOutstandingInstalments      ValueAttributePdfService     `xml:"NumberOfOutstandingInstalments"`      // Кол-во непогаш. платежей
	NumberOfInstalments                 ValueAttributePdfService     `xml:"NumberOfInstalments"`                 // Общее кол-во платежей
	OutstandingAmount                   ValueAttributePdfService     `xml:"OutstandingAmount"`                   // Непогашенная сумма
	PeriodicityOfPayments               ValueAttributePdfService     `xml:"PeriodicityOfPayments"`               // Периодичность
	MethodOfPayments                    ValueAttributePdfService     `xml:"MethodOfPayments"`                    // Форма расчёта
	NumberOfOverdueInstalments          ValueAttributePdfService     `xml:"NumberOfOverdueInstalments"`          // Дни просрочки (тек.)
	OverdueAmount                       ValueAttributePdfService     `xml:"OverdueAmount"`                       // Сумма просрочки (тек.)
	MonthlyInstalmentAmount             ValueAttributePdfService     `xml:"MonthlyInstalmentAmount"`             // Период. платеж
	InterestRate                        ValueAttributePdfService     `xml:"InterestRate"`                        // Проц. ставка
	SubjectRole                         ValueAttributePdfService     `xml:"SubjectRole"`                         // Роль субъекта
	FinancialInstitution                ValueAttributePdfService     `xml:"FinancialInstitution"`                // Кредитор
	FinancialInstitutionBIN             ValueAttributePdfService     `xml:"FinancialInstitutionBIN"`             // БИН кредитора
	CreditorType                        ValueAttributePdfService     `xml:"CreditorType"`                        // Тип кредитора
	SpecialRelationship                 ValueAttributePdfService     `xml:"SpecialRelationship"`                 // Связанность с банком
	AnnualEffectiveRate                 ValueAttributePdfService     `xml:"AnnualEffectiveRate"`                 // ГЭСВ
	NominalRate                         ValueAttributePdfService     `xml:"NominalRate"`                         // Номинальная ставка
	AmountProvisions                    ValueAttributePdfService     `xml:"AmountProvisions"`                    // Размер провизии
	LoanAccount                         ValueAttributePdfService     `xml:"LoanAccount"`                         // Ссудный счет
	GracePrincipal                      ValueAttributePdfService     `xml:"GracePrincipal"`                      // Льготный период (ОД)
	GracePay                            ValueAttributePdfService     `xml:"GracePay"`                            // Льготный период (%)
	PlaceOfDisbursement                 ValueAttributePdfService     `xml:"PlaceOfDisbursement"`                 // Место освоения
	PaymentsCalendar                    PaymentsCalendarPdfService   `xml:"PaymentsCalendar"`                    // Календарь платежей
	ContractThirdParty                  ValueAttributePdfService     `xml:"ContractThirdParty"`                  // Цессионарий
	ParentContractCode                  ValueAttributePdfService     `xml:"ParentContractCode"`                  // Код род. контракта
	ParentOperationDate                 ValueAttributePdfService     `xml:"ParentOperationDate"`                 // Дата род. операции
	Fine                                ValueAttributePdfService     `xml:"Fine"`                                // Пеня (тек.)
	Penalty                             ValueAttributePdfService     `xml:"Penalty"`                             // Штраф (тек.)
	BranchLocation                      ValueAttributePdfService     `xml:"BranchLocation"`                      // Филиал
	ProlongationCount                   ValueAttributePdfService     `xml:"ProlongationCount"`                   // Кол-во пролонгаций
	NumberOfTransactions                ValueAttributePdfService     `xml:"NumberOfTransactions"`                // Кол-во транзакций
	InstalmentAmount                    ValueAttributePdfService     `xml:"InstalmentAmount"`                    // Сумма период. платежа
	ActualDate                          ValueAttributePdfService     `xml:"ActualDate"`                          // Дата факт. выдачи
	DateOfInserted                      ValueAttributePdfService     `xml:"DateOfInserted"`                      // Дата получения бюро
	NumberOfOverdueInstalmentsMax       ValueAttributePdfService     `xml:"NumberOfOverdueInstalmentsMax"`       // Макс. дни проср.
	NumberOfOverdueInstalmentsMaxDate   ValueAttributePdfService     `xml:"NumberOfOverdueInstalmentsMaxDate"`   // Дата макс. дней
	NumberOfOverdueInstalmentsMaxAmount ValueAttributePdfService     `xml:"NumberOfOverdueInstalmentsMaxAmount"` // Сумма на макс. дни
	OverdueAmountMax                    ValueAttributePdfService     `xml:"OverdueAmountMax"`                    // Макс. сумма проср.
	OverdueAmountMaxDate                ValueAttributePdfService     `xml:"OverdueAmountMaxDate"`                // Дата макс. суммы
	OverdueAmountMaxCount               ValueAttributePdfService     `xml:"OverdueAmountMaxCount"`               // Дни на макс. сумму
	CreditObject                        ValueAttributePdfService     `xml:"CreditObject"`                        // Объект кредитования
	InterconnectedSubjects              InterconnectedSubjectsInfo   `xml:"InterconnectedSubjects"`              // Связанные объекты
	FundingSource                       ValueAttributePdfService     `xml:"FundingSource"`                       // Источник финансирования
	Schema                              ValueAttributePdfService     `xml:"Schema"`                              // Схема
	LastPaymentDate                     ValueAttributePdfService     `xml:"LastPaymentDate"`                     // Дата посл. платежа
	LastPaymentSum                      ValueAttributePdfService     `xml:"LastPaymentSum"`                      // Сумма посл. платежа
	ThirdPartyHolderBin                 ValueAttributePdfService     `xml:"ThirdPartyHolderBin"`                 // БИН цессионария
	ThirdPartyHolderSum                 ValueAttributePdfService     `xml:"ThirdPartyHolderSum"`                 // Сумма уступки
	ThirdPartyHolderDate                ValueAttributePdfService     `xml:"ThirdPartyHolderDate"`                // Дата уступки
	CreditLineRelations                 CreditLineRelations          `xml:"CreditLineRelations"`
	AvailableDate                       ValueAttributePdfService     `xml:"AvailableDate"`
	AvailableLimit                      ValueAttributePdfService     `xml:"AvailableLimit"`
	Prolongation                        Prolongation                 `xml:"Prolongation"`
	Rehabilitation                      Rehabilitation               `xml:"Rehabilitation"`
	ContractsPhase                      ValueAttributePdfService     `xml:"ContractPhase"`
}

type InterconnectedSubjectsInfo struct {
	Text  string `xml:",chardata"`
	Value string `xml:"value,attr"`
	Name  string `xml:"name,attr"`
}

type Rehabilitation struct {
	Text                 string                   `xml:",chardata"`
	StatusRehabilitation ValueAttributePdfService `xml:"StatusRehabilitation"`
	DateReabilitate      ValueAttributePdfService `xml:"DateReabilitate"`
	CalcDate             ValueAttributePdfService `xml:"CalcDate"`
}

type Prolongation struct {
	Text                  string                   `xml:",chardata"`
	ProlongationStartDate ValueAttributePdfService `xml:"ProlongationStartDate"`
	ProlongationEndDate   ValueAttributePdfService `xml:"ProlongationEndDate"`
}

type Tranches struct {
	Text           string                   `xml:",chardata"`
	Title          string                   `xml:"title,attr"`
	PrimaryGroup   ValueAttributePdfService `xml:"PrimaryGroup"`
	SecondaryGroup ValueAttributePdfService `xml:"SecondaryGroup"`
}

type CreditLineRelations struct {
	Text               string   `xml:",chardata"`
	ExistingTranches   Tranches `xml:"ExistingTranches"`
	TerminatedTranches Tranches `xml:"TerminatedTranches"`
	WithdrawnTranches  Tranches `xml:"WithdrawnTranches"`
}

// InterconnectedSubjectDetailPdfService представляет <InterconnectedSubject> в основном разделе.
type InterconnectedSubjectDetailPdfService struct {
	XMLName     xml.Name                 `xml:"InterconnectedSubject"` // Явно указываем имя тега
	TypeOfLink  ValueAttributePdfService `xml:"TypeOfLink"`            // Роль
	SubjectCode ValueAttributePdfService `xml:"SubjectCode"`           // ID
	SubjectName ValueAttributePdfService `xml:"SubjectName"`           // Название
	SubjectRNN  ValueAttributePdfService `xml:"SubjectRNN"`            // РНН
	SubjectIIN  ValueAttributePdfService `xml:"SubjectIIN"`            // ИИН
}

// QueriesPeriodPdfService представляет периоды <Days30>, <Days90>...
type QueriesPeriodPdfService struct {
	Title string `xml:"title,attr,omitempty"` // Название периода
	Value string `xml:"value,attr"`           // Количество запросов
}

// PublicSourceDetailPdfService представляет элемент источника <QamqorList>...
type PublicSourceDetailPdfService struct {
	Code        string                       `xml:"code,attr,omitempty"`  // Код источника
	Title       string                       `xml:"title,attr,omitempty"` // Название источника
	Source      ValueAttributePdfService     `xml:"Source"`               // Ссылка
	ActualDate  ValueAttributePdfService     `xml:"ActualDate"`           // Дата бюро
	RefreshDate ValueAttributePdfService     `xml:"RefreshDate"`          // Дата источника
	Status      PublicSourceStatusPdfService `xml:"Status"`               // Статус
	Companies   ValueAttributePdfService     `xml:"Companies"`            // Компании
}

type Ksk struct {
	PublicSourceDetailPdfService
	SubjectSource SubjectKSKSource `xml:"Subject"` // Субъект источника
}

type SubjectKSKSource struct {
	XMLName     xml.Name                 `xml:"Subject"`
	Source      ValueAttributePdfService `xml:"Source"`
	Iin         ValueAttributePdfService `xml:"Iin"`
	Region      ValueAttributePdfService `xml:"region"`
	Address     ValueAttributePdfService `xml:"address"`
	Account     ValueAttributePdfService `xml:"account"`
	Name        ValueAttributePdfService `xml:"Name"`
	OverdueDays ValueAttributePdfService `xml:"OverdueDays"`
	RefreshDate ValueAttributePdfService `xml:"RefreshDate"`
	Service     KskService               `xml:"Service"`
}

type KskService struct {
	ServiceName ValueAttributePdfService `xml:"ServiceName"`
	ServiceFine ValueAttributePdfService `xml:"ServiceFine"`
}

// RWAContractsPdfService представляет элемент <Contracts> внутри <RWA170>.
type RWAContractsPdfService struct {
	XMLName       xml.Name                      `xml:"Contracts"`                    // Явно указываем имя тега
	ContractsSumm string                        `xml:"ContractsSumm,attr,omitempty"` // Общая сумма задолженности
	Title         string                        `xml:"title,attr,omitempty"`         // Заголовок
	Contract      []RWAContractDetailPdfService `xml:"Contract"`                     // Список контрактов (тег <Contract>)
}

// --- Структуры, используемые еще глубже ---

// NegativeStatusClientPdfService представляет обертку для <NegativeStatusOfClient>.
type NegativeStatusClientPdfService struct {
	XMLName          xml.Name                       `xml:"NegativeStatusOfClient"` // Явно указываем имя тега
	StatusDetail     NegativeStatusDetailPdfService `xml:"NegativeStatusOfClient"` // Детали статуса
	RegistrationDate RegistrationDatePdfService     `xml:"RegistrationDate"`       // Дата регистрации
}

// NegativeStatusContractPdfService представляет обертку для <NegativeStatusOfContract>.
type NegativeStatusContractPdfService struct {
	XMLName          xml.Name                       `xml:"NegativeStatusOfContract"` // Явно указываем имя тега
	StatusDetail     NegativeStatusDetailPdfService `xml:"NegativeStatusOfContract"` // Детали статуса
	RegistrationDate RegistrationDatePdfService     `xml:"RegistrationDate"`         // Дата регистрации
	SubjectRole      SubjectRolePdfService          `xml:"SubjectRole"`              // Роль субъекта
	SubjectRoleEn    SubjectRolePdfServiceEn        `xml:"SubjectRoleEn"`            // Роль субъекта (англ.)
}

// SubjectRoleSummaryPdfService представляет <SubjectRole> в сводке.
type SubjectRoleSummaryPdfService struct {
	XMLName              xml.Name                   `xml:"SubjectRole"`          // Явно указываем имя тега
	ID                   string                     `xml:"id,attr,omitempty"`    // ID роли
	Title                string                     `xml:"title,attr,omitempty"` // Название роли
	Type                 string                     `xml:"type,attr,omitempty"`  // Тип роли
	NumberOfContracts    ValueAttributePdfService   `xml:"NumberOfContracts"`    // Количество договоров
	NumberOfCreditLines  ValueAttributePdfService   `xml:"NumberOfCreditLines"`  // Количество кредитных линий
	TotalOutstandingDebt ValueAttributePdfService   `xml:"TotalOutstandingDebt"` // Общая непогашенная сумма
	TotalDebtOverdue     ValueAttributePdfService   `xml:"TotalDebtOverdue"`     // Общая сумма просроченных взносов
	TotalFine            ValueAttributePdfService   `xml:"TotalFine"`            // Сумма пеней
	TotalPenalty         ValueAttributePdfService   `xml:"TotalPenalty"`         // Сумма штрафов
	ContractStatuses     ContractStatusesPdfService `xml:"ContractStatuses"`     // Распределение по статусам
}

// QuarterInquiryPdfService представляет кварталы <FirstQuarter>...
type QuarterInquiryPdfService struct {
	Text  string `xml:",chardata"`
	Title string `xml:"title,attr,omitempty"` // Название квартала
	Value string `xml:"value,attr"`           // Количество запросов
}

// CollateralPdfService представляет <Collateral> в контракте.
type CollateralPdfService struct {
	XMLName                xml.Name                 `xml:"Collateral"`             // Явно указываем имя тега
	TypeOfGuarantee        ValueAttributePdfService `xml:"TypeOfGuarantee"`        // Вид обеспечения
	ValueOfGuarantee       ValueAttributePdfService `xml:"ValueOfGuarantee"`       // Стоимость обеспечения
	TypeOfValueOfGuarantee ValueAttributePdfService `xml:"TypeOfValueOfGuarantee"` // Вид стоимости
	AdditionalInformation  ValueAttributePdfService `xml:"AdditionalInformation"`  // Доп. инфо
	PlaceOfGuarantee       ValueAttributePdfService `xml:"PlaceOfGuarantee"`       // Местоположение
	TypeOfCollateral       ValueAttributePdfService `xml:"TypeOfCollateral"`       // Вид залога
	CollateralStatus       ValueAttributePdfService `xml:"CollateralStatus"`       // Статус залога
	CollateralName         ValueAttributePdfService `xml:"CollateralName"`         // Наименование залога
}

type CollateralList struct {
	Collateral []CollateralPdfService // Обеспечение
}

// SettlementPdfService представляет <Settlement> в контракте.
type SettlementPdfService struct {
	XMLName                     xml.Name                 `xml:"Settlement"`                  // Явно указываем имя тега
	SettlementApplicationDate   ValueAttributePdfService `xml:"SettlementApplicationDate"`   // Дата заявления
	SettlementResult            ValueAttributePdfService `xml:"SettlementResult"`            // Результат
	SettlementDecisionDate      ValueAttributePdfService `xml:"SettlementDecisionDate"`      // Дата ответа
	SettlementNewContractType   ValueAttributePdfService `xml:"SettlementNewContractType"`   // Тип док-та
	SettlementNewContractNumber ValueAttributePdfService `xml:"SettlementNewContractNumber"` // Номер док-та
	SettlementNewContractDate   ValueAttributePdfService `xml:"SettlementNewContractDate"`   // Дата док-та
	DebtCollection              ValueAttributePdfService `xml:"DebtCollection"`              // Инфо о взыскании
	DebtCollectionDate          ValueAttributePdfService `xml:"DebtCollectionDate"`          // Дата решения
	DebtCollectionEndDate       ValueAttributePdfService `xml:"DebtCollectionEndDate"`       // Дата отмены
	SettlementType              ValueAttributePdfService `xml:"SettlementType"`              // Программа
}

// ParentContractListPdfService представляет <ParentContractList> в контракте.
type ParentContractListPdfService struct {
	XMLName         xml.Name                         `xml:"ParentContractList"`   // Явно указываем имя тега
	Title           string                           `xml:"title,attr,omitempty"` // Заголовок
	ParentContracts []ParentContractDetailPdfService `xml:"Contract"`             // Детали родительских контрактов
}

// PaymentsCalendarPdfService представляет <PaymentsCalendar> в контракте.
type PaymentsCalendarPdfService struct {
	XMLName xml.Name                `xml:"PaymentsCalendar"` // Явно указываем имя тега
	Years   []YearPaymentPdfService `xml:"Year"`             // Список годов
}

// PublicSourceStatusPdfService представляет <Status> в публичном источнике.
type PublicSourceStatusPdfService struct {
	XMLName xml.Name `xml:"Status"`               // Явно указываем имя тега
	ID      string   `xml:"id,attr,omitempty"`    // ID статуса
	Title   string   `xml:"title,attr,omitempty"` // Название
	Value   string   `xml:"value,attr"`           // Значение
}

// RWAContractDetailPdfService представляет <Contract> внутри <RWA170>.
type RWAContractDetailPdfService struct {
	XMLName      xml.Name                 `xml:"Contract"`     // Явно указываем имя тега
	ContractCode ValueAttributePdfService `xml:"ContractCode"` // Код контракта
	ContractSumm ValueAttributePdfService `xml:"ContractSumm"` // Сумма задолженности
}

// --- Самые вложенные и базовые структуры ---

// NegativeStatusDetailPdfService используется внутри NegativeStatusClient/Contract.
type NegativeStatusDetailPdfService struct {
	ID            string `xml:"id,attr,omitempty"`            // ID статуса
	Title         string `xml:"title,attr,omitempty"`         // Название статуса
	Value         string `xml:"value,attr"`                   // Значение статуса
	SubjectRoleEn string `xml:"SubjectRoleEn,attr,omitempty"` // Роль на англ.
}

// RegistrationDatePdfService используется внутри NegativeStatusClient/Contract.
type RegistrationDatePdfService struct {
	XMLName xml.Name `xml:"RegistrationDate"`     // Явно указываем имя тега
	Title   string   `xml:"title,attr,omitempty"` // Название поля
	Value   string   `xml:"value,attr"`           // Значение даты
}

// SubjectRolePdfService используется внутри NegativeStatusContract.
type SubjectRolePdfService struct {
	XMLName xml.Name `xml:"SubjectRole"`          // Явно указываем имя тега
	ID      string   `xml:"id,attr,omitempty"`    // ID роли
	Title   string   `xml:"title,attr,omitempty"` // Название роли
	Value   string   `xml:"value,attr"`           // Значение роли
}

type SubjectRolePdfServiceEn struct {
	XMLName xml.Name `xml:"SubjectRoleEn"`        // Явно указываем имя тега
	ID      string   `xml:"id,attr,omitempty"`    // ID роли
	Title   string   `xml:"title,attr,omitempty"` // Название роли
	Value   string   `xml:"value,attr"`           // Значение роли
}

// ContractStatusesPdfService используется внутри SubjectRoleSummary.
type ContractStatusesPdfService struct {
	XMLName        xml.Name           `xml:"ContractStatuses"` // Явно указываем имя тега
	ContractStatus ContractStatusList `xml:"ContractStatus"`   // Список статусов
}

type ContractStatusList struct {
	ContractStatus []ValueAttributePdfService `xml:"ContractStatus"`
}

func (s *ContractStatusList) UnmarshalXML(d *xml.Decoder, start xml.StartElement) error {
	type alias ContractStatusList
	var a alias

	err := d.DecodeElement(&a, &start)
	if err == nil {
		s.ContractStatus = a.ContractStatus
		return nil
	}

	// Попробуем как одиночный элемент
	var single ValueAttributePdfService
	if err = d.DecodeElement(&single, &start); err != nil {
		return err
	}

	s.ContractStatus = []ValueAttributePdfService{single}
	return nil
}

// ParentContractDetailPdfService используется внутри ParentContractList.
type ParentContractDetailPdfService struct {
	XMLName                xml.Name                 `xml:"Contract"`               // Явно указываем имя тега (вложенный Contract)
	ProviderName           ValueAttributePdfService `xml:"ProviderName"`           // Поставщик
	ProviderBin            ValueAttributePdfService `xml:"ProviderBin"`            // БИН поставщика
	ContractDate           ValueAttributePdfService `xml:"ContractDate"`           // Дата контракта
	ContractCode           ValueAttributePdfService `xml:"ContractCode"`           // Код контракта
	OperationDate          ValueAttributePdfService `xml:"OperationDate"`          // Дата уступки
	OperationSum           ValueAttributePdfService `xml:"OperationSum"`           // Сумма уступки
	ParentOperationRealSum ValueAttributePdfService `xml:"ParentOperationRealSum"` // Факт. стоимость
}

// YearPaymentPdfService используется внутри PaymentsCalendar.
type YearPaymentPdfService struct {
	XMLName  xml.Name                  `xml:"Year"`                 // Явно указываем имя тега
	Title    string                    `xml:"title,attr,omitempty"` // Год
	Payments []PaymentDetailPdfService `xml:"Payment"`              // Платежи по месяцам
}

// ContractStatusSummaryPdfService используется внутри ContractStatuses.
type ContractStatusSummaryPdfService struct {
	XMLName xml.Name `xml:"ContractStatus"`       // Явно указываем имя тега
	Count   string   `xml:"count,attr,omitempty"` // Количество
	Value   string   `xml:"value,attr"`           // Название статуса
}

// PaymentDetailPdfService используется внутри YearPayment.
type PaymentDetailPdfService struct {
	XMLName xml.Name `xml:"Payment"`                // Явно указываем имя тега
	Fine    string   `xml:"fine,attr,omitempty"`    // Пеня
	Number  string   `xml:"number,attr,omitempty"`  // Номер месяца
	Overdue string   `xml:"overdue,attr,omitempty"` // Сумма просрочки
	Penalty string   `xml:"penalty,attr,omitempty"` // Штраф
	Title   string   `xml:"title,attr,omitempty"`   // Название месяца
	Value   string   `xml:"value,attr"`             // Дни просрочки/статус
}

// --- Базовые универсальные структуры ---

// ValueAttributePdfService используется повсеместно.
type ValueAttributePdfService struct {
	ID     string `xml:"id,attr,omitempty"`     // Атрибут 'id' (идентификатор) - fi_unique_number_id
	Title  string `xml:"title,attr,omitempty"`  // Атрибут 'title' (название поля)
	STitle string `xml:"stitle,attr,omitempty"` // Атрибут 'stitle' (дополнительное название поля)
	Value  string `xml:"value,attr"`            // Атрибут 'value' (значение поля)
	Number string `xml:"number,attr,omitempty"` // Атрибут 'number' (например, в SubjectRole внутри Contract) - fi_unique_numberid_id
	Count  string `xml:"count,attr,omitempty"`  // Атрибут 'count' (например, в ContractStatus внутри Summary)
}

// TextValuePdfService используется для простых текстовых элементов.
type TextValuePdfService struct {
	Value string `xml:",chardata"` // Текстовое содержимое элемента
}

// EmptyElementWithTitlePdfService используется для пустых элементов с title.
type EmptyElementWithTitlePdfService struct {
	Title string `xml:"title,attr,omitempty"` // Атрибут 'title'
}
