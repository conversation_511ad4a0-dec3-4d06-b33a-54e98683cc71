package entity

import "encoding/json"

type ReportRequest struct {
	RequestID string `json:"request_id"`
	Callback  string `json:"callback,omitempty"`
	Preset    string `json:"preset"`
	Fields    Fields `json:"fields"`
}

type Fields struct {
	IIN  string `json:"iin"`
	Name string `json:"name,omitempty"`
}

type CreateReportResponse struct {
	Status          string `json:"status"`
	ReportID        string `json:"report_id"`
	ReadyPercentage int    `json:"ready_percentage"`
}

func (c *CreateReportResponse) UnmarshalJSON(data []byte) error {
	var raw struct {
		Status          string  `json:"status"`
		ReportID        string  `json:"report_id"`
		ReadyPercentage float64 `json:"ready_percentage"`
	}
	if err := json.Unmarshal(data, &raw); err != nil {
		return err
	}

	c.Status = raw.Status
	c.ReportID = raw.ReportID
	c.ReadyPercentage = int(raw.ReadyPercentage)

	return nil
}

type ErrResponse struct {
	Message string `json:"message"`
}

type ErrWithCodeResponse struct {
	Code    string `json:"code"`
	Message string `json:"message"`
}

type ErrWithResultDescription struct {
	Result      int64  `json:"result"`
	Description string `json:"result_descr"`
}

type ReportResponse struct {
	Sources  []Source `json:"sources"`
	ReportID string   `json:"reportID"`
}

type Source struct {
	Name        Name   `json:"name"`
	Status      int    `json:"status"` // Статус субъекта. Принимает значения: 1 – найден, 2 – не найден, 3 – не доступен, 4 – недостаточно информации (когда по заданному параметру не производится поиск в каком-то источнике)
	Code        string `json:"code"`
	URL         string `json:"url"`
	DateUpdated string `json:"date_updated"`
	DateActual  string `json:"date_actual"`
	Infos       []Info `json:"infos,omitempty"`
}

type Info struct {
	DetailsKK []Details `json:"kk,omitempty"`
	DetailsRU []Details `json:"ru,omitempty"`
}

type Details struct {
	Title string `json:"title,omitempty"`
	Value string `json:"value,omitempty"`
}

type Name struct {
	Ru string `json:"ru"`
	Kk string `json:"kk"`
}
