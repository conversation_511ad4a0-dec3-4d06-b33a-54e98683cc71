package entity

type IINData struct {
	IIN string `json:"iin"`
}

type FamilyInfoRequest struct {
	IINData IINData `json:"iin_data"`
}

type FamilyInfoResponse struct {
	Response Response `json:"response"`
	Message  string   `json:"message"`
}

// Информация о самом ответе
type Response struct {
	ResponseInfo ResponseInfo `json:"ResponseInfo"`
	ResponseData ResponseData `json:"ResponseData"`
}

// Метаданные ответа
type ResponseInfo struct {
	ResponseDate string `json:"ResponseDate"`
	Status       Status `json:"Status"`
}

// Статус ответа
type Status struct {
	Code    string `json:"Status"`
	Message string `json:"Message"`
}

// Основные данные
type ResponseData struct {
	Data FamilyInfoData `json:"Data"`
}

// Данные о семье
type FamilyInfoData struct {
	MessageDate    string       `json:"MessageDate"`
	RequestID      string       `json:"ColvirRequestID"`
	Receiver       EntityInfo   `json:"Receiver"`
	Sender         EntityInfo   `json:"Sender"`
	MessageResult  EntityInfo   `json:"MessageResult"`
	FamilyInfoList []FamilyInfo `json:"FamilyInfoList"`
}

// Информация об отправителе/получателе
type EntityInfo struct {
	Code   string `json:"Status"`
	NameRu string `json:"NameRU"`
	NameKz string `json:"NameKZ"`
}

// Общая структура семейной информации (может быть о браке или рождении)
type FamilyInfo struct {
	MarriageInfos *MarriageInfo `json:"MarriageInfos,omitempty"`
	BirthInfos    *BirthInfo    `json:"BirthInfos,omitempty"`
}

// Информация о браке
type MarriageInfo struct {
	ActDate              string       `json:"ActDate"`
	ActNumber            string       `json:"ActNumber"`
	HusbandBirthDate     string       `json:"HusbandBirthDate"`
	HusbandIIN           string       `json:"HusbandIIN"`
	HusbandLifeStatus    string       `json:"HusbandLifeStatus"`
	HusbandName          string       `json:"HusbandName"`
	HusbandPatronymic    string       `json:"HusbandPatronymic,omitempty"`
	HusbandSurNameAfter  string       `json:"HusbandSurNameAfter"`
	HusbandSurNameBefore string       `json:"HusbandSurNameBefore"`
	WifeBirthDate        string       `json:"WifeBirthDate"`
	WifeIIN              string       `json:"WifeIIN"`
	WifeLifeStatus       string       `json:"WifeLifeStatus"`
	WifeName             string       `json:"WifeName"`
	WifeSurNameAfter     string       `json:"WifeSurNameAfter"`
	WifeSurNameBefore    string       `json:"WifeSurNameBefore"`
	ZagsCode             string       `json:"ZagsCode"`
	ZagsNameKZ           string       `json:"ZagsNameKZ"`
	ZagsNameRU           string       `json:"ZagsNameRU"`
	DivorceAR            *DivorceInfo `json:"DivorceAR,omitempty"`
}

// Информация о разводе
type DivorceInfo struct {
	DivorceActNumber string     `json:"DivorceActNumber"`
	DivorceAktDate   string     `json:"DivorceAktDate"`
	DivorceAktZags   EntityInfo `json:"DivorceAktZags"`
}

// Информация о рождении
type BirthInfo struct {
	ActDate           string `json:"ActDate"`
	ActNumber         string `json:"ActNumber"`
	ChildBirthDate    string `json:"ChildBirthDate"`
	ChildIIN          string `json:"ChildIIN"`
	ChildLifeStatus   string `json:"ChildLifeStatus"`
	ChildName         string `json:"ChildName"`
	ChildPatronymic   string `json:"ChildPatronymic,omitempty"`
	ChildSurName      string `json:"ChildSurName"`
	FatherBirthDate   string `json:"FatherBirthDate"`
	FatherIIN         string `json:"FatherIIN"`
	FatherLifeStatus  string `json:"FatherLifeStatus"`
	FatherName        string `json:"FatherName"`
	FatherPatronymic  string `json:"FatherPatronymic,omitempty"`
	FatherSurName     string `json:"FatherSurName"`
	MarriageActDate   string `json:"MarriageActDate"`
	MarriageActNumber string `json:"MarriageActNumber"`
	MarriageActPlace  string `json:"MarriageActPlace"`
	MotherApplication string `json:"MotherApplication"`
	MotherBirthDate   string `json:"MotherBirthDate"`
	MotherIIN         string `json:"MotherIIN"`
	MotherLifeStatus  string `json:"MotherLifeStatus"`
	MotherName        string `json:"MotherName"`
	MotherSurName     string `json:"MotherSurName"`
	ZagsCode          string `json:"ZagsCode"`
	ZagsNameKZ        string `json:"ZagsNameKZ"`
	ZagsNameRU        string `json:"ZagsNameRU"`
}
