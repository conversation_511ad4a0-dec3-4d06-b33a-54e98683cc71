package entity

import "time"

type GetSoHoEntrepreneurResult struct {
	Results *GetSoHoEntrepreneurResponse
	Error   ErrWithResultDescription
}

type GetSoHoEntrepreneurResponse struct {
	ID          int64     `json:"id"`
	RiskClass   string    `json:"risk_class"`
	Ball        int       `json:"ball"`
	DefaultRate string    `json:"default_rate"`
	IsF<PERSON>zen    bool      `json:"is_Frozen"`
	QueryDate   time.Time `json:"query_date"`
	LastName    string    `json:"last_name"`
	MiddleName  string    `json:"middle_name"`
	FirstName   string    `json:"first_name"`
	BadRate     string    `json:"bad_rate"`
	Timestamp   time.Time `json:"timestamp"`
	Iin         string    `json:"iin"`
	IsEnt       string    `json:"is_ent"`
}
