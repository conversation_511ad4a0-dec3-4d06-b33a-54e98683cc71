package entity

import (
	"encoding/json"
	"fmt"
	"time"
)

type ParamsSendLoanApplicationInfo struct {
	Iin              string  `json:"iin"`
	PhoneNumber      string  `json:"phone_number"`
	Sum              float64 `json:"sum"`
	LoanPurpose      int32   `json:"loan_purpose"`
	LoanObject       int32   `json:"loan_object"`
	Status           int32   `json:"status"`
	ConsentConfirmed bool    `json:"consent_confirmed"`
	RepType          int32   `json:"rep_type"`
	RequestID        string  `json:"-"`
}

type SendLoanApplicationInfoResp struct {
	ID                      string    `json:"id"`
	Iin                     string    `json:"iin"`
	LoanApplicationDate     time.Time `json:"loan_app_date"`
	LoanApplicationDateUnix int64     `json:"loan_app_date_unix"`
	CurrentDate             time.Time `json:"current_date"`
	CurrentDateUnix         int64     `json:"current_date_unix"`
}

func (r *SendLoanApplicationInfoResp) UnmarshalJSON(data []byte) error {
	var raw struct {
		ID                      string `json:"id"`
		Iin                     string `json:"iin"`
		LoanApplicationDate     string `json:"loan_app_date"`
		LoanApplicationDateUnix int64  `json:"loan_app_date_unix"`
		CurrentDate             string `json:"current_date"`
		CurrentDateUnix         int64  `json:"current_date_unix"`
	}
	if err := json.Unmarshal(data, &raw); err != nil {
		return err
	}

	layout := "2006-01-02 15:04:05-07"
	loanAppDate, err := time.Parse(layout, raw.LoanApplicationDate)
	if err != nil {
		return fmt.Errorf("parsing loan_app_date: %w", err)
	}

	currentDate, err := time.Parse(layout, raw.CurrentDate)
	if err != nil {
		return fmt.Errorf("parsing current_date: %w", err)
	}

	r.ID = raw.ID
	r.Iin = raw.Iin
	r.LoanApplicationDate = loanAppDate
	r.LoanApplicationDateUnix = raw.LoanApplicationDateUnix
	r.CurrentDate = currentDate
	r.CurrentDateUnix = raw.CurrentDateUnix

	return nil
}
