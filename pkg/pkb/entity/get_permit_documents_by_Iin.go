package entity

type GetPermitDocumentsByIinRequest struct {
	Iin  string `json:"uin"`
	Page int    `json:"page"`
	Size int    `json:"size"`
}

type GetPermitDocumentsByIinResponse struct {
	Code    string     `json:"code"`
	Message string     `json:"message"`
	Data    PermitData `json:"data"`
}

type PermitData struct {
	Response PermitResponse `json:"response"`
}

type PermitResponse struct {
	ResponseData PermitResponseData `json:"responseData"`
}

type PermitResponseData struct {
	Data PermitDetails `json:"data"`
}

type PermitDetails struct {
	ReqNum     string                       `json:"reqNum"`
	Licenses   PermitResponseDataLicenses   `json:"licenses"`
	Request    PermitResponseDataRequest    `json:"request"`
	SystemInfo PermitResponseDataSystemInfo `json:"systemInfo"`
}

type PermitResponseDataLicenses struct {
	TaxpayerLicense []TaxpayerLicense `json:"taxpayerLicense"`
}

type TaxpayerLicense struct {
	GlobalUniqueNumber  string       `json:"globalUniqueNumber,omitempty"`
	ActivityType        ActivityType `json:"activityType"`
	Licensiar           Licensiar    `json:"licensiar"`
	ValidityStartDate   string       `json:"validityStartDate,omitempty"`
	ValidityEndDate     string       `json:"validityEndDate,omitempty"`
	ModifiedDocumentURL string       `json:"modifiedDocumentUrl"`
	DocumentID          string       `json:"documentId"`
}

type ActivityType struct {
	Code   string `json:"code"`
	NameRu string `json:"nameRu"`
	NameKz string `json:"nameKz"`
	NameEn string `json:"nameEn,omitempty"`
}

type Licensiar struct {
	Code   string `json:"code"`
	NameRu string `json:"nameRu"`
	NameKz string `json:"nameKz"`
	NameEn string `json:"nameEn,omitempty"`
}

type PermitResponseDataRequest struct {
	IinBin    string `json:"iinBin"`
	PageIndex int    `json:"pageIndex"`
	PageSize  int    `json:"pageSize"`
}

type PermitResponseDataSystemInfo struct {
	MessageID     string `json:"messageId"`
	MessageDate   string `json:"messageDate"`
	MessageType   string `json:"messageType"`
	ResponseCode  string `json:"responseCode"`
	SenderID      string `json:"senderId"`
	SignatureType string `json:"signatureType"`
}
