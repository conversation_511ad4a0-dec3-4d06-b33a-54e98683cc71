package entity

type GetOrganizationInfoResponse struct {
	Code    string           `json:"code"`
	Message string           `json:"message"`
	Data    OrganizationData `json:"data,omitempty"`
}

type OrganizationData struct {
	ID              int                  `json:"id,omitempty"`
	Status          NameCodeOrganization `json:"status,omitempty"`
	Ips             Ips                  `json:"ips,omitempty"`
	Organization    Organization         `json:"organization,omitempty"`
	Activity        Activity             `json:"activity,omitempty"`
	Address         AddressOrganization  `json:"address,omitempty"`
	FoundersUL      FoundersUL           `json:"founders_ul,omitempty"`
	FoundersFL      []FounderFL          `json:"founders_fl,omitempty"`
	FoundersCount   int                  `json:"founders_count,omitempty"`
	FoundersCountUL int                  `json:"founders_count_ul,omitempty"`
	FoundersCountFL int                  `json:"founders_count_fl,omitempty"`
	Benefiziars     *Benefiziar          `json:"benefiziars,omitempty"`
	StatInfo        StatInfo             `json:"stat_info,omitempty"`
}

type StatusOrganization struct {
	Code string `json:"code,omitempty"`
	Ru   string `json:"ru,omitempty"`
	Kk   string `json:"kk,omitempty"`
}

type Ips struct {
	Organization Organization `json:"organization,omitempty"`
}

type Organization struct {
	CountryFac      bool                 `json:"CountryFac,omitempty"`
	BIN             string               `json:"bin,omitempty"`
	RegStatus       NameCodeOrganization `json:"reg_status,omitempty"`
	RegDept         NameCodeOrganization `json:"registration_department,omitempty"`
	RegDate         string               `json:"registration_date,omitempty"`
	LastRegDate     string               `json:"registration_last_date,omitempty"`
	LiquidationDate string               `json:"liquidation_date,omitempty"`
	NameRu          string               `json:"name_ru,omitempty"`
	NameKk          string               `json:"name_kk,omitempty"`
	NameEn          string               `json:"name_en,omitempty"`
	ShortRu         string               `json:"short_name_ru,omitempty"`
	ShortKk         string               `json:"short_name_kk,omitempty"`
	ShortEn         string               `json:"short_name_en,omitempty"`
	OrgForm         NameCodeOrganization `json:"org_form,omitempty"`
	LawForm         NameCodeOrganization `json:"law_form,omitempty"`
	PEType          NameCodeOrganization `json:"private_enterprise_type,omitempty"`
	TaxStatus       bool                 `json:"tax_org_status,omitempty"`
	Liquidation     Liquidation          `json:"liquidation,omitempty"`
	Additional      AdditionalInfo       `json:"additional_info,omitempty"`
	Leader          OrganizationLeader   `json:"organization_leader,omitempty"`
	IssueDate       string               `json:"issue_date,omitempty"`
}

type AdditionalInfo struct {
	Creation          NameCodeOrganization `json:"creation_method,omitempty"`
	Property          NameCodeOrganization `json:"property_type,omitempty"`
	TypicalCharter    bool                 `json:"typical_charter,omitempty"`
	CommerceOrg       bool                 `json:"commerce_org,omitempty"`
	EnterpriseSubject bool                 `json:"enterprise_subject,omitempty"`
	ForeignInvest     bool                 `json:"foreign_invest,omitempty"`
	Branches          bool                 `json:"branches_existence,omitempty"`
}

type OrganizationLeader struct {
	IIN      string   `json:"iin,omitempty"`
	Position Position `json:"position,omitempty"`
}

type Position struct {
	PositionType NameCodeOrganization `json:"position_type,omitempty"`
}

type Activity struct {
	Main bool   `json:"main,omitempty"`
	Oked string `json:"oked,omitempty"`
	Kk   string `json:"activity_kk,omitempty"`
	Ru   string `json:"activity_ru,omitempty"`
}

type AddressOrganization struct {
	RKA         string                `json:"rka,omitempty"`
	ZipCode     string                `json:"zip_code,omitempty"`
	Kato        string                `json:"kato,omitempty"`
	ATE         string                `json:"ATE,omitempty"`
	GeonimCode  string                `json:"geonim_code,omitempty"`
	DistrictRu  string                `json:"district_ru,omitempty"`
	DistrictKk  string                `json:"district_kk,omitempty"`
	RegionRu    string                `json:"region_ru,omitempty"`
	RegionKk    string                `json:"region_kk,omitempty"`
	CityRu      string                `json:"city_ru,omitempty"`
	CityKk      string                `json:"city_kk,omitempty"`
	StreetRu    string                `json:"street_ru,omitempty"`
	StreetKk    string                `json:"street_kk,omitempty"`
	Building    *NameCodeOrganization `json:"building_type,omitempty"`
	BuildingNum string                `json:"building_number,omitempty"`
}

type FoundersUL struct {
	BIN string `json:"bin,omitempty"`
}

type FounderFL struct {
	IIN string `json:"iin,omitempty"`
}

type Benefiziar struct {
	Birthday string `json:"Birthday,omitempty"`
}

type StatInfo struct {
	OrgSize    NameCodeOrganization `json:"org_size,omitempty"`
	Attr       NameCodeOrganization `json:"activity_attr,omitempty"`
	Activity   ActivityStatInfo     `json:"activity,omitempty"`
	ActualDate string               `json:"actual_date,omitempty"`
}

type ActivityStatInfo struct {
	Main bool   `json:"main,omitempty"`
	Oked string `json:"oked,omitempty"`
	Kk   string `json:"kk,omitempty"`
	Ru   string `json:"ru,omitempty"`
}

type Liquidation struct {
	LiqBase         LiqStruct `json:"liqBase,omitempty"`
	LiqType         LiqStruct `json:"liqType,omitempty"`
	LiqDecisionDate string    `json:"liqDecisionDate,omitempty"`
}

type LiqStruct struct {
	Code string `json:"code,omitempty"`
	Ru   string `json:"nameRu,omitempty"`
	Kk   string `json:"nameKz,omitempty"`
}

// Универсальные структуры для кодов и языков
type NameCodeOrganization struct {
	Code string `json:"code,omitempty"`
	Ru   string `json:"ru,omitempty"`
	Kk   string `json:"kk,omitempty"`
}
