package pkb

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"

	"git.redmadrobot.com/zaman/backend/zaman/pkg/pkb/entity"

	"github.com/google/uuid"
)

// Создание отчета Xdata
func (c *Client) CreateReport(ctx context.Context, req entity.ReportRequest) (*entity.CreateReportResponse, error) {
	token, err := c.GetValidToken(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get valid token: %w", err)
	}

	url := fmt.Sprintf("%s%s", c.BaseURL, createReportEndpoint)

	if _, err := uuid.Parse(req.RequestID); err != nil {
		return nil, fmt.Errorf("invalid UUID format for request_id: %w", err)
	}

	reqBody, err := json.Marshal(req)
	if err != nil {
		return nil, fmt.Errorf("error marshaling request body: %w", err)
	}

	httpReq, err := http.NewRequestWithContext(ctx, http.MethodPost, url, bytes.NewBuffer(reqBody))
	if err != nil {
		return nil, fmt.Errorf("error creating new http request: %w", err)
	}

	httpReq.Header.Set("Authorization", "Bearer "+token)
	httpReq.Header.Set("Content-Type", "application/json")

	resp, err := c.HTTPClient.Do(httpReq)
	if err != nil {
		return nil, fmt.Errorf("error doing http request: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		bodyBytes, _ := io.ReadAll(resp.Body)
		return nil, fmt.Errorf("error status of http response [%d]: %s", resp.StatusCode, bodyBytes)
	}

	var reportResp entity.CreateReportResponse
	if err := json.NewDecoder(resp.Body).Decode(&reportResp); err != nil {
		return nil, fmt.Errorf("error decoding http response body: %w", err)
	}

	return &reportResp, nil
}

// Получение статуса отчета для Xdata
func (c *Client) GetReportStatus(ctx context.Context, reportID string) (*entity.CreateReportResponse, error) {
	token, err := c.GetValidToken(ctx)
	if err != nil {
		return nil, fmt.Errorf("error getting valid token: %w", err)
	}
	if _, err := uuid.Parse(reportID); err != nil {
		return nil, fmt.Errorf("invalid UUID format for report_id: %w", err)
	}

	url := fmt.Sprintf("%s%s?reportID=%s", c.BaseURL, getReportStatusEndpoint, reportID)

	httpReq, err := http.NewRequestWithContext(ctx, http.MethodGet, url, nil)
	if err != nil {
		return nil, fmt.Errorf("error creating new http request: %w", err)
	}

	httpReq.Header.Set("Authorization", "Bearer "+token)

	resp, err := c.HTTPClient.Do(httpReq)
	if err != nil {
		return nil, fmt.Errorf("error doing http request: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		if resp.StatusCode == http.StatusInternalServerError {
			return nil, ErrInternalServerError
		}
		bodyBytes, _ := io.ReadAll(resp.Body)
		return nil, fmt.Errorf("error status of http response [%d]: %s", resp.StatusCode, bodyBytes)
	}

	var reportStatus entity.CreateReportResponse
	if err := json.NewDecoder(resp.Body).Decode(&reportStatus); err != nil {
		return nil, fmt.Errorf("error decoding http response body: %w", err)
	}

	return &reportStatus, nil
}

// Получение отчета Xdata
func (c *Client) GetReport(ctx context.Context, reportID string) (*entity.ReportResponse, error) {
	token, err := c.GetValidToken(ctx)
	if err != nil {
		return nil, fmt.Errorf("error getting valid token: %w", err)
	}
	if _, err := uuid.Parse(reportID); err != nil {
		return nil, fmt.Errorf("invalid UUID format for report_id: %w", err)
	}
	url := fmt.Sprintf("%s%s?reportID=%s&format=%s", c.BaseURL, getReportEndpoint, reportID, "json")

	httpReq, err := http.NewRequestWithContext(ctx, http.MethodGet, url, nil)
	if err != nil {
		return nil, fmt.Errorf("error creating new http request: %w", err)
	}

	httpReq.Header.Set("Authorization", "Bearer "+token)

	resp, err := c.HTTPClient.Do(httpReq)
	if err != nil {
		return nil, fmt.Errorf("error doing http request: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		if resp.StatusCode == http.StatusInternalServerError {
			return nil, ErrInternalServerError
		}
		bodyBytes, _ := io.ReadAll(resp.Body)
		return nil, fmt.Errorf("error status of http response [%d]: %s", resp.StatusCode, bodyBytes)
	}

	var reportResp entity.ReportResponse
	// так как гарантированно получаем json тут не обрабатываем xml
	if err := json.NewDecoder(resp.Body).Decode(&reportResp); err != nil {
		return nil, fmt.Errorf("error decoding http response body: %w", err)
	}

	return &reportResp, nil
}
