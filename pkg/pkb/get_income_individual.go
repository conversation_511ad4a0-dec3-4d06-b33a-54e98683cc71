package pkb

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"

	"git.redmadrobot.com/zaman/backend/zaman/pkg/pkb/entity"
)

func (c *Client) GetIncomeIndividual(ctx context.Context, req entity.IncomeIndividualRequest) (
	*entity.GetIncomeIndividualResp,
	error,
) {
	token, err := c.GetValidToken(ctx)
	if err != nil {
		return nil, fmt.Errorf("error getting token: %w", err)
	}

	if req.ConsentTokenTerm == 0 {
		req.ConsentTokenTerm = defaultConsentTokenTerm
	}
	if req.ConsentTokenTerm < minConsentTokenTerm || req.ConsentTokenTerm > maxConsentTokenTerm {
		return nil, fmt.Errorf(
			"invalid consent token term: %d (must be between %d and %d)", req.ConsentTokenTerm, minConsentTokenTerm,
			maxConsentTokenTerm,
		)
	}

	reqBody, err := json.Marshal(req)
	if err != nil {
		return nil, fmt.Errorf("error marshaling request body: %w", err)
	}

	reqHTTP, err := http.NewRequestWithContext(
		ctx,
		http.MethodPost,
		c.BaseURL+GetIncomeIndividualEndpoint,
		bytes.NewBuffer(reqBody),
	)
	if err != nil {
		return nil, fmt.Errorf("error creating request: %w", err)
	}

	reqHTTP.Header.Set("Authorization", "Bearer "+token)
	reqHTTP.Header.Set("Content-Type", "application/json")

	resp, err := c.HTTPClient.Do(reqHTTP)
	if err != nil {
		return nil, fmt.Errorf("error sending request: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		if resp.StatusCode == http.StatusInternalServerError {
			return nil, ErrInternalServerError
		}
		var errResp entity.ErrWithCodeResponse
		bodyBytes, _ := io.ReadAll(resp.Body)
		if err = json.Unmarshal(bodyBytes, &errResp); err != nil {
			return nil, fmt.Errorf("error decoding http err response body: %w", err)
		}
		return nil, fmt.Errorf("error status of http response [%d]: %s", resp.StatusCode, errResp.Message)
	}

	var result entity.GetIncomeIndividualResp
	if err = json.NewDecoder(resp.Body).Decode(&result); err != nil {
		return nil, fmt.Errorf("error decoding http response body: %w", err)
	}

	return &result, nil
}
