package pkb

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"

	"git.redmadrobot.com/zaman/backend/zaman/pkg/pkb/entity"
)

// Получение персональной информаций из ГБДФЛ по ИИН
func (c *Client) GetFamilyInfoByIIN(ctx context.Context, iin string) (*entity.FamilyInfoResponse, error) {
	token, err := c.GetValidToken(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get valid token: %w", err)
	}
	url := fmt.Sprintf("%s%s", c.BaseURL, getFamilyInfoByIinEndpoint)
	requestBody := entity.FamilyInfoRequest{
		IINData: entity.IINData{
			IIN: iin,
		},
	}
	reqBody, err := json.Marshal(requestBody)
	if err != nil {
		return nil, fmt.Errorf("error marshaling request body: %w", err)
	}
	httpReq, err := http.NewRequestWithContext(ctx, http.MethodPost, url, bytes.NewBuffer(reqBody))
	if err != nil {
		return nil, fmt.Errorf("error creating new http request: %w", err)
	}

	httpReq.Header.Set("X-Subject-Consent", "4")
	httpReq.Header.Set("Authorization", "Bearer "+token)
	httpReq.Header.Set("Content-Type", "application/json")
	httpReq.Header.Set("X-Client-Type", "API")

	resp, err := c.HTTPClient.Do(httpReq)
	if err != nil {
		return nil, fmt.Errorf("error doing http request: %w", err)
	}
	defer resp.Body.Close()
	if resp.StatusCode != http.StatusOK {
		if resp.StatusCode == http.StatusInternalServerError {
			return nil, ErrInternalServerError
		}
		bodyBytes, _ := io.ReadAll(resp.Body)
		return nil, fmt.Errorf("error status of http response [%d]: %s", resp.StatusCode, bodyBytes)
	}
	var familyInfoResp entity.FamilyInfoResponse
	if err := json.NewDecoder(resp.Body).Decode(&familyInfoResp); err != nil {
		return nil, fmt.Errorf("error decoding http response body: %w", err)
	}

	return &familyInfoResp, nil
}
