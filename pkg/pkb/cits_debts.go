package pkb

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"

	"git.redmadrobot.com/zaman/backend/zaman/pkg/pkb/entity"
)

func (c *Client) GetCitsDebts(ctx context.Context, iin string) (*entity.CitsDebtsResp, error) {
	token, err := c.GetValidToken(ctx)
	if err != nil {
		return nil, fmt.Errorf("error getting valid token: %w", err)
	}

	reqURL, err := c.getRequestURL(fmt.Sprintf("%s?uin=%s", citsDebtsEndpoint, iin))
	if err != nil {
		return nil, fmt.Errorf("error creating requestURL: %w", err)
	}

	req, err := http.NewRequestWithContext(ctx, http.MethodGet, reqURL, nil)
	if err != nil {
		return nil, fmt.Errorf("error creating request: %w", err)
	}

	req.Header.Set("Authorization", "Bearer "+token)
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("X-Client-Type", "API")

	res, err := c.HTTPClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("error sending request: %w", err)
	}
	defer res.Body.Close()

	if res.StatusCode != http.StatusOK {
		bodyBytes, err := io.ReadAll(res.Body)
		if err != nil {
			return nil, fmt.Errorf("error reading response body: %w", err)
		}

		return nil, fmt.Errorf("error response: %d, %s", res.StatusCode, string(bodyBytes))
	}

	var result entity.CitsDebtsResp
	if err = json.NewDecoder(res.Body).Decode(&result); err != nil {
		return nil, fmt.Errorf("error parsing the response body: %w", err)
	}

	return &result, nil
}

func (c *Client) getRequestURL(path string) (string, error) {
	result, err := url.JoinPath(c.BaseURL, path)
	if err != nil {
		return "", fmt.Errorf("failed to join path for url %s; path %s; err: %w", c.BaseURL, path, err)
	}
	return result, nil
}
