package pkb

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"

	"git.redmadrobot.com/zaman/backend/zaman/pkg/pkb/entity"
)

// Получение информации о наличии статуса получателя Адресной Социальной Помощи (АСП)
func (c *Client) GetAspStatus(ctx context.Context, req *entity.AspReq) (*entity.AspResp, error) {
	token, err := c.GetValidToken(ctx)
	if err != nil {
		return nil, fmt.Errorf("error getting token: %w", err)
	}

	body, err := json.Marshal(req)
	if err != nil {
		return nil, fmt.Errorf("error marshalling body: %w", err)
	}

	httpReq, err := http.NewRequestWithContext(
		ctx,
		http.MethodPost,
		c.BaseURL+GetAspStatysByIin,
		bytes.NewBuffer(body),
	)
	if err != nil {
		return nil, fmt.Errorf("error creating http request: %w", err)
	}

	httpReq.Header.Set("Authorization", fmt.Sprintf("Bearer %s", token))
	httpReq.Header.Set("Content-Type", "application/json")
	httpReq.Header.Set("Accept", "application/json")

	resp, err := c.HTTPClient.Do(httpReq)
	if err != nil {
		return nil, fmt.Errorf("error sending http request: %w", err)
	}

	defer resp.Body.Close()

	bodyBytes, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("error reading response body: %w", err)
	}

	if resp.StatusCode != http.StatusOK {
		if resp.StatusCode == http.StatusInternalServerError {
			return nil, ErrInternalServerError
		}
		var errResp entity.ErrWithCodeResponse
		if err = json.Unmarshal(bodyBytes, &errResp); err != nil {
			return nil, fmt.Errorf("error unmarshalling http err response body: %w", err)
		}
		return nil, fmt.Errorf("error status of http response [%d]: %s", resp.StatusCode, errResp.Message)
	}

	var result entity.AspResp
	if err = json.Unmarshal(bodyBytes, &result); err != nil {
		return nil, fmt.Errorf("error unmarshalling response body: %w", err)
	}

	return &result, nil
}
