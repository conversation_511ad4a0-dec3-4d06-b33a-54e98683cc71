package pkb

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"

	"git.redmadrobot.com/zaman/backend/zaman/pkg/pkb/entity"

	"github.com/google/uuid"
)

// Отправка информаций о кредитной заявке
func (c *Client) SendLoanApplicationInfo(ctx context.Context, params entity.ParamsSendLoanApplicationInfo) (*entity.SendLoanApplicationInfoResp, error) {
	token, err := c.GetValidToken(ctx)
	if err != nil {
		return nil, fmt.Errorf("error getting valid token: %w", err)
	}

	url := fmt.Sprintf("%s%s", c.AdditionalURL, sendLoanApplicationInfoEndpoint)

	if _, err := uuid.Parse(params.RequestID); err != nil {
		return nil, fmt.Errorf("invalid UUID format for request_id: %w", err)
	}

	reqBody, err := json.Marshal(params)
	if err != nil {
		return nil, fmt.Errorf("error marshaling request body: %w", err)
	}

	httpReq, err := http.NewRequestWithContext(ctx, http.MethodPost, url, bytes.NewBuffer(reqBody))
	if err != nil {
		return nil, fmt.Errorf("error creating new http request: %w", err)
	}

	httpReq.Header.Set("Authorization", fmt.Sprintf("Bearer %s", token))
	httpReq.Header.Set("Content-Type", "application/json")
	httpReq.Header.Set("RequestId", params.RequestID)

	resp, err := c.HTTPClient.Do(httpReq)
	if err != nil {
		return nil, fmt.Errorf("error doing http request: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		if resp.StatusCode == http.StatusInternalServerError {
			return nil, ErrInternalServerError
		}
		bodyBytes, _ := io.ReadAll(resp.Body)
		return nil, fmt.Errorf("error status of http response [%d]: %s", resp.StatusCode, bodyBytes)
	}

	var respBody entity.SendLoanApplicationInfoResp
	if err := json.NewDecoder(resp.Body).Decode(&respBody); err != nil {
		return nil, fmt.Errorf("error decoding http response body: %w", err)
	}

	return &respBody, nil
}
