package pkb

import (
	"archive/zip"
	"bytes"
	"encoding/xml"
	"fmt"
	"io"

	"git.redmadrobot.com/zaman/backend/zaman/pkg/pkb/entity"
)

const (
	StatusOriginIsUnreachable = 523
	StatusXMLFileName         = "status.xml"
	ReportXMLFileName         = "report.xml"
)

func processGetReportPdfServiceZipResponse(zipData []byte) (
	*entity.GetReportPdfServiceResp,
	error,
) {
	if len(zipData) == 0 {
		return nil, fmt.Errorf("zip data is nil or empty")
	}

	zipReader, err := zip.NewReader(bytes.NewReader(zipData), int64(len(zipData)))
	if err != nil {
		return nil, fmt.Errorf("failed to create zip reader: %w", err)
	}

	var status entity.GetReportPdfServiceStatus
	var report entity.GetReportPdfServiceResp

	var dataXML []byte

	for _, file := range zipReader.File {
		if file.Name == StatusXMLFileName {
			rc, err := file.Open()
			if err != nil {
				return nil, fmt.Errorf("failed to open status.xml: %w", err)
			}
			defer rc.Close()
			statusXMLContent, err := io.ReadAll(rc)
			if err != nil {
				return nil, fmt.Errorf("failed to read status.xml: %w", err)
			}

			err = xml.Unmarshal(statusXMLContent, &status)
			if err != nil {
				return nil, fmt.Errorf("failed to unmarshal status.xml: %w", err)
			}
		} else if file.Name == ReportXMLFileName {
			rc, err := file.Open()
			if err != nil {
				return nil, fmt.Errorf("failed to open file %s: %w", file.Name, err)
			}
			defer rc.Close()
			dataXML, err = io.ReadAll(rc)
			if err != nil {
				return nil, fmt.Errorf("failed to read file %s: %w", file.Name, err)
			}
			err = xml.Unmarshal(dataXML, &report)
			if err != nil {
				return nil, fmt.Errorf("failed to unmarshal report.xml: %w", err)
			}
		}
	}
	if status.ReportID == 0 {
		return nil, fmt.Errorf("ReportID is empty")
	}

	report.Status = &status

	return &report, nil
}

func verifyZipFile(data []byte) error {
	// Проверяем, что ответ не пустой
	if len(data) == 0 {
		return fmt.Errorf("empty response body")
	}

	// Проверяем, что ответ является ZIP-файлом
	if len(data) < 4 {
		return fmt.Errorf("response is too short to be a valid ZIP file")
	}

	// Проверка сигнатуры ZIP-файла (первые 4 байта должны быть 0x50 0x4B 0x03 0x04)
	if data[0] != 0x50 || data[1] != 0x4B || data[2] != 0x03 || data[3] != 0x04 {
		return fmt.Errorf("response is not a valid ZIP file")
	}
	return nil
}
