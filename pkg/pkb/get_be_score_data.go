package pkb

import (
	"bytes"
	"context"
	"encoding/xml"
	"fmt"
	"io"
	"net/http"

	"git.redmadrobot.com/zaman/backend/zaman/pkg/pkb/entity"
)

func (c *Client) GetBeScoreData(
	ctx context.Context,
	beScoreReq *entity.BeScoreRequest,
) (*entity.BeScoreEnvelopeResponse, error) {
	buf := bytes.NewBuffer(nil)
	buf.WriteString(xml.Header)

	soapReq := c.makeBeScoreEnvelopeRequest(beScoreReq)

	body, err := xml.Marshal(soapReq)
	if err != nil {
		return nil, fmt.Errorf("error marshalling body: %w", err)
	}
	buf.Write(body)

	req, err := http.NewRequestWithContext(
		ctx,
		http.MethodPost,
		c.AdditionalURL+GetBeScoreEndpoint,
		buf,
	)
	if err != nil {
		return nil, fmt.Errorf("error creating request: %w", err)
	}

	req.Header.Set("Content-Type", "text/xml")
	req.Header.Set("charset", "utf-8")

	resp, err := c.HTTPClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("error sending request: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		bodyBytes, _ := io.ReadAll(resp.Body)
		return nil, fmt.Errorf("error status of http response [%d]: %s", resp.StatusCode, string(bodyBytes))
	}

	var result entity.BeScoreEnvelopeResponse
	if err = xml.NewDecoder(resp.Body).Decode(&result); err != nil {
		return nil, fmt.Errorf("error decoding http response body: %w", err)
	}

	return &result, nil
}

func (c *Client) makeBeScoreEnvelopeRequest(req *entity.BeScoreRequest) entity.BeScoreEnvelopeRequest {
	return entity.BeScoreEnvelopeRequest{
		SoapEnv: pkbBeScoreSoapEnv,
		Ws:      pkbBeScoreNamespace,
		Header: entity.BeScoreHeaderRequest{
			CigWsHeader: entity.CigWsHeader{
				UserName:      c.Login,
				Password:      c.Password,
				Culture:       pkbBeScoreCulture,
				SecurityToken: pkbBeScoreSecurityToken,
				UserID:        pkbBeScoreUserID,
				Version:       pkbBeScoreVersion,
			},
		},
		Body: entity.BeScoreBodyRequest{
			Score: entity.Score{
				ScoreCard: pkbBeScoreScoreCard,
				Attributes: entity.Attributes{
					Attribute: []entity.Attribute{
						{
							Name:  pkbBeScoreIINAttr,
							Value: req.Iin,
						},
						{
							Name:  pkbBeScoreConsentAttr,
							Value: pkbBeScoreConsentConfirmed,
						},
					},
				},
			},
		},
	}
}
