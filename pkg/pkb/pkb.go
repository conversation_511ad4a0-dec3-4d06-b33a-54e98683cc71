package pkb

import (
	"bytes"
	"context"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"sync"
	"time"

	"git.redmadrobot.com/zaman/backend/zaman/pkg/pkb/entity"

	"git.redmadrobot.com/zaman/backend/zaman/pkg/utils"
)

const userInfoKey = "userInfo"

var _ PKBProvider = (*Client)(nil)

type (
	PKBProvider interface {
		Auth(ctx context.Context) error
		RefreshToken(ctx context.Context) error
		CreateReport(ctx context.Context, req entity.ReportRequest) (*entity.CreateReportResponse, error)
		GetReportStatus(ctx context.Context, reportID string) (*entity.CreateReportResponse, error)
		GetValidToken(ctx context.Context) (string, error)
		GetPersonalInfoByIin(ctx context.Context, iin string) (*entity.GetPersonalInfoResponse, error)
		GetReport(ctx context.Context, reportID string) (*entity.ReportResponse, error)
		GetArmyStatusByIIN(ctx context.Context, iin string) (*entity.ArmyStatus, error)
		GetFamilyInfoByIIN(ctx context.Context, iin string) (*entity.FamilyInfoResponse, error)
		GetSusnSubject(ctx context.Context, params entity.ParamsGetSusnSubject) (*entity.GetSusnSubjectResponse, error)
		SendLoanApplicationInfo(
			ctx context.Context,
			params entity.ParamsSendLoanApplicationInfo,
		) (*entity.SendLoanApplicationInfoResp, error)
		GetStopCreditStatusByIin(ctx context.Context, iin string) (*entity.StopCreditStatusResponse, error)
		JurSearchByIIN(ctx context.Context, iin string) (*entity.SendJurSearchResponse, error)
		GetPermitDocumentsByIin(ctx context.Context, iin string) (*entity.GetPermitDocumentsByIinResponse, error)
		GetReportPdfService(ctx context.Context, params entity.ParamsGetReportPdfService) (*entity.GetReportPdfServiceResp, error)
		GetGbdulbybin(ctx context.Context, bin string) (*entity.GetOrganizationInfoResponse, error)
		CheckIinPhoneMatch(ctx context.Context, iin string, phoneNumber string) (*entity.IinPhoneMatchResponse, error)
		GetAspStatus(ctx context.Context, req *entity.AspReq) (*entity.AspResp, error)
		GetIncomeCompany(ctx context.Context, req *entity.IncomeCompanyRequest) (*entity.GetIncomeCompanyResp, error)
		GetIncomeIndividual(ctx context.Context, req entity.IncomeIndividualRequest) (*entity.GetIncomeIndividualResp, error)
		GetBeScoreData(ctx context.Context, req *entity.BeScoreRequest) (*entity.BeScoreEnvelopeResponse, error)
		GetSoHoEntrepreneur(ctx context.Context, iinOrBin string) (*entity.GetSoHoEntrepreneurResult, error)
		GetCitsDebts(ctx context.Context, iin string) (*entity.CitsDebtsResp, error)
	}
	Client struct {
		HTTPClient    *http.Client
		BaseURL       string // https://fcbopenapi-gw.1cb.kz
		AdditionalURL string // https://secure2.1cb.kz
		Login         string
		Password      string
		Tokens        *entity.Tokens
		mu            sync.RWMutex
	}
)

// Update NewClient to pass the flag
func NewClient(cfg *Config) PKBProvider {
	return &Client{
		HTTPClient:    utils.CreateHTTPClientWithUserIDTransport(http.DefaultTransport, cfg.UseMock),
		BaseURL:       cfg.BaseURL,
		AdditionalURL: cfg.AdditionalURL,
		Login:         cfg.Login,
		Password:      cfg.Password,
	}
}

// Метод по логину и паролю получает токен
func (c *Client) Auth(ctx context.Context) error {
	url := fmt.Sprintf("%s%s", c.BaseURL, authEndpoint)
	authHeader := "Basic " + base64.StdEncoding.EncodeToString([]byte(c.Login+":"+c.Password))
	req, err := http.NewRequestWithContext(ctx, http.MethodPost, url, bytes.NewBuffer(nil))
	if err != nil {
		return fmt.Errorf("error creating new http request: %w", err)
	}
	req.Header.Set("Authorization", authHeader)
	resp, err := c.HTTPClient.Do(req)
	if err != nil {
		return fmt.Errorf("error doing http request: %w", err)
	}
	defer resp.Body.Close()
	if resp.StatusCode != http.StatusOK {
		bodyBytes, _ := io.ReadAll(resp.Body)
		return fmt.Errorf("error status of http response [%d]: %s", resp.StatusCode, bodyBytes)
	}
	var tokens entity.Tokens
	if err = json.NewDecoder(resp.Body).Decode(&tokens); err != nil {
		return fmt.Errorf("error decoding http response body: %w", err)
	}
	c.Tokens = &tokens
	return nil
}

// Обновляет токен
func (c *Client) RefreshToken(ctx context.Context) error {
	if c.Tokens == nil {
		return fmt.Errorf("no tokens available for refresh")
	}

	refreshToken := c.Tokens.Refresh.Hash
	url := fmt.Sprintf("%s%s", c.BaseURL, refreshEndpoint)
	requestBody, err := json.Marshal(entity.RefreshRequest{TokenHash: refreshToken})
	if err != nil {
		return fmt.Errorf("error marshaling request body: %w", err)
	}
	req, err := http.NewRequestWithContext(ctx, http.MethodPost, url, bytes.NewBuffer(requestBody))
	if err != nil {
		return fmt.Errorf("error creating new http request: %w", err)
	}
	req.Header.Set("Content-Type", "application/json")
	resp, err := c.HTTPClient.Do(req)
	if err != nil {
		return fmt.Errorf("error doing http request: %w", err)
	}
	defer resp.Body.Close()
	if resp.StatusCode != http.StatusOK {
		bodyBytes, _ := io.ReadAll(resp.Body)
		return fmt.Errorf("error status of http response [%d]: %s", resp.StatusCode, bodyBytes)
	}
	var tokenResp entity.Tokens
	if err = json.NewDecoder(resp.Body).Decode(&tokenResp); err != nil {
		return fmt.Errorf("error decoding http response body: %w", err)
	}
	c.Tokens = &tokenResp
	return nil
}

// Получает действительный токен для аутентификации
// Если есть действующий токен доступа - возвращает его
// Если токен доступа истек, но есть действующий токен обновления - обновляет токены
// Если все токены недействительны - выполняет повторную аутентификацию
func (c *Client) GetValidToken(ctx context.Context) (string, error) {
	// Пробуем получить действующий токен доступа под RLock
	c.mu.RLock()
	if c.Tokens != nil && c.Tokens.Access.ExpiresAt != "" {
		expiresAt, err := time.Parse(time.RFC3339Nano, c.Tokens.Access.ExpiresAt)
		if err == nil && time.Now().Before(expiresAt) {
			token := c.Tokens.Access.Hash
			c.mu.RUnlock()
			return token, nil
		}
	}
	c.mu.RUnlock()

	// Если быстрый путь не сработал - блокируем для записи
	c.mu.Lock()
	defer c.mu.Unlock()

	// Повторно проверяем токен доступа под Lock
	if c.Tokens != nil && c.Tokens.Access.ExpiresAt != "" {
		expiresAt, err := time.Parse(time.RFC3339Nano, c.Tokens.Access.ExpiresAt)
		if err == nil && time.Now().Before(expiresAt) {
			return c.Tokens.Access.Hash, nil
		}

		// Проверяем возможность обновления токена
		if c.Tokens.Refresh.ExpiresAt != "" {
			refreshExpiresAt, err := time.Parse(time.RFC3339Nano, c.Tokens.Refresh.ExpiresAt)
			if err == nil && time.Now().Before(refreshExpiresAt) {
				if err := c.RefreshToken(ctx); err != nil {
					return "", fmt.Errorf("error refreshing token: %w", err)
				}
				return c.Tokens.Access.Hash, nil
			}
		}
	}

	// Если все токены недействительны - выполняем повторную аутентификацию
	if err := c.Auth(ctx); err != nil {
		return "", fmt.Errorf("error authenticating: %w", err)
	}
	return c.Tokens.Access.Hash, nil
}

func AddUserIDToContext(ctx context.Context, userID string) context.Context {
	userInfo, ok := ctx.Value(userInfoKey).(map[string]interface{})
	if !ok {
		// If userInfo doesn't exist or is not a map, create a new one
		userInfo = make(map[string]interface{})
	}

	// Add or update the user ID in the userInfo map
	userInfo["user_id"] = userID

	// Return the updated context with the new userInfo
	return context.WithValue(ctx, userInfoKey, userInfo) //nolint:staticcheck
}
