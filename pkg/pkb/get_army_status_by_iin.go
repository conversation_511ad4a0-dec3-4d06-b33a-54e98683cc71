package pkb

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"

	"git.redmadrobot.com/zaman/backend/zaman/pkg/pkb/entity"

	"github.com/google/uuid"
)

// Получение статуса призывника по ИИН
func (c *Client) GetArmyStatusByIIN(ctx context.Context, iin string) (*entity.ArmyStatus, error) {
	token, err := c.GetValidToken(ctx)
	if err != nil {
		return nil, fmt.Errorf("error getting valid token: %w", err)
	}

	req, err := http.NewRequestWithContext(
		ctx,
		http.MethodGet,
		fmt.Sprintf(c.AdditionalURL+GetArmyStatusEndpoint, iin),
		nil,
	)
	if err != nil {
		return nil, fmt.Errorf("error creating new http request: %w", err)
	}

	req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", token))
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("RequestID", uuid.New().String())

	resp, err := c.HTTPClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("error doing http request: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		if resp.StatusCode == http.StatusInternalServerError {
			return nil, ErrInternalServerError
		}
		var errResp entity.ErrResponse
		if err = json.NewDecoder(resp.Body).Decode(&errResp); err != nil {
			var rawErrResp string
			if err = json.NewDecoder(resp.Body).Decode(&rawErrResp); err != nil {
				return nil, fmt.Errorf("error decoding http err response body: %w", err)
			}
			return nil, fmt.Errorf("error decoding http err response body: %w", err)
		}
		if errResp.Message == fmt.Sprintf(RecruitNotFound, iin) {
			return nil, ErrRecruitNotFound
		}
		return nil, fmt.Errorf("error status of http response [%d]: %s", resp.StatusCode, errResp.Message)
	}

	var result entity.ArmyStatus
	if err = json.NewDecoder(resp.Body).Decode(&result); err != nil {
		return nil, fmt.Errorf("error decoding http response body: %w", err)
	}

	return &result, nil
}
