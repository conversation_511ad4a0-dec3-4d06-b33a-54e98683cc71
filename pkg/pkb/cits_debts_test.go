package pkb

import (
	"context"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"

	"git.redmadrobot.com/zaman/backend/zaman/pkg/pkb/entity"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func newMockPKBProviderGetCitsDebtsServer(t *testing.T, m mockServerCase) *httptest.Server {
	handler := http.NewServeMux()
	handler.HandleFunc(
		m.URI, func(w http.ResponseWriter, r *http.Request) {
			assert.Equal(t, m.Method, r.Method)

			authHeader := r.Header.Get("Authorization")
			expectedAuthHeader := "Bearer " + hashAccessToken
			assert.Equal(t, expectedAuthHeader, authHeader)

			contentType := r.Header.Get("Content-Type")
			assert.Equal(t, "application/json", contentType)

			clientType := r.Header.Get("X-Client-Type")
			assert.Equal(t, "API", clientType)

			w.WriteHeader(m.StatusCode)
			if m.Resp != nil {
				err := json.NewEncoder(w).Encode(m.Resp)
				require.NoError(t, err)
			}
		},
	)
	return httptest.NewServer(handler)
}

func TestClient_GetCitsDebts(t *testing.T) {
	testCases := []struct {
		name         string
		mockCase     mockServerCase
		expectErr    bool
		expectFields func(t *testing.T, resp *entity.CitsDebtsResp)
	}{
		{
			name: "Success",
			mockCase: mockServerCase{
				StatusCode: http.StatusOK,
				Resp: map[string]any{
					"requestNumber": "req-123",
					"code":          "200",
					"message":       "Success",
					"data": map[string]any{
						"iinBin":                      iin,
						"nameKz":                      "Аты КЗ",
						"nameRu":                      "Имя РУ",
						"totalArrear":                 "1000",
						"totalTaxArrear":              "500",
						"pensionContributionArrear":   "100",
						"socialContributionArrear":    "200",
						"socialHealthInsuranceArrear": "300",
						"taxOrgInfo":                  []map[string]any{},
					},
				},
				URI:    citsDebtsEndpoint + "?uin=" + iin,
				Method: http.MethodGet,
			},
			expectErr: false,
			expectFields: func(t *testing.T, resp *entity.CitsDebtsResp) {
				require.Equal(t, "req-123", resp.RequestNumber)
				require.Equal(t, "200", resp.Code)
				require.Equal(t, "Success", resp.Message)
				require.Equal(t, iin, resp.Data.IinBin)
				require.Equal(t, "Аты КЗ", resp.Data.NameKz)
				require.Equal(t, "Имя РУ", resp.Data.NameRu)
				require.Equal(t, "1000", resp.Data.TotalArrear)
				require.Equal(t, "500", resp.Data.TotalTaxArrear)
				require.Equal(t, "100", resp.Data.PensionContributionArrear)
				require.Equal(t, "200", resp.Data.SocialContributionArrear)
				require.Equal(t, "300", resp.Data.SocialHealthInsuranceArrear)
				require.NotNil(t, resp.Data.TaxOrgInfo)
				require.Len(t, resp.Data.TaxOrgInfo, 0)
			},
		},
		{
			name: "Error response",
			mockCase: mockServerCase{
				StatusCode: http.StatusBadRequest,
				Resp:       map[string]any{"message": "Invalid IIN format"},
				URI:        citsDebtsEndpoint + "?uin=" + iin,
				Method:     http.MethodGet,
			},
			expectErr: true,
			expectFields: func(t *testing.T, resp *entity.CitsDebtsResp) {
				assert.Nil(t, resp)
			},
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			mockServer := newMockPKBProviderGetCitsDebtsServer(t, tc.mockCase)
			defer mockServer.Close()
			client := getTestPKBClient(mockServer.URL, mockServer.Client())

			ctx := context.Background()
			resp, err := client.GetCitsDebts(ctx, iin)
			if tc.expectErr {
				assert.Error(t, err)
				assert.Nil(t, resp)
			} else {
				require.NoError(t, err)
				require.NotNil(t, resp)
				tc.expectFields(t, resp)
			}
		})
	}
}
