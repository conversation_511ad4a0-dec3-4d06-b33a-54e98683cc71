package pkb

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"

	"git.redmadrobot.com/zaman/backend/zaman/pkg/pkb/entity"
)

// Получение информации по спискам лицензий ИП, на наличие запрещенных по ИИН
func (c *Client) GetPermitDocumentsByIin(ctx context.Context, iin string) (*entity.GetPermitDocumentsByIinResponse, error) {
	token, err := c.GetValidToken(ctx)
	if err != nil {
		return nil, fmt.Errorf("error getting token: %w", err)
	}

	allData := entity.GetPermitDocumentsByIinResponse{}
	page := 1
	size := 10

	for {
		param := entity.GetPermitDocumentsByIinRequest{
			Iin:  iin,
			Page: page,
			Size: size,
		}

		body, err := json.Marshal(param)
		if err != nil {
			return nil, fmt.Errorf("error marshalling body: %w", err)
		}

		req, err := http.NewRequestWithContext(
			ctx,
			http.MethodPost,
			c.BaseURL+getPermitDocumentsByIinEndpoint,
			bytes.NewBuffer(body),
		)
		if err != nil {
			return nil, fmt.Errorf("error creating request: %w", err)
		}

		req.Header.Set("X-Client-Type", "API")
		req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", token))
		req.Header.Set("Content-Type", "application/json")

		resp, err := c.HTTPClient.Do(req)
		if err != nil {
			return nil, fmt.Errorf("error sending request: %w", err)
		}
		defer resp.Body.Close()

		if resp.StatusCode != http.StatusOK {
			if resp.StatusCode == http.StatusInternalServerError {
				return nil, ErrInternalServerError
			}
			var errResp entity.ErrWithCodeResponse
			bodyBytes, _ := io.ReadAll(resp.Body)
			if err = json.Unmarshal(bodyBytes, &errResp); err != nil {
				return nil, fmt.Errorf("error decoding http err response body: %w", err)
			}
			return nil, fmt.Errorf("error status of http response [%d]: %s", resp.StatusCode, errResp.Message)
		}

		var result entity.GetPermitDocumentsByIinResponse
		if err = json.NewDecoder(resp.Body).Decode(&result); err != nil {
			return nil, fmt.Errorf("error decoding http response body: %w", err)
		}

		// Если в ответе пустые данные — значит, больше страниц нет, выходим
		if len(result.Data.Response.ResponseData.Data.Licenses.TaxpayerLicense) == 0 {
			break
		}

		// Добавляем данные текущей страницы в общий результат
		allData.Data.Response.ResponseData.Data.Licenses.TaxpayerLicense = append(
			allData.Data.Response.ResponseData.Data.Licenses.TaxpayerLicense,
			result.Data.Response.ResponseData.Data.Licenses.TaxpayerLicense...,
		)

		page++
	}

	return &allData, nil
}
