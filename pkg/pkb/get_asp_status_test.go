package pkb

import (
	"context"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"git.redmadrobot.com/zaman/backend/zaman/pkg/pkb/entity"
)

func TestClient_GetAspStatus(t *testing.T) {
	successHandler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		_ = json.NewEncoder(w).Encode(entity.AspResp{
			Result: entity.ApsResult{Iin: "123456789012", State: "ASP"},
		})
	})

	badRequestHandler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusBadRequest)
		_ = json.NewEncoder(w).Encode(entity.ErrWithCodeResponse{
			Code:    "IIN_FORMAT_ERROR",
			Message: "IIN_FORMAT_ERROR",
		})
	})

	internalErrorHandler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusInternalServerError)
	})

	badJSONHandler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		_, _ = w.Write([]byte(`{"result": "bad`))
	})

	testCases := []struct {
		name         string
		handler      http.Handler
		req          *entity.AspReq
		expectedResp *entity.AspResp
		expectedErr  string
	}{
		{
			name:    "Success",
			handler: successHandler,
			req:     &entity.AspReq{Iin: "123456789012"},
			expectedResp: &entity.AspResp{
				Result: entity.ApsResult{Iin: "123456789012", State: "ASP"},
			},
		},
		{
			name:    "Without Token",
			handler: successHandler,
			req:     &entity.AspReq{Iin: "123456789012"},
			expectedResp: &entity.AspResp{
				Result: entity.ApsResult{Iin: "123456789012", State: "ASP"},
			},
		},
		{
			name:        "Bad Request - 400",
			handler:     badRequestHandler,
			req:         &entity.AspReq{Iin: "error-400"},
			expectedErr: "error status of http response [400]: IIN_FORMAT_ERROR",
		},
		{
			name:        "Internal Server Error - 500",
			handler:     internalErrorHandler,
			req:         &entity.AspReq{Iin: "error-500"},
			expectedErr: ErrInternalServerError.Error(),
		},
		{
			name:        "Bad JSON response",
			handler:     badJSONHandler,
			req:         &entity.AspReq{Iin: "bad-json"},
			expectedErr: "error unmarshalling response body",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			server := httptest.NewServer(tc.handler)
			defer server.Close()

			client := getTestPKBClient(server.URL, server.Client())

			switch tc.name {
			case "Without Token":
				client.Tokens = nil
			}

			resp, err := client.GetAspStatus(context.Background(), tc.req)

			if tc.expectedErr != "" {
				require.Error(t, err)
				assert.Contains(t, err.Error(), tc.expectedErr)
				assert.Nil(t, resp)
			} else {
				require.NoError(t, err)
				require.NotNil(t, resp)
				assert.Equal(t, tc.expectedResp, resp)
			}
		})
	}
}
