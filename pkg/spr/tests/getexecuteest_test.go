package tests_test

import (
	"bytes"
	"context"
	"encoding/json"
	"io"
	"net/http"
	"testing"

	"git.redmadrobot.com/zaman/backend/zaman/pkg/spr"
	"git.redmadrobot.com/zaman/backend/zaman/pkg/spr/entity"
	"git.redmadrobot.com/zaman/backend/zaman/pkg/spr/tests"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestSPRGetGetExecuteEst_Success(t *testing.T) {
	t.<PERSON><PERSON>()

	mockServerData := tests.MockServerCase{
		StatusCode: http.StatusOK,
		Resp: map[string]any{
			"result": map[string]any{
				"decline": map[string]any{
					"af":  1,
					"sc":  2,
					"mac": 3,
					"lim": 4,
				},
				"products": []map[string]any{
					{
						"need_uw":         1,
						"need_pv":         1,
						"approved_amount": 1000,
						"approved_period": 12,
						"kdn":             0.5,
						"kdd":             0.6,
					},
				},
				"error": map[string]any{
					"type":    "test_error",
					"message": "test message",
				},
				"stop_credit": 1,
				"detailed_info": map[string]any{
					"random_value": 1.0,
					"risk_check": []map[string]any{
						{
							"check_code":          "test_code",
							"check_result":        1,
							"check_desc":          "test_desc",
							"risk_rule_type_id":   2,
							"risk_rule_name":      "test_rule_name",
							"created_at":          "2024-01-01",
							"is_pilot":            1,
							"pilot_name":          "test_pilot",
							"pilot_random_number": 1.0,
							"pilot_cutoff":        1.0,
						},
					},
					"risk_rule": []map[string]any{
						{
							"check_code":          "test_code",
							"check_result":        1,
							"check_desc":          "test_desc",
							"risk_rule_type_id":   1,
							"risk_rule_name":      "test_rule_name",
							"created_at":          "2024-01-01",
							"is_pilot":            1,
							"pilot_name":          "test_pilot",
							"pilot_random_number": 1.0,
							"pilot_cutoff":        1.0,
						},
					},
					"calc_limit_and_kdn": map[string]any{
						"stage1": map[string]any{
							"calculated_limit":          1000,
							"calculated_period":         12,
							"calculated_pmt":            100,
							"charges_halyk":             10,
							"charges_kaspi":             20,
							"cnt_distinct_periods":      5,
							"commissions":               30,
							"credit_decision":           "approved",
							"date_retirement":           "2024-01-01",
							"date_today":                "2024-01-01",
							"expenses":                  100,
							"income_pkb":                1000,
							"income_stage":              1000,
							"income_transactions_halyk": 500,
							"income_transactions_kaspi": 500,
							"income_transactions_sum":   1000,
							"max_period":                24,
							"max_term_mnth":             24,
							"max_transactions_halyk":    1000,
							"max_transactions_kaspi":    1000,
							"min_term_mnth":             3,
							"number_of_children":        2,
							"payment_sum":               1000,
							"pm":                        100,
							"purchases":                 500,
							"receipts":                  500,
							"reject_reason":             "",
							"replenishment":             500,
							"transfers":                 500,
							"withdrawals":               500,
						},
						"stage2": map[string]any{
							"calculated_limit":                    1000,
							"calculated_period":                   12,
							"calculated_pmt":                      100,
							"charges_pkb":                         10,
							"credit_decision":                     "approved",
							"income_stage":                        1000,
							"k":                                   0.5,
							"k_max":                               0.6,
							"kdn":                                 0.5,
							"kdn_max":                             0.6,
							"markup":                              0.1,
							"markup_mnth":                         0.1,
							"max_period":                          24,
							"max_pmt":                             1000,
							"min_pmt_min_amount":                  100,
							"monthly_instalment_amount_converted": 100,
							"monthly_charges":                     10,
							"overdue_pkb":                         0,
							"pti_coef":                            0.5,
							"reject_reason":                       "",
							"requested_amount":                    1000,
							"requested_period":                    12,
							"score":                               100,
							"tmp_calculated_limit":                1000,
							"tmp_period":                          12,
							"tmp_pmt":                             100,
						},
						"stage3": map[string]any{
							"calculated_kdd":                        0.5,
							"calculated_kdn":                        0.5,
							"calculated_limit":                      1000,
							"calculated_period":                     12,
							"calculated_pmt":                        100,
							"charges_pkb":                           10,
							"credit_decision":                       "approved",
							"income_stage":                          1000,
							"k":                                     0.5,
							"k_lim":                                 0.6,
							"ki":                                    1,
							"markup_mnth":                           0.1,
							"max_limit":                             2000,
							"min_limit":                             500,
							"number_of_overdue_instalments_max_all": 0,
							"overdue_pkb":                           0,
							"reject_reason":                         "",
							"requested_amount":                      1000,
							"step_limit":                            100,
							"tmp_calculated_limit":                  1000,
							"tmp_period":                            12,
							"tmp_pmt":                               100,
							"total_unpaid_sum":                      0,
							"unpaid_sum":                            0,
						},
					},
				},
			},
		},
		Method:      http.MethodPost,
		ContentType: "application/json",
	}

	mockServer := tests.GetMockHTTPServer(
		spr.ExecuteExtEndpoint, func(w http.ResponseWriter, r *http.Request) {
			assert.Equal(t, mockServerData.Method, r.Method)

			contentType := r.Header.Get("Content-Type")
			assert.Equal(t, "application/json", contentType)

			bodyBytes, err := io.ReadAll(r.Body)
			require.NoError(t, err)

			if mockServerData.ReqBody != nil && len(bodyBytes) > 0 {
				decoder := json.NewDecoder(bytes.NewReader(bodyBytes))
				var actualReqBody entity.SprRequest
				err = decoder.Decode(&actualReqBody)
				require.NoError(t, err)
			}

			w.WriteHeader(mockServerData.StatusCode)
			if mockServerData.Resp != nil {
				err := json.NewEncoder(w).Encode(mockServerData.Resp)
				require.NoError(t, err)
			}

		},
	)

	mockClient := tests.GetClient(mockServer.URL, mockServer.Client())

	data := &entity.SprRequest{}
	result, err := mockClient.GetExecuteExt(context.Background(), data)

	require.NoError(t, err)
	assert.NotNil(t, result)
}
