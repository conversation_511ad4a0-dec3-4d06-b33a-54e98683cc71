package tests

import (
	"net/http"
	"net/http/httptest"

	"git.redmadrobot.com/zaman/backend/zaman/pkg/spr"
)

type MockServerCase struct {
	StatusCode  int
	Resp        map[string]any
	URI         string
	Method      string
	ContentType string
	ReqString   *string
	ReqBody     map[string]any
	RespBytes   []byte
}

func GetClient(url string, client *http.Client) spr.SPRProvider {
	cfg := &spr.Config{
		BaseURL: url,
	}
	result := spr.NewClient(cfg)

	result.HTTPClient = client
	return result
}

func GetMockHTTPServer(url string, wrapper func(w http.ResponseWriter, r *http.Request)) *httptest.Server {
	handler := http.NewServeMux()
	handler.HandleFunc(url, wrapper)
	return httptest.NewServer(handler)
}
