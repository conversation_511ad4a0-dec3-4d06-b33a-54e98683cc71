package spr

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"

	"git.redmadrobot.com/backend-go/rmr-pkg/core/logs"
	"git.redmadrobot.com/zaman/backend/zaman/pkg/spr/consts"
	"git.redmadrobot.com/zaman/backend/zaman/pkg/spr/entity"
)

func (client *Client) GetExecuteExt(ctx context.Context, data *entity.SprRequest) (*entity.SprResponse, error) {
	reqBody, err := json.Marshal(data)
	if err != nil {
		return nil, err
	}

	logs.FromContext(ctx).Debug().RawJSON("spr request", reqBody)

	baseURL, err := url.Parse(client.BaseURL + ExecuteExtEndpoint)
	if err != nil {
		return nil, fmt.Errorf("invalid base URL: %w", err)
	}

	params := url.Values{}
	params.Add(consts.QueryParamAlias, data.StrategyInfo.Alias)
	baseURL.RawQuery = params.Encode()

	request, err := http.NewRequestWithContext(
		ctx, http.MethodPost, baseURL.String(), bytes.NewBuffer(reqBody),
	)
	if err != nil {
		return nil, err
	}

	request.Header.Set("Content-Type", "application/json")
	request.Header.Set("Authorization", fmt.Sprintf("%s %s", client.TypeAuth, client.TokenAuth))

	response, err := client.HTTPClient.Do(request)
	if err != nil {
		return nil, err
	}
	defer response.Body.Close()

	if response.StatusCode != http.StatusOK {
		if response.StatusCode == http.StatusInternalServerError {
			bodyBytes, _ := io.ReadAll(response.Body)
			return nil, fmt.Errorf("%w (body: %s)", ErrInternalServerError, bodyBytes)
		}
		var errResp entity.ErrWithCodeResponse
		bodyBytes, _ := io.ReadAll(response.Body)
		if err = json.Unmarshal(bodyBytes, &errResp); err != nil {
			return nil, fmt.Errorf("error decoding http err response body: %w", err)
		}
		return nil, fmt.Errorf("error status of http response [%d]: %s", response.StatusCode, errResp.Message)
	}

	var result entity.SprResponse
	if err = json.NewDecoder(response.Body).Decode(&result); err != nil {
		return nil, fmt.Errorf("error decoding http response body: %w", err)
	}

	resultByte, _ := json.Marshal(result)
	logs.FromContext(ctx).Debug().RawJSON("spr response", resultByte)

	return &result, nil
}
