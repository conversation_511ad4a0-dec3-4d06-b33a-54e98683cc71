package spr

import "errors"

const (
	IncorrectRequestIDDesc = "Incorrect ColvirRequestID"
	IncorrectIinDesc       = "Incorrect iin"
	IncorrectIndexDesc     = "Incorrect index"
	AuthErrDesc            = "Auth error"
	RecruitNotFound        = "Recruit IIN %s not found"
)

var (
	ErrInternalServerError  = errors.New("internal server error")
	ErrStatusGatewayTimeout = errors.New("status gateway timeout")
	ErrOriginIsUnreachable  = errors.New("origin is unreachable")

	ErrIncorrectRequestID   = errors.New("incorrect request id")
	ErrIncorrectIin         = errors.New("incorrect iin")
	ErrIncorrectIndex       = errors.New("incorrect index")
	ErrAuth                 = errors.New("auth error")
	ErrTokenExpired         = errors.New("token expired")
	ErrRecruitNotFound      = errors.New("recruit not found")
	ErrJurSearchUinNotFound = errors.New("jursearch uin not found")
)
