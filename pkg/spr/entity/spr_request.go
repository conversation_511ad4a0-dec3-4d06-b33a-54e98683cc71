package entity

type SprRequest struct {
	Application  Application  `json:"application" bson:"application"`
	StrategyInfo StrategyInfo `json:"strategyInfo" bson:"strategyInfo"`
}

type StrategyInfo struct {
	Alias     string `json:"alias" bson:"alias"`
	Signature string `json:"signature" bson:"signature"`
}

type Application struct {
	AppID                  int64               `json:"app_id" bson:"app_id"`
	TrackID                int64               `json:"track_id" bson:"track_id"`
	ClientID               int64               `json:"client_id" bson:"client_id"`
	AppType                string              `json:"app_type" bson:"app_type"`
	AppCreatedAt           string              `json:"app_created_at" bson:"app_created_at"`
	IpdlStatus             string              `json:"ipdl_status" bson:"ipdl_status"`
	ClientType             string              `json:"client_type" bson:"client_type"`
	IPRegDate              string              `json:"ip_reg_date" bson:"ip_reg_date"`
	IsBlocked              int                 `json:"is_blocked" bson:"is_blocked"`
	ClIdentification       string              `json:"cl_identification" bson:"cl_identification"`
	NotKzResident          int                 `json:"not_kz_resident" bson:"not_kz_resident"`
	BcID                   int                 `json:"bc_id" bson:"bc_id"`
	LifeStatus             int                 `json:"life_status" bson:"life_status"`
	Usdkzt                 float64             `json:"usdkzt" bson:"usdkzt"`
	Rubkzt                 float64             `json:"rubkzt" bson:"rubkzt"`
	Eurkzt                 float64             `json:"eurkzt" bson:"eurkzt"`
	PassportNumber         string              `json:"passport_number" bson:"passport_number"`
	IDNumber               string              `json:"id_number" bson:"id_number"`
	Birthday               string              `json:"birthday" bson:"birthday"`
	CrSoldToExternal       int                 `json:"cr_sold_to_external" bson:"cr_sold_to_external"`
	CrOpen                 int                 `json:"cr_open" bson:"cr_open"`
	RejProject             int                 `json:"rej_project" bson:"rej_project"`
	RejProjectType         []RejProjectType    `json:"rej_project_type" bson:"rej_project_type"`
	Iin                    string              `json:"iin" bson:"iin"`
	PkbIncomeRequestType   string              `json:"pkb_income_request_type" bson:"pkb_income_request_type"`
	ClientSex              string              `json:"client_sex" bson:"client_sex"`
	ClientAge              int                 `json:"client_age" bson:"client_age"`
	ClientEmail            string              `json:"client_email" bson:"client_email"`
	CleanEmailWoDomain     string              `json:"clean_email_wo_domain" bson:"clean_email_wo_domain"`
	RequestedAmount        float64             `json:"requested_amount" bson:"requested_amount"`
	PresentPosition        string              `json:"present_position" bson:"present_position"`
	CompanyScope           string              `json:"company_scope" bson:"company_scope"`
	RequestedPeriod        int                 `json:"requested_period" bson:"requested_period"`
	Markup                 float64             `json:"markup" bson:"markup"`
	ClientPhone            string              `json:"client_phone" bson:"client_phone"`
	IP                     string              `json:"ip" bson:"ip"`
	JsUserAgent            string              `json:"js_user_agent" bson:"js_user_agent"`
	JsSessionID            string              `json:"js_session_id" bson:"js_session_id"`
	Channel                string              `json:"channel" bson:"channel"`
	Children               int                 `json:"children" bson:"children"`
	LastName               string              `json:"last_name" bson:"last_name"`
	FirstName              string              `json:"first_name" bson:"first_name"`
	MiddleName             string              `json:"middle_name" bson:"middle_name"`
	AltscoreID             int64               `json:"altscore_id" bson:"altscore_id"`
	FlowType               string              `json:"flow_type" bson:"flow_type"`
	Pm                     float64             `json:"pm" bson:"pm"`
	UwRejected30D          int                 `json:"uw_rejected_30d" bson:"uw_rejected_30d"`
	AppNumber              int                 `json:"app_number" bson:"app_number"`
	LoanPurpose            string              `json:"loan_purpose" bson:"loan_purpose"`
	EducationLevel         string              `json:"education_level" bson:"education_level"`
	MaxOverdue             int                 `json:"max_overdue" bson:"max_overdue"`
	NumberOfClosedLoans    int                 `json:"number_of_closed_loans" bson:"number_of_closed_loans"`
	ClientRegistrationDate string              `json:"client_registration_date" bson:"client_registration_date"`
	DateOfPrevLoanClosure  string              `json:"date_of_prev_loan_closure" bson:"date_of_prev_loan_closure"`
	AdditionalContacts     []AdditionalContact `json:"additional_contacts" bson:"additional_contacts"`
	Coid                   Coid                `json:"coid" bson:"coid"`
	JuicyScore             JuicyScore          `json:"juicy_score" bson:"juicy_score"`
	PkbIncome              PkbIncome           `json:"pkb_income" bson:"pkb_income"`
	PkbKo                  PkbKO               `json:"pkb_ko" bson:"pkb_ko"`
	PkbSusn                PkbSusn             `json:"pkb_susn" bson:"pkb_susn"`
	PkbBmg                 PkbBmg              `json:"pkb_bmg" bson:"pkb_bmg"`
	PkbAsp                 PkbAsp              `json:"pkb_asp" bson:"pkb_asp"`
	AltScore               AltScore            `json:"altscore" bson:"altscore"`
	Seon                   Seon                `json:"seon" bson:"seon"`
	PkbRecruit             PkbRecruit          `json:"pkb_recruit" bson:"pkb_recruit"`
	PkbScoreEntrep         PkbScoreEntrep      `json:"pkb_score_entrep" bson:"pkb_score_entrep"`
	PkbScore               PkbScore            `json:"pkb_score" bson:"pkb_score"`
}

type Coid struct {
	LifeStatus   LifeStatus         `json:"life_status" bson:"life_status"`
	Verification VerificationSchema `json:"verification" bson:"verification"`
}

type AdditionalContact struct {
	FirstName    string `json:"first_name" bson:"first_name"`
	LastName     string `json:"last_name" bson:"last_name"`
	ContactPhone string `json:"contact_phone" bson:"contact_phone"`
	RelationType string `json:"relation_type" bson:"relation_type"`
}

type RejProjectType struct {
	RejRuleName string `json:"rej_rule_name" bson:"rej_rule_name"`
	RejDate     string `json:"rej_date" bson:"rej_date"`
}

type LifeStatus struct {
	NameRu     string `json:"name_ru" bson:"name_ru"`
	Code       string `json:"code" bson:"code"`
	NameKz     string `json:"name_kz" bson:"name_kz"`
	ChangeDate string `json:"change_date" bson:"change_date"`
}

type VerificationSchema struct {
	Result          string `json:"result" bson:"result"`
	Vendor          string `json:"vendor" bson:"vendor"`
	Iin             string `json:"iin" bson:"iin"`
	XIdempotencyKey string `json:"x_idempotency_key" bson:"x_idempotency_key"`
}
