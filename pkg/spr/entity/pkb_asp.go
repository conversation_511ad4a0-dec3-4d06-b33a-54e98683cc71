package entity

type PkbAsp struct {
	ExtDataPkbAsp []ExtDataPkbAsp `json:"ext_data_pkb_asp" bson:"ext_data_pkb_asp"`
}

type ExtDataPkbAsp struct {
	RequestLogID        int     `json:"request_log_id" bson:"request_log_id"`
	Iin                 string  `json:"iin" bson:"iin"`
	State               int     `json:"state" bson:"state"`
	SurName             string  `json:"surname" bson:"surname"`
	FirstName           string  `json:"firstname" bson:"firstname"`
	SecondName          string  `json:"secondname" bson:"secondname"`
	Sex                 string  `json:"sex" bson:"sex"`
	BirthDate           string  `json:"birth_date" bson:"birth_date"`
	SummAsp             float64 `json:"summ_asp" bson:"summ_asp"`
	FirstPaymentDate    string  `json:"first_payment_date" bson:"first_payment_date"`
	LastPaymentDate     string  `json:"last_payment_date" bson:"last_payment_date"`
	NumberOfChildren    int     `json:"number_of_children" bson:"number_of_children"`
	NumberFamilyMembers int     `json:"number_family_members" bson:"number_family_members"`
	SddFamilyMembers    float64 `json:"sdd_family_members" bson:"sdd_family_members"`
}
