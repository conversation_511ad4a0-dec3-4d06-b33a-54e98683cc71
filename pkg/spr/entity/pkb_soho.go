package entity

type PkbScoreEntrep struct {
	ExtDataScoreEntrep []ExtDataScoreEntrep `json:"ext_data_score_entrep" bson:"ext_data_score_entrep"`
}
type ExtDataScoreEntrep struct {
	ScoreID     string  `json:"score_id" bson:"score_id"`
	RiskClass   string  `json:"risk_class" bson:"risk_class"`
	Ball        int     `json:"ball" bson:"ball"`
	DefaultRate string  `json:"default_rate" bson:"default_rate"`
	IsFrozen    int     `json:"is_frozen" bson:"is_frozen"`
	QueryDate   string  `json:"query_date" bson:"query_date"`
	LastName    string  `json:"last_name" bson:"last_name"`
	MiddleName  string  `json:"middle_name" bson:"middle_name"`
	FirstName   string  `json:"first_name" bson:"first_name"`
	BadRate     float64 `json:"bad_rate" bson:"bad_rate"`
	Timestamp   string  `json:"timestamp" bson:"timestamp"`
	Iin         string  `json:"iin" bson:"iin"`
	IsEnt       int     `json:"is_ent" bson:"is_ent"`
	Result      int     `json:"result" bson:"result"`
	ResultDescr string  `json:"result_descr" bson:"result_descr"`
}
