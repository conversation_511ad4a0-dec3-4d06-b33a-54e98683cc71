package entity

type AltScore struct {
	ExtDataAltScore             []ExtDataAltscore             `json:"ext_data_altscore" bson:"ext_data_altscore"`
	ExtDataAltScoreTransactions []ExtDataAltscoreTransactions `json:"ext_data_altscore_transactions" bson:"ext_data_altscore_transactions"`
}
type ExtDataAltscore struct {
	RequestLogID               int     `json:"request_log_id" bson:"request_log_id"`
	AltScoreUID                string  `json:"altscore_uid" bson:"altscore_uid"`
	IncomeStatementsID         float64 `json:"income_statements_id" bson:"income_statements_id"`
	IncomeStatementsBank       string  `json:"income_statements_bank" bson:"income_statements_bank"`
	IncomeStatementsStatus     string  `json:"income_statements_status" bson:"income_statements_status"`
	IncomeStatementsCreatedAt  string  `json:"income_statements_created_at" bson:"income_statements_created_at"`
	IncomeStatementsUpdatedAt  string  `json:"income_statements_updated_at" bson:"income_statements_updated_at"`
	FullName                   string  `json:"full_name" bson:"full_name"`
	Iin                        string  `json:"iin" bson:"iin"`
	AccountNumber              string  `json:"account_number" bson:"account_number"`
	Currency                   string  `json:"currency" bson:"currency"`
	BankName                   string  `json:"bank_name" bson:"bank_name"`
	CardNumber                 string  `json:"card_number" bson:"card_number"`
	PeriodStartDate            string  `json:"period_start_date" bson:"period_start_date"`
	PeriodEndDate              string  `json:"period_end_date" bson:"period_end_date"`
	BalanceBefore              float64 `json:"balance_before" bson:"balance_before"`
	BalanceAfter               float64 `json:"balance_after" bson:"balance_after"`
	TransactionTotalReceipt    float64 `json:"transaction_total_receipt" bson:"transaction_total_receipt"`
	TransactionTotalExpense    float64 `json:"transaction_total_expense" bson:"transaction_total_expense"`
	TransactionTotalCommission float64 `json:"transaction_total_commission" bson:"transaction_total_commission"`
	TransactionReplenishment   float64 `json:"transaction_replenishment" bson:"transaction_replenishment"`
	TransactionTransfers       float64 `json:"transaction_transfers" bson:"transaction_transfers"`
	TransactionPurchases       float64 `json:"transaction_purchases" bson:"transaction_purchases"`
	TransactionWithdrawals     float64 `json:"transaction_withdrawals" bson:"transaction_withdrawals"`
	TransactionOther           float64 `json:"transaction_other" bson:"transaction_other"`
	LimitRemainingSalary       float64 `json:"limit_remaining_salary" bson:"limit_remaining_salary"`
	LimitTransfers             float64 `json:"limit_transfers" bson:"limit_transfers"`
	IsDiscrepancy              int     `json:"is_discrepancy" bson:"is_discrepancy"`
}

type ExtDataAltscoreTransactions struct {
	AltScoreUID       string  `json:"altscore_uid" bson:"altscore_uid"`
	ExtDataAltscoreID int     `json:"ext_data_altscore_id" bson:"ext_data_altscore_id"`
	Date              string  `json:"date" bson:"date"`
	CompletedDate     string  `json:"completed_date" bson:"completed_date"`
	Amount            float64 `json:"amount" bson:"amount"`
	Commission        float64 `json:"commission" bson:"commission"`
	Detail            string  `json:"detail" bson:"detail"`
	Type              string  `json:"type" bson:"type"`
	CreatedAt         string  `json:"created_at" bson:"created_at"`
	UpdatedAt         string  `json:"updated_at" bson:"updated_at"`
}
