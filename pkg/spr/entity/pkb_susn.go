package entity

type PkbSusn struct {
	ExtDataPkbSusn []ExtDataPkbSusn `json:"ext_data_pkb_susn" bson:"ext_data_pkb_susn"`
}

type ExtDataPkbSusn struct {
	RequestLogID int    `json:"request_log_id" bson:"request_log_id"`
	IIN          string `json:"iin" bson:"iin"`
	StatusCode   string `json:"status_code" bson:"status_code"`
	StatusStart  string `json:"status_start" bson:"status_start"`
	StatusEnd    string `json:"status_end" bson:"status_end"`
}
