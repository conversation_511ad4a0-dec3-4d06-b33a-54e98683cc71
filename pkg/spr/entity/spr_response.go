package entity

type SprResponse struct {
	Result Result `json:"result" bson:"result"`
}
type Result struct {
	Decline      Decline      `json:"decline" bson:"decline"`
	Products     []Products   `json:"products" bson:"products"`
	Error        Error        `json:"error" bson:"error"`
	StopCredit   int8         `json:"stop_credit" bson:"stopCredit"`
	DetailedInfo DetailedInfo `json:"detailed_info" bson:"detailedInfo"`
}

type Decline struct {
	Af  int8 `json:"af" bson:"af"`
	Sc  int8 `json:"sc" bson:"sc"`
	Mac int8 `json:"mac" bson:"mac"`
	Lim int8 `json:"lim" bson:"lim"`
}

type Products struct {
	NeedUw         int8    `json:"need_uw" bson:"needUw"`
	NeedPv         int8    `json:"need_pv" bson:"needPv"`
	ApprovedAmount float64 `json:"approved_amount" bson:"approvedAmount"`
	ApprovedPeriod int32   `json:"approved_period" bson:"approvedPeriod"`
	Kdn            float64 `json:"kdn" bson:"kdn"`
	Kdd            float64 `json:"kdd" bson:"kdd"`
}

type Error struct {
	Type    string `json:"type" bson:"type"`
	Message string `json:"message" bson:"message"`
}

type DetailedInfo struct {
	RandomValue     float64         `json:"random_value" bson:"random_value"`
	RiskCheck       []RiskCheck     `json:"risk_check" bson:"risk_check"`
	RiskRule        []RiskRule      `json:"risk_rule" bson:"risk_rule"`
	CalcLimitAndKDN CalcLimitAndKDN `json:"calc_limit_and_kdn" bson:"calc_limit_and_kdn"`
}

type RiskCheck struct {
	CheckCode         string  `json:"check_code" bson:"check_code"`
	CheckResult       int8    `json:"check_result" bson:"check_result"`
	CheckDesc         string  `json:"check_desc" bson:"check_desc"`
	RiskRuleTypeID    int32   `json:"risk_rule_type_id" bson:"risk_rule_type_id"`
	RiskRuleName      string  `json:"risk_rule_name" bson:"risk_rule_name"`
	CreatedAt         string  `json:"created_at" bson:"created_at"`
	IsPilot           int8    `json:"is_pilot" bson:"is_pilot"`
	PilotName         string  `json:"pilot_name" bson:"pilot_name"`
	PilotRandomNumber float64 `json:"pilot_random_number" bson:"pilot_random_number"`
	PilotCutoff       float64 `json:"pilot_cutoff" bson:"pilot_cutoff"`
}

type RiskRule struct {
	CheckCode         string  `json:"check_code" bson:"check_code"`
	CheckResult       int8    `json:"check_result" bson:"check_result"`
	CheckDesc         string  `json:"check_desc" bson:"check_desc"`
	RiskRuleTypeID    int32   `json:"risk_rule_type_id" bson:"risk_rule_type_id"`
	RiskRuleName      string  `json:"risk_rule_name" bson:"risk_rule_name"`
	CreatedAt         string  `json:"created_at" bson:"created_at"`
	IsPilot           int8    `json:"is_pilot" bson:"is_pilot"`
	PilotName         string  `json:"pilot_name" bson:"pilot_name"`
	PilotRandomNumber float64 `json:"pilot_random_number" bson:"pilot_random_number"`
	PilotCutoff       float64 `json:"pilot_cutoff" bson:"pilot_cutoff"`
}

type CalcLimitAndKDN struct {
	Stage1 Stage1 `json:"Stage1" bson:"Stage1"`
	Stage2 Stage2 `json:"Stage2" bson:"Stage2"`
	Stage3 Stage3 `json:"Stage3" bson:"Stage3"`
}

type Stage1 struct {
	CalculatedLimit         float64 `json:"calculated_limit" bson:"calculated_limit"`
	CalculatedPeriod        int32   `json:"calculated_period" bson:"calculated_period"`
	CalculatedPmt           float64 `json:"calculated_pmt" bson:"calculated_pmt"`
	ChargesHalyk            float64 `json:"charges_halyk" bson:"charges_halyk"`
	ChargesKaspi            float64 `json:"charges_kaspi" bson:"charges_kaspi"`
	CntDistinctPeriods      int32   `json:"cnt_distinct_periods" bson:"cnt_distinct_periods"`
	Commissions             float64 `json:"commissions" bson:"commissions"`
	CreditDecision          string  `json:"credit_decision" bson:"credit_decision"`
	DateRetirement          string  `json:"date_retirement" bson:"date_retirement"`
	DateToday               string  `json:"date_today" bson:"date_today"`
	Expenses                float64 `json:"expenses" bson:"expenses"`
	IncomePkb               float64 `json:"income_pkb" bson:"income_pkb"`
	IncomeStage             float64 `json:"income_stage" bson:"income_stage"`
	IncomeTransactionsHalyk float64 `json:"income_transactions_halyk" bson:"income_transactions_halyk"`
	IncomeTransactionsKaspi float64 `json:"income_transactions_kaspi" bson:"income_transactions_kaspi"`
	IncomeTransactionsSum   float64 `json:"income_transactions_sum" bson:"income_transactions_sum"`
	MaxPeriod               int32   `json:"max_period" bson:"max_period"`
	MaxTermMnth             int32   `json:"max_term_mnth" bson:"max_term_mnth"`
	MaxTransactionsHalyk    float64 `json:"max_transactions_halyk" bson:"max_transactions_halyk"`
	MaxTransactionsKaspi    float64 `json:"max_transactions_kaspi" bson:"max_transactions_kaspi"`
	MinTermMnth             int32   `json:"min_term_mnth" bson:"min_term_mnth"`
	NumberOfChildren        int32   `json:"number_of_children" bson:"number_of_children"`
	PaymentSum              float64 `json:"paymentSum" bson:"paymentSum"`
	Pm                      float64 `json:"pm" bson:"pm"`
	Purchases               float64 `json:"purchases" bson:"purchases"`
	Receipts                float64 `json:"receipts" bson:"receipts"`
	RejectReason            string  `json:"reject_reason" bson:"reject_reason"`
	Replenishment           float64 `json:"replenishment" bson:"replenishment"`
	Transfers               float64 `json:"transfers" bson:"transfers"`
	Withdrawals             float64 `json:"withdrawals" bson:"withdrawals"`
}

type Stage2 struct {
	CalculatedLimit                  float64 `json:"calculated_limit" bson:"calculated_limit"`
	CalculatedPeriod                 int32   `json:"calculated_period" bson:"calculated_period"`
	CalculatedPmt                    float64 `json:"calculated_pmt" bson:"calculated_pmt"`
	ChargesPkb                       float64 `json:"charges_pkb" bson:"charges_pkb"`
	CreditDecision                   string  `json:"credit_decision" bson:"credit_decision"`
	IncomeStage                      float64 `json:"income_stage" bson:"income_stage"`
	K                                float64 `json:"K" bson:"K"`
	KMax                             float64 `json:"K_max" bson:"K_max"`
	Kdn                              float64 `json:"kdn" bson:"kdn"`
	KdnMax                           float64 `json:"kdn_max" bson:"kdn_max"`
	Markup                           float64 `json:"markup" bson:"markup"`
	MarkupMnth                       float64 `json:"markup_mnth" bson:"markup_mnth"`
	MaxPeriod                        int32   `json:"max_period" bson:"max_period"`
	MaxPmt                           float64 `json:"max_pmt" bson:"max_pmt"`
	MinPmtMinAmount                  float64 `json:"min_pmt_min_amount" bson:"min_pmt_min_amount"`
	MonthlyInstalmentAmountConverted float64 `json:"MonthlyInstalmentAmountConverted" bson:"MonthlyInstalmentAmountConverted"`
	MonthlyCharges                   float64 `json:"monthly_charges" bson:"monthly_charges"`
	OverduePkb                       float64 `json:"overdue_pkb" bson:"overdue_pkb"`
	PTICoef                          float64 `json:"PTI_coef" bson:"PTI_coef"`
	RejectReason                     string  `json:"reject_reason" bson:"reject_reason"`
	RequestedAmount                  float64 `json:"requested_amount" bson:"requested_amount"`
	RequestedPeriod                  int32   `json:"requested_period" bson:"requested_period"`
	Score                            float64 `json:"score" bson:"score"`
	TmpCalculatedLimit               float64 `json:"tmp_calculated_limit" bson:"tmp_calculated_limit"`
	TmpPeriod                        int32   `json:"tmp_period" bson:"tmp_period"`
	TmpPmt                           float64 `json:"tmp_pmt" bson:"tmp_pmt"`
}

type Stage3 struct {
	CalculatedKdd                    float64 `json:"calculated_kdd" bson:"calculated_kdd"`
	CalculatedKdn                    float64 `json:"calculated_kdn" bson:"calculated_kdn"`
	CalculatedLimit                  float64 `json:"calculated_limit" bson:"calculated_limit"`
	CalculatedPeriod                 int32   `json:"calculated_period" bson:"calculated_period"`
	CalculatedPmt                    float64 `json:"calculated_pmt" bson:"calculated_pmt"`
	ChargesPkb                       float64 `json:"charges_pkb" bson:"charges_pkb"`
	CreditDecision                   string  `json:"credit_decision" bson:"credit_decision"`
	IncomeStage                      float64 `json:"income_stage" bson:"income_stage"`
	K                                float64 `json:"K" bson:"K"`
	KLim                             float64 `json:"K_lim" bson:"K_lim"`
	Ki                               int8    `json:"ki" bson:"ki"`
	MarkupMnth                       float64 `json:"markup_mnth" bson:"markup_mnth"`
	MaxLimit                         float64 `json:"max_limit" bson:"max_limit"`
	MinLimit                         float64 `json:"min_limit" bson:"min_limit"`
	NumberOfOverdueInstalmentsMaxAll int32   `json:"number_of_overdue_instalments_max_all" bson:"number_of_overdue_instalments_max_all"`
	NumberOfOverdueInstalmentsMax3M  int32   `json:"number_of_overdue_instalments_max_3m" bson:"number_of_overdue_instalments_max_3m"`
	NumberOfOverdueInstalmentsMax6M  int32   `json:"number_of_overdue_instalments_max_6m" bson:"number_of_overdue_instalments_max_6m"`
	OverduePkb                       float64 `json:"overdue_pkb" bson:"overdue_pkb"`
	RejectReason                     string  `json:"reject_reason" bson:"reject_reason"`
	RequestedAmount                  float64 `json:"requested_amount" bson:"requested_amount"`
	StepLimit                        float64 `json:"step_limit" bson:"step_limit"`
	TmpCalculatedLimit               float64 `json:"tmp_calculated_limit" bson:"tmp_calculated_limit"`
	TmpPeriod                        int32   `json:"tmp_period" bson:"tmp_period"`
	TmpPmt                           float64 `json:"tmp_pmt" bson:"tmp_pmt"`
	TotalUnpaidSum                   float64 `json:"total_unpaid_sum" bson:"total_unpaid_sum"`
	UnpaidSum                        float64 `json:"unpaid_sum" bson:"unpaid_sum"`
}
