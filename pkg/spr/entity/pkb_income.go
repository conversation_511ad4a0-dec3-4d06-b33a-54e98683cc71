package entity

type PkbIncome struct {
	ExtDataPkbIncome                   []ExtDataPkbIncome                   `json:"ext_data_pkb_income" bson:"ext_data_pkb_income"`
	ExtDataPkbIncomeDeductionsDetailed []ExtDataPkbIncomeDeductionsDetailed `json:"ext_data_pkb_income_deductions_detailed" bson:"ext_data_pkb_income_deductions_detailed"`
	ExtDataPkbIncomeRefund             []ExtDataPkbIncomeRefund             `json:"ext_data_pkb_income_refund" bson:"ext_data_pkb_income_refund"`
}

type ExtDataPkbIncome struct {
	MessageID          string `json:"message_id" bson:"message_id"`
	ExtDataPkbIncomeID string `json:"ext_data_pkb_income_id" bson:"ext_data_pkb_income_id"`
	RequestLogID       int    `json:"request_log_id" bson:"request_log_id"`
	CorrelationID      string `json:"correlation_id" bson:"correlation_id"`
	StatusCode         string `json:"status_code" bson:"status_code"`
	StatusMessage      string `json:"status_message" bson:"status_message"`
	SessionID          string `json:"session_id" bson:"session_id"`
	RequestIIN         string `json:"request_iin" bson:"request_iin"`
	RequestNumber      string `json:"request_number" bson:"request_number"`
	ResponseDate       string `json:"response_date" bson:"response_date"`
	ResponseNumber     string `json:"response_number" bson:"response_number"`
	ResponseCode       string `json:"response_code" bson:"response_code"`
	RefundPresence     int    `json:"refund_presence" bson:"refund_presence"`
}

type ExtDataPkbIncomeDeductionsDetailed struct {
	ExtDataPkbIncomeID string  `json:"ext_data_pkb_income_id" bson:"ext_data_pkb_income_id"`
	Bin                string  `json:"bin" bson:"bin"`
	Date               string  `json:"date" bson:"date"`
	Name               string  `json:"name" bson:"name"`
	PaymentSum         float64 `json:"payment_sum" bson:"payment_sum"`
	PaymentPeriod      string  `json:"payment_period" bson:"payment_period"`
}

type ExtDataPkbIncomeRefund struct {
	ExtDataPKBIncomeID string  `json:"ext_data_pkb_income_id" bson:"ext_data_pkb_income_id"`
	RefundStatus       string  `json:"refund_status" bson:"refund_status"`
	RefundType         string  `json:"refund_type" bson:"refund_type"`
	RefundDate         string  `json:"refund_date" bson:"refund_date"`
	RefundPeriod       string  `json:"refund_period" bson:"refund_period"`
	RefundSumm         float64 `json:"refund_summ" bson:"refund_summ"`
}
