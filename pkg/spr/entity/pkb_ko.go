//nolint:misspell
package entity

type PkbKO struct {
	ExtDataPkbReport                                      []ExtDataPkbReport                                      `json:"ext_data_pkb_report" bson:"ext_data_pkb_report"`
	ExtDataPkbReportContracts                             []ExtDataPkbReportContracts                             `json:"ext_data_pkb_report_contracts" bson:"ext_data_pkb_report_contracts"`
	ExtDataPkbReportCollateral                            []ExtDataPkbReportCollateral                            `json:"ext_data_pkb_report_collateral" bson:"ext_data_pkb_report_collateral"`
	ExtDataPkbReportPaymentsCalendar                      []ExtDataPkbReportPaymentsCalendar                      `json:"ext_data_pkb_report_payments_calendar" bson:"ext_data_pkb_report_payments_calendar"`
	ExtDataPkbReportIdentificationDocuments               []ExtDataPkbReportIdentificationDocuments               `json:"ext_data_pkb_report_identification_documents" bson:"ext_data_pkb_report_identification_documents"`
	ExtDataPkbReportInterconnectedSubject                 []ExtDataPkbReportInterconnectedSubject                 `json:"ext_data_pkb_report_interconnected_subject" bson:"ext_data_pkb_report_interconnected_subject"`
	ExtDataPkbReportNegativeDataClient                    []ExtDataPkbReportNegativeDataClient                    `json:"ext_data_pkb_report_negative_data_client" bson:"ext_data_pkb_report_negative_data_client"`
	ExtDataPkbReportNegativeDataContract                  []ExtDataPkbReportNegativeDataContract                  `json:"ext_data_pkb_report_negative_data_contract" bson:"ext_data_pkb_report_negative_data_contract"`
	ExtDataPkbReportNumberOfQueries                       []ExtDataPkbReportNumberOfQueries                       `json:"ext_data_pkb_report_number_of_queries" bson:"ext_data_pkb_report_number_of_queries"`
	ExtDataPkbReportPublicSources                         []ExtDataPkbReportPublicSources                         `json:"ext_data_pkb_report_public_sources" bson:"ext_data_pkb_report_public_sources"`
	ExtDataPkbReportRWA170Contract                        []ExtDataPkbReportRWA170Contract                        `json:"ext_data_pkb_report_RWA170_contract" bson:"ext_data_pkb_report_RWA170_contract"`
	ExtDataPkbReportSubjectsAddress                       []ExtDataPkbReportSubjectsAddress                       `json:"ext_data_pkb_report_subjects_address" bson:"ext_data_pkb_report_subjects_address"`
	ExtDataPkbReportSummaryInformationContracts           []ExtDataPkbReportSummaryInformationContract            `json:"ext_data_pkb_report_summary_information_contracts" bson:"ext_data_pkb_report_summary_information_contracts"`
	ExtDataPkbReportSummaryInformationContractStatuses    []ExtDataPkbReportSummaryInformationContractStatuses    `json:"ext_data_pkb_report_summary_information_contract_statuses" bson:"ext_data_pkb_report_summary_information_contract_statuses"`
	ExtDataPkbReportContractsSubjectRole                  []ExtDataPkbReportContractsSubjectRole                  `json:"ext_data_pkb_report_contracts_subject_role" bson:"ext_data_pkb_report_contracts_subject_role"`
	ExtDataPkbReportContractsParent                       []ExtDataPkbReportContractsParent                       `json:"ext_data_pkb_report_contracts_parent" bson:"ext_data_pkb_report_contracts_parent"`
	ExtDataPkbReportBankrupt                              []ExtDataPkbReportBankrupt                              `json:"ext_data_pkb_report_bankrupt" bson:"ext_data_pkb_report_bankrupt"`
	ExtDataPkbReportRelatedCompaniesDocuments             []ExtDataPkbReportRelatedCompaniesDocuments             `json:"ext_data_pkb_report_related_companies_documents" bson:"ext_data_pkb_report_related_companies_documents"`
	ExtDataPkbReportRelatedCompanies                      []ExtDataPkbReportRelatedCompanies                      `json:"ext_data_pkb_report_related_companies" bson:"ext_data_pkb_report_related_companies"`
	ExtDataPkbReportContractsRehabilitation               []ExtDataPkbReportContractsRehabilitation               `json:"ext_data_pkb_report_contracts_rehabilitation" bson:"ext_data_pkb_report_contracts_rehabilitation"`
	ExtDataPkbReportContractsProlongation                 []ExtDataPkbReportContractsProlongation                 `json:"ext_data_pkb_report_contracts_prolongation" bson:"ext_data_pkb_report_contracts_prolongation"`
	ExtDataPkbReportApplications                          []ExtDataPkbReportApplications                          `json:"ext_data_pkb_report_applications" bson:"ext_data_pkb_report_applications"`
	ExtDataPkbReportSummaryInformationApplicationStatuses []ExtDataPkbReportSummaryInformationApplicationStatuses `json:"ext_data_pkb_report_summary_information_application_statuses" bson:"ext_data_pkb_report_summary_information_application_statuses"`
	ExtDataPkbReportSummaryInformationApplications        []ExtDataPkbReportSummaryInformationApplications        `json:"ext_data_pkb_report_summary_information_applications" bson:"ext_data_pkb_report_summary_information_applications"`
	ExtDataPkbReportSUSN                                  []ExtDataPkbReportSUSN                                  `json:"ext_data_pkb_report_susn" bson:"ext_data_pkb_report_susn"`
}

type ExtDataPkbReport struct {
	ExtDataPkbReportID                int     `json:"ext_data_pkb_report_id" bson:"ext_data_pkb_report_id"`
	RequestLogID                      int     `json:"request_log_id" bson:"request_log_id"`
	Datetime                          string  `json:"datetime" bson:"datetime"`
	ReferenceID                       int     `json:"reference_id" bson:"reference_id"`
	ServiceName                       string  `json:"service_name" bson:"service_name"`
	ResultCulture                     string  `json:"result_culture" bson:"result_culture"`
	ResultCode                        int     `json:"result_code" bson:"result_code"`
	ResultDescription                 string  `json:"result_description" bson:"result_description"`
	UsageIdentity                     string  `json:"usage_identity" bson:"usage_identity"`
	Intitle                           string  `json:"intitle" bson:"intitle"`
	TitleName                         string  `json:"title_name" bson:"title_name"`
	Title                             string  `json:"title" bson:"title"`
	Sems                              string  `json:"sems" bson:"sems"`
	HeaderEntityType                  string  `json:"header_entity_type" bson:"header_entity_type"`
	HeaderReportCode                  string  `json:"header_report_code" bson:"header_report_code"`
	HeaderIntitle                     string  `json:"header_intitle" bson:"header_intitle"`
	HeaderName                        string  `json:"header_name" bson:"header_name"`
	HeaderStitle                      string  `json:"header_stitle" bson:"header_stitle"`
	HeaderSubject                     string  `json:"header_subject" bson:"header_subject"`
	HeaderTitle                       string  `json:"header_title" bson:"header_title"`
	FiUniqueNumberID                  string  `json:"fi_unique_number_id" bson:"fi_unique_number_id"`
	FiUniqueNumberidID                string  `json:"fi_unique_numberid_id" bson:"fi_unique_numberid_id"`
	RegistrationID                    int64   `json:"registration_id" bson:"registration_id"`
	Rnn                               int64   `json:"rnn" bson:"rnn"`
	RnnID                             int     `json:"rnn_id" bson:"rnn_id"`
	Sic                               string  `json:"sic" bson:"sic"`
	SicID                             int     `json:"sic_id" bson:"sic_id"`
	Iin                               string  `json:"iin" bson:"iin"`
	IinID                             int     `json:"iin_id" bson:"iin_id"`
	DateOfBirth                       string  `json:"date_of_birth" bson:"date_of_birth"` // Использован string вместо Date
	Gender                            string  `json:"gender" bson:"gender"`
	Surname                           string  `json:"surname" bson:"surname"`
	Name                              string  `json:"name" bson:"name"`
	FathersName                       string  `json:"fathers_name" bson:"fathers_name"`
	BirthName                         string  `json:"birth_name" bson:"birth_name"`
	CityOfBirth                       string  `json:"city_of_birth" bson:"city_of_birth"`
	Education                         string  `json:"education" bson:"education"`
	EducationID                       int     `json:"education_id" bson:"education_id"`
	MatrialStatus                     string  `json:"matrial_status" bson:"matrial_status"`
	MatrialStatusID                   int     `json:"matrial_status_id" bson:"matrial_status_id"`
	RegionOfBirth                     string  `json:"region_of_birth" bson:"region_of_birth"`
	CountryOfBirth                    string  `json:"country_of_birth" bson:"country_of_birth"`
	SubjectName                       string  `json:"subject_name" bson:"subject_name"`
	SubjectStitle                     string  `json:"subject_stitle" bson:"subject_stitle"`
	SubjectTitle                      string  `json:"subject_title" bson:"subject_title"`
	NumberOfDependents1               int32   `json:"number_of_dependents1" bson:"number_of_dependents1"`
	NumberOfChildren1                 int32   `json:"number_of_children1" bson:"number_of_children1"`
	NumberOfDependents2               int32   `json:"number_of_dependents2" bson:"number_of_dependents2"`
	NumberOfChildren2                 int32   `json:"number_of_children2" bson:"number_of_children2"`
	NumberOfDependents3               int32   `json:"number_of_dependents3" bson:"number_of_dependents3"`
	StreetAddress                     string  `json:"street_address" bson:"street_address"`
	Street                            string  `json:"street" bson:"street"`
	City                              string  `json:"city" bson:"city"`
	ZipCode                           string  `json:"zip_code" bson:"zip_code"`
	Region                            string  `json:"region" bson:"region"`
	Country                           string  `json:"country" bson:"country"`
	Number                            string  `json:"number" bson:"number"`
	CellularPhone                     string  `json:"cellular_phone" bson:"cellular_phone"`
	HomePhone                         string  `json:"home_phone" bson:"home_phone"`
	OfficePhone                       string  `json:"office_phone" bson:"office_phone"`
	Fax                               string  `json:"fax" bson:"fax"`
	Email                             string  `json:"email" bson:"email"`
	EmployeesSalary                   string  `json:"employees_salary" bson:"employees_salary"`
	ClassificationName                string  `json:"classification_name" bson:"classification_name"`
	ClassificationTitle               string  `json:"classification_title" bson:"classification_title"`
	BorrowerClassification            string  `json:"borrower_classification" bson:"borrower_classification"`
	BorrowerClassificationID          int     `json:"borrower_classification_id" bson:"borrower_classification_id"`
	Patent                            string  `json:"patent" bson:"patent"`
	Resident                          string  `json:"resident" bson:"resident"`
	ResidentID                        int     `json:"resident_id" bson:"resident_id"`
	SubjectsPosition                  string  `json:"subjects_position" bson:"subjects_position"`
	Citizenship                       string  `json:"citizenship" bson:"citizenship"`
	CitizenshipID                     int     `json:"citizenship_id" bson:"citizenship_id"`
	SubjectsEmployment                string  `json:"subjects_employment" bson:"subjects_employment"`
	ForeignersCitizenship             string  `json:"foreigners_citizenship" bson:"foreigners_citizenship"`
	EconomicActivityGroup             string  `json:"economic_activity_group" bson:"economic_activity_group"`
	NumberOfApplicationsTitle         string  `json:"number_of_applications_title" bson:"number_of_applications_title"`
	NumberOfApplicationsType          string  `json:"number_of_applications_type" bson:"number_of_applications_type"`
	NumberOfApplicationsValue         string  `json:"number_of_applications_value" bson:"number_of_applications_value"`
	NumberOfApplicationsWeek          int     `json:"number_of_applications_week" bson:"number_of_applications_week"`
	NumberOfApplicationsMonth         int     `json:"number_of_applications_month" bson:"number_of_applications_month"`
	NumberOfApplicationsQuarter       int     `json:"number_of_applications_quarter" bson:"number_of_applications_quarter"`
	NumberOfApplicationsYear          int     `json:"number_of_applications_year" bson:"number_of_applications_year"`
	NumberOfApplicationsLastThreeYear int     `json:"number_of_applications_last_three_year" bson:"number_of_applications_last_three_year"`
	NumberOfInquiriesValue            int     `json:"number_of_inquiries_value" bson:"number_of_inquiries_value"`
	NumberOfInquiriesFirstQuarter     int     `json:"number_of_inquiries_first_quarter" bson:"number_of_inquiries_first_quarter"`
	NumberOfInquiriesSecondQuarter    int     `json:"number_of_inquiries_second_quarter" bson:"number_of_inquiries_second_quarter"`
	NumberOfInquiriesThirdQuarter     int     `json:"number_of_inquiries_third_quarter" bson:"number_of_inquiries_third_quarter"`
	NumberOfInquiriesFourthQuarter    int     `json:"number_of_inquiries_fourth_quarter" bson:"number_of_inquiries_fourth_quarter"`
	StopCredit                        int     `json:"stop_credit" bson:"stop_credit"`
	GamblerFeature                    int     `json:"gambler_feature" bson:"gambler_feature"`
	GamblerCount                      int     `json:"gambler_count" bson:"gambler_count"`
	GamblerSumm                       float64 `json:"gambler_summ" bson:"gambler_summ"`
	ErrorCode                         int     `json:"error_code" bson:"error_code"`
}

type ExtDataPkbReportContracts struct {
	ExtDataPkbReportID                  int     `json:"ext_data_pkb_report_id" bson:"ext_data_pkb_report_id"`
	Type                                string  `json:"type" bson:"type"`
	ContractUID                         string  `json:"contract_uid" bson:"contract_uid"`
	ContractType                        string  `json:"contract_type" bson:"contract_type"`
	ContractTypeCode                    string  `json:"contract_type_code" bson:"contract_type_code"`
	Currency                            string  `json:"currency" bson:"currency"`
	CodeOfContract                      string  `json:"code_of_contract" bson:"code_of_contract"`
	AgreementNumber                     string  `json:"agreement_number" bson:"agreement_number"`
	AgreementNumberGuarantee            string  `json:"agreement_number_guarantee" bson:"agreement_number_guarantee"`
	TypeOfFounding                      string  `json:"type_of_founding" bson:"type_of_founding"`
	TypeOfFoundingID                    int     `json:"type_of_founding_id" bson:"type_of_founding_id"`
	PurposeOfCredit                     string  `json:"purpose_of_credit" bson:"purpose_of_credit"`
	PurposeOfCreditID                   string  `json:"purpose_of_credit_id" bson:"purpose_of_credit_id"`
	CurrencyCode                        string  `json:"currency_code" bson:"currency_code"`
	ContractsPhase                      string  `json:"contracts_phase" bson:"contracts_phase"`
	ContractStatus                      string  `json:"contract_status" bson:"contract_status"`
	ContractStatusID                    int     `json:"contract_status_id" bson:"contract_status_id"`
	DateOfApplication                   string  `json:"date_of_application" bson:"date_of_application"` // Использован string вместо Date
	DateOfCreditStart                   string  `json:"date_of_creditstart" bson:"date_of_creditstart"`
	DateOfCreditEnd                     string  `json:"date_of_creditend" bson:"date_of_creditend"`
	DateOfRealRepayment                 string  `json:"date_of_real_repayment" bson:"date_of_real_repayment"`
	DateAgreementGuarantee              string  `json:"date_agreement_guarantee" bson:"date_agreement_guarantee"`
	GuaranteeEvent                      string  `json:"guarantee_event" bson:"guarantee_event"`
	LastUpdate                          string  `json:"last_update" bson:"last_update"`
	ClassificationOfContract            string  `json:"classification_of_contract" bson:"classification_of_contract"`
	TotalAmount                         float64 `json:"total_amount" bson:"total_amount"`
	Amount                              float64 `json:"amount" bson:"amount"`
	CreditUsage                         string  `json:"credit_usage" bson:"credit_usage"`
	CreditUsageID                       string  `json:"credit_usage_id" bson:"credit_usage_id"`
	ResidualAmount                      float64 `json:"residual_amount" bson:"residual_amount"`
	CreditLimit                         float64 `json:"credit_limit" bson:"credit_limit"`
	NumberOfOutstandingInstalments      int     `json:"number_of_outstanding_instalments" bson:"number_of_outstanding_instalments"`
	NumberOfInstalments                 int     `json:"number_of_instalments" bson:"number_of_instalments"`
	OutstandingAmount                   float64 `json:"outstanding_amount" bson:"outstanding_amount"`
	PeriodicityOfPayments               string  `json:"periodicity_of_payments" bson:"periodicity_of_payments"`
	PeriodicityOfPaymentsID             int     `json:"periodicity_of_payments_id" bson:"periodicity_of_payments_id"`
	MethodOfPayments                    string  `json:"method_of_payments" bson:"method_of_payments"`
	MethodOfPaymentsID                  int     `json:"method_of_payments_id" bson:"method_of_payments_id"`
	NumberOfOverdueInstalments          int     `json:"number_of_overdue_instalments" bson:"number_of_overdue_instalments"`
	OverdueAmount                       float64 `json:"overdue_amount" bson:"overdue_amount"`
	MonthlyInstalmentAmount             float64 `json:"monthly_instalment_amount" bson:"monthly_instalment_amount"`
	InterestRate                        float64 `json:"interest_rate" bson:"interest_rate"`
	SubjectRole                         string  `json:"subject_role" bson:"subject_role"`
	SubjectRoleNumber                   int     `json:"subject_role_number" bson:"subject_role_number"`
	FinancialInstitution                string  `json:"financial_institution" bson:"financial_institution"`
	FinancialInstitutionID              int     `json:"financial_institution_id" bson:"financial_institution_id"`
	SpecialRelationship                 string  `json:"special_relationship" bson:"special_relationship"`
	AnnualEffectiveRate                 float64 `json:"annual_effective_rate" bson:"annual_effective_rate"`
	NominalRate                         float64 `json:"nominal_rate" bson:"nominal_rate"`
	AmountProvisions                    float64 `json:"amount_provisions" bson:"amount_provisions"`
	LoanAccount                         string  `json:"loan_account" bson:"loan_account"`
	GracePrincipal                      string  `json:"grace_principal" bson:"grace_principal"`
	GracePay                            string  `json:"grace_pay" bson:"grace_pay"`
	PlaceOfDisbursement                 string  `json:"place_of_disbursement" bson:"place_of_disbursement"`
	PlaceOfDisbursementID               string  `json:"place_of_disbursement_id" bson:"place_of_disbursement_id"`
	ContractThirdParty                  string  `json:"contract_third_party" bson:"contract_third_party"`
	ParentContractCode                  string  `json:"parent_contract_code" bson:"parent_contract_code"`
	ParentProvider                      string  `json:"parent_provider" bson:"parent_provider"`
	ParentProviderID                    string  `json:"parent_provider_id" bson:"parent_provider_id"`
	ParentContractStatus                string  `json:"parent_contract_status" bson:"parent_contract_status"`
	ParentContractStatusID              string  `json:"parent_contract_status_id" bson:"parent_contract_status_id"`
	ParentOperationDate                 string  `json:"parent_operatio_ndate" bson:"parent_operatio_ndate"`
	Fine                                float64 `json:"fine" bson:"fine"`
	Penalty                             float64 `json:"penalty" bson:"penalty"`
	BranchLocation                      string  `json:"branch_location" bson:"branch_location"`
	ProlongationCount                   int     `json:"prolongation_count" bson:"prolongation_count"`
	NumberOfTransactions                int     `json:"number_of_transactions" bson:"number_of_transactions"`
	InstalmentAmount                    float64 `json:"instalment_amount" bson:"instalment_amount"`
	ActualDate                          string  `json:"actual_date" bson:"actual_date"`
	DateOfInserted                      string  `json:"date_of_inserted" bson:"date_of_inserted"`
	NumberOfOverdueInstalmentsMax       int     `json:"number_of_overdue_instalments_max" bson:"number_of_overdue_instalments_max"`
	NumberOfOverdueInstalmentsMaxDate   string  `json:"number_of_overdue_instalments_max_date" bson:"number_of_overdue_instalments_max_date"`
	NumberOfOverdueInstalmentsMaxAmount float64 `json:"number_of_overdue_instalments_max_amount" bson:"number_of_overdue_instalments_max_amount"`
	OverdueAmountMax                    float64 `json:"overdue_amount_max" bson:"overdue_amount_max"`
	OverdueAmountMaxDate                string  `json:"overdue_amount_max_date" bson:"overdue_amount_max_date"`
	OverdueAmountMaxCount               float64 `json:"overdue_amount_max_count" bson:"overdue_amount_max_count"`
	CreditObject                        string  `json:"credit_object" bson:"credit_object"`
	CreditObjectID                      string  `json:"credit_object_id" bson:"credit_object_id"`
	InterconnectedSubjects              string  `json:"interconnected_subjects" bson:"interconnected_subjects"`
	InterconnectedSubjectsName          string  `json:"interconnected_subjects_name" bson:"interconnected_subjects_name"`
	Schema                              string  `json:"schema" bson:"schema"`
	FinancialInstitutionBIN             string  `json:"financial_institution_bin" bson:"financial_institution_bin"`
	LastPaymentDate                     string  `json:"last_payment_date" bson:"last_payment_date"`
	SettlementStitle                    string  `json:"settlement_stitle" bson:"settlement_stitle"`
	SettlementApplicationDate           string  `json:"settlement_application_date" bson:"settlement_application_date"`
	SettlementResult                    string  `json:"settlement_result" bson:"settlement_result"`
	SettlementDecisionDate              string  `json:"settlement_decision_date" bson:"settlement_decision_date"`
	SettlementNewContractType           string  `json:"settlement_new_contract_type" bson:"settlement_new_contract_type"`
	SettlementNewContractNumber         string  `json:"settlement_new_contract_number" bson:"settlement_new_contract_number"`
	SettlementNewContractDate           string  `json:"settlement_new_contract_date" bson:"settlement_new_contract_date"`
	SettlementDebtCollection            string  `json:"settlement_debt_collection" bson:"settlement_debt_collection"`
	SettlementDebtCollectionDate        string  `json:"settlement_debt_collection_date" bson:"settlement_debt_collection_date"`
	SettlementDebtCollectionEndDate     string  `json:"settlement_debt_collection_end_date" bson:"settlement_debt_collection_end_date"`
	ThirdPartyHolderBIN                 string  `json:"third_party_holder_bin" bson:"third_party_holder_bin"`
	ThirdPartyHolderSum                 float64 `json:"third_party_holder_sum" bson:"third_party_holder_sum"`
	ThirdPartyHolderDate                string  `json:"third_party_holder_date" bson:"third_party_holder_date"`
	AvailableLimit                      float64 `json:"available_limit" bson:"available_limit"`
	RealPaymentDate                     string  `json:"real_payment_date" bson:"real_payment_date"`
}

type ExtDataPkbReportCollateral struct {
	ExtDataPkbReportID             int     `json:"ext_data_pkb_report_id" bson:"ext_data_pkb_report_id"`
	ExtDataPkbReportContractsID    int     `json:"ext_data_pkb_report_contracts_id" bson:"ext_data_pkb_report_contracts_id"`
	ExtDataPkbReportApplicationsID int     `json:"ext_data_pkb_report_applications_id" bson:"ext_data_pkb_report_applications_id"`
	ApplicationUID                 string  `json:"application_uid" bson:"application_uid"`
	ContractUID                    string  `json:"contract_uid" bson:"contract_uid"`
	TypeOfGuarantee                string  `json:"type_of_guarantee" bson:"type_of_guarantee"`
	ValueOfGuarantee               float64 `json:"value_of_guarantee" bson:"value_of_guarantee"`
	TypeOfValueOfGuarantee         string  `json:"type_of_value_of_guarantee" bson:"type_of_value_of_guarantee"`
	AdditionalInformation          string  `json:"additional_information" bson:"additional_information"`
	PlaceOfGuarantee               string  `json:"place_of_guarantee" bson:"place_of_guarantee"`
	TypeOfCollateral               string  `json:"type_of_collateral" bson:"type_of_collateral"`
	CollateralStatus               string  `json:"collateral_status" bson:"collateral_status"`
	CollateralName                 string  `json:"collateral_name" bson:"collateral_name"`
}

type ExtDataPkbReportPaymentsCalendar struct {
	ExtDataPkbReportID             int     `json:"ext_data_pkb_report_id" bson:"ext_data_pkb_report_id"`
	ExtDataPkbReportContractsID    int     `json:"ext_data_pkb_report_contracts_id" bson:"ext_data_pkb_report_contracts_id"`
	ExtDataPkbReportApplicationsID int     `json:"ext_data_pkb_report_applications_id" bson:"ext_data_pkb_report_applications_id"`
	ApplicationUID                 string  `json:"application_uid" bson:"application_uid"`
	ContractUID                    string  `json:"contract_uid" bson:"contract_uid"`
	Year                           int     `json:"year" bson:"year"`
	PaymentFine                    float64 `json:"payment_fine" bson:"payment_fine"`
	PaymentNumber                  int     `json:"payment_number" bson:"payment_number"`
	PaymentOverdue                 float64 `json:"payment_overdue" bson:"payment_overdue"`
	PaymentPenalty                 float64 `json:"payment_penalty" bson:"payment_penalty"`
	PaymentTitle                   string  `json:"payment_title" bson:"payment_title"`
	PaymentValue                   float64 `json:"payment_value" bson:"payment_value"`
}

type ExtDataPkbReportIdentificationDocuments struct {
	ExtDataPkbReportID int    `json:"ext_data_pkb_report_id" bson:"ext_data_pkb_report_id"`
	Rank               string `json:"rank" bson:"rank"`
	Stitle             string `json:"stitle" bson:"stitle"`
	Type               string `json:"type" bson:"type"`
	Name               string `json:"name" bson:"name"`
	NameID             int    `json:"name_id" bson:"name_id"`
	DateOfRegistration string `json:"date_of_registration" bson:"date_of_registration"`
	DateOfIssuance     string `json:"date_of_issuance" bson:"date_of_issuance"`
	DateOfExpiration   string `json:"date_of_expiration" bson:"date_of_expiration"`
	Number             string `json:"number" bson:"number"`
	IssuanceLocation   string `json:"issuance_location" bson:"issuance_location"`
	DateOfInserted     string `json:"date_of_inserted" bson:"date_of_inserted"`
}

type ExtDataPkbReportInterconnectedSubject struct {
	ExtDataPkbReportID int    `json:"ext_data_pkb_report_id" bson:"ext_data_pkb_report_id"`
	TypeOfLink         string `json:"type_of_link" bson:"type_of_link"`
	SubjectCode        string `json:"subject_code" bson:"subject_code"`
}

type ExtDataPkbReportNegativeDataClient struct {
	ExtDataPkbReportID     int    `json:"ext_data_pkb_report_id" bson:"ext_data_pkb_report_id"`
	Title                  string `json:"title" bson:"title"`
	Type                   string `json:"type" bson:"type"`
	TypeTitle              string `json:"type_title" bson:"type_title"`
	NegativeStatusUID      string `json:"negative_status_uid" bson:"negative_status_uid"`
	NegativeStatusOfClient string `json:"negative_status_of_client" bson:"negative_status_of_client"`
	RegistrationDate       string `json:"registration_date" bson:"registration_date"`
}

type ExtDataPkbReportNegativeDataContract struct {
	ExtDataPkbReportID         int    `json:"ext_data_pkb_report_id" bson:"ext_data_pkb_report_id"`
	Title                      string `json:"title" bson:"title"`
	Type                       string `json:"type" bson:"type"`
	TypeTitle                  string `json:"type_title" bson:"type_title"`
	NegativeStatusUID          string `json:"negative_status_uid" bson:"negative_status_uid"`
	NegativeStatusOfContract   string `json:"negative_status_of_contract" bson:"negative_status_of_contract"`
	RegistrationDate           string `json:"registration_date" bson:"registration_date"`
	NegativeStatusOfContractID int    `json:"negativestatus_of_contract_id" bson:"negativestatus_of_contract_id"`
	SubjectRole                string `json:"subject_role" bson:"subject_role"`
	SubjectRoleEn              string `json:"subject_role_en" bson:"subject_role_en"`
}

type ExtDataPkbReportNumberOfQueries struct {
	ExtDataPkbReportID  int `json:"ext_data_pkb_report_id" bson:"ext_data_pkb_report_id"`
	Value               int `json:"value" bson:"value"`
	DetailsQueries7Days int `json:"details_queries_7days" bson:"details_queries_7days"`
	Days30              int `json:"days_30" bson:"days_30"`
	Days90              int `json:"days_90" bson:"days_90"`
	Days120             int `json:"days_120" bson:"days_120"`
	Days180             int `json:"days_180" bson:"days_180"`
	Days360             int `json:"days_360" bson:"days_360"`
}

type ExtDataPkbReportPublicSources struct {
	ExtDataPkbReportID int    `json:"ext_data_pkb_report_id" bson:"ext_data_pkb_report_id"`
	Source             string `json:"source" bson:"source"`
	ActualDate         string `json:"actual_date" bson:"actual_date"`
	RefreshDate        string `json:"refresh_date" bson:"refresh_date"`
	Status             string `json:"status" bson:"status"`
	StatusID           int    `json:"status_id" bson:"status_id"`
	Companies          string `json:"companies" bson:"companies"`
}

type ExtDataPkbReportRWA170Contract struct {
	ExtDataPkbReportID int     `json:"ext_data_pkb_report_id" bson:"ext_data_pkb_report_id"`
	ContractsSumm      float64 `json:"contracts_summ" bson:"contracts_summ"`
	ContractCode       string  `json:"contract_code" bson:"contract_code"`
	ContractSumm       float64 `json:"contract_summ" bson:"contract_summ"`
}

type ExtDataPkbReportSubjectsAddress struct {
	ExtDataPkbReportID int    `json:"ext_data_pkb_report_id" bson:"ext_data_pkb_report_id"`
	AddressType        string `json:"address_type" bson:"address_type"`
	Street             string `json:"street" bson:"street"`
	Number             string `json:"number" bson:"number"`
	ZipCode            string `json:"zip_code" bson:"zip_code"`
	City               string `json:"city" bson:"city"`
	Country            string `json:"country" bson:"country"`
	Region             string `json:"region" bson:"region"`
	HomePhone          string `json:"home_phone" bson:"home_phone"`
	OfficePhone        string `json:"office_phone" bson:"office_phone"`
	Fax                string `json:"fax" bson:"fax"`
	CellularPhone      string `json:"cellularphone" bson:"cellularphone"`
	EmailAddress       string `json:"email_address" bson:"email_address"`
	WebPageAddress     string `json:"web_page_address" bson:"web_page_address"`
	AddressInserted    string `json:"address_inserted" bson:"address_inserted"`
	PostBox            string `json:"post_box" bson:"post_box"`
	AdditionalInfo     string `json:"additional_info" bson:"additional_info"`
	Title              string `json:"title" bson:"title"`
	Type               string `json:"type" bson:"type"`
}

type ExtDataPkbReportSummaryInformationContract struct {
	ExtDataPkbReportID   int     `json:"ext_data_pkb_report_id" bson:"ext_data_pkb_report_id"`
	ContractType         string  `json:"contract_type" bson:"contract_type"`
	SumInfoContractUID   string  `json:"sum_info_contract_uid" bson:"sum_info_contract_uid"`
	Title                string  `json:"title" bson:"title"`
	Type                 string  `json:"type" bson:"type"`
	NumberOfContracts    int     `json:"number_of_contracts" bson:"number_of_contracts"`
	NumberOfCreditlines  int     `json:"number_of_creditlines" bson:"number_of_creditlines"`
	TotalOutstandingDebt float64 `json:"total_outstanding_debt" bson:"total_outstanding_debt"`
	TotalDebtOverdue     float64 `json:"total_debt_overdue" bson:"total_debt_overdue"`
	TotalFine            float64 `json:"total_fine" bson:"total_fine"`
	TotalPenalty         float64 `json:"total_penalty" bson:"total_penalty"`
}

type ExtDataPkbReportSummaryInformationContractStatuses struct {
	ExtDataPkbReportID            int    `json:"ext_data_pkb_report_id" bson:"ext_data_pkb_report_id"`
	SummaryInformationContractsID int    `json:"summary_information_contracts_id" bson:"summary_information_contracts_id"`
	SumInfoContractUID            string `json:"sum_info_contract_uid" bson:"sum_info_contract_uid"`
	Count                         int    `json:"count" bson:"count"`
	Value                         string `json:"value" bson:"value"`
}

type ExtDataPkbReportContractsSubjectRole struct {
	ExtDataPkbReportID             int    `json:"ext_data_pkb_report_id" bson:"ext_data_pkb_report_id"`
	ExtDataPkbReportContractsID    int    `json:"ext_data_pkb_report_contracts_id" bson:"ext_data_pkb_report_contracts_id"`
	ExtDataPkbReportApplicationsID int    `json:"ext_data_pkb_report_applications_id" bson:"ext_data_pkb_report_applications_id"`
	ContractUID                    string `json:"contract_uid" bson:"contract_uid"`
	ApplicationUID                 string `json:"application_uid" bson:"application_uid"`
	SubjectRole                    string `json:"subject_role" bson:"subject_role"`
	SubjectRoleNumber              int    `json:"subject_role_number" bson:"subject_role_number"`
}

type ExtDataPkbReportContractsParent struct {
	ExtDataPkbReportID     int     `json:"ext_data_pkb_report_id" bson:"ext_data_pkb_report_id"`
	ContractUID            string  `json:"contract_uid" bson:"contract_uid"`
	ProviderName           string  `json:"provider_name" bson:"provider_name"`
	ProviderBIN            string  `json:"provider_bin" bson:"provider_bin"`
	ContractDate           string  `json:"contract_date" bson:"contract_date"`
	ContractCode           string  `json:"contract_code" bson:"contract_code"`
	OperationDate          string  `json:"operation_date" bson:"operation_date"`
	OperationSum           float64 `json:"operation_sum" bson:"operation_sum"`
	ParentOperationRealSum float64 `json:"parent_operation_real_sum" bson:"parent_operation_real_sum"`
}

type ExtDataPkbReportBankrupt struct {
	ExtDataPkbReportID       int    `json:"ext_data_pkb_report_id" bson:"ext_data_pkb_report_id"`
	IIN                      string `json:"iin" bson:"iin"`
	FIO                      string `json:"fio" bson:"fio"`
	BankruptcyType           string `json:"bankruptcy_type" bson:"bankruptcy_type"`
	StatementNumber          string `json:"statement_number" bson:"statement_number"`
	StatementStatus          string `json:"statement_status" bson:"statement_status"`
	LastModificationDate     string `json:"last_modification_date" bson:"last_modification_date"`
	StatementApplicationDate string `json:"statement_application_date" bson:"statement_application_date"`
}

type ExtDataPkbReportRelatedCompaniesDocuments struct {
	ExtDataPkbReportID                 int    `json:"ext_data_pkb_report_id" bson:"ext_data_pkb_report_id"`
	ExtDataPkbReportRelatedCompaniesID int    `json:"ext_data_pkb_report_related_companies_id" bson:"ext_data_pkb_report_related_companies_id"`
	DocumentType                       string `json:"document_type" bson:"document_type"`
	DocumentNumber                     string `json:"document_number" bson:"document_number"`
}

type ExtDataPkbReportRelatedCompanies struct {
	ExtDataPkbReportID int    `json:"ext_data_pkb_report_id" bson:"ext_data_pkb_report_id"`
	SubjectRole        string `json:"subject_role" bson:"subject_role"`
	SubjectRoleID      int    `json:"subject_role_id" bson:"subject_role_id"`
	CompanyID          string `json:"company_id" bson:"company_id"`
	CompanyName        string `json:"company_name" bson:"company_name"`
}

type ExtDataPkbReportContractsRehabilitation struct {
	ExtDataPkbReportID          int    `json:"ext_data_pkb_report_id" bson:"ext_data_pkb_report_id"`
	ExtDataPkbReportContractsID int    `json:"ext_data_pkb_report_contracts_id" bson:"ext_data_pkb_report_contracts_id"`
	ContractUID                 string `json:"contract_uid" bson:"contract_uid"`
	StatusRehabilitation        string `json:"status_rehabilitation" bson:"status_rehabilitation"`
	StatusRehabilitationID      int    `json:"status_rehabilitation_id" bson:"status_rehabilitation_id"`
	DateReabilitate             string `json:"date_reabilitate" bson:"date_reabilitate"`
	CalcDate                    string `json:"calc_date" bson:"calc_date"`
}

type ExtDataPkbReportContractsProlongation struct {
	ExtDataPkbReportID          int    `json:"ext_data_pkb_report_id" bson:"ext_data_pkb_report_id"`
	ExtDataPkbReportContractsID int    `json:"ext_data_pkb_report_contracts_id" bson:"ext_data_pkb_report_contracts_id"`
	ContractUID                 string `json:"contract_uid" bson:"contract_uid"`
	ProlongationStartDate       string `json:"prolongation_start_date" bson:"prolongation_start_date"`
	ProlongationEndDate         string `json:"prolongation_end_date" bson:"prolongation_end_date"`
}

type ExtDataPkbReportApplications struct {
	ExtDataPkbReportID                  int     `json:"ext_data_pkb_report_id" bson:"ext_data_pkb_report_id"`
	ContractUID                         string  `json:"contract_uid" bson:"contract_uid"`
	Type                                string  `json:"type" bson:"type"`
	ContractType                        string  `json:"contract_type" bson:"contract_type"`
	ContractTypeCode                    string  `json:"contract_type_code" bson:"contract_type_code"`
	Currency                            string  `json:"currency" bson:"currency"`
	CodeOfContract                      string  `json:"code_of_contract" bson:"code_of_contract"`
	AgreementNumber                     string  `json:"agreement_number" bson:"agreement_number"`
	AgreementNumberGuarantee            string  `json:"agreement_number_guarantee" bson:"agreement_number_guarantee"`
	TypeOfFounding                      string  `json:"type_of_founding" bson:"type_of_founding"`
	TypeOfFoundingID                    int     `json:"type_of_founding_id" bson:"type_of_founding_id"`
	PurposeOfCredit                     string  `json:"purpose_of_credit" bson:"purpose_of_credit"`
	PurposeOfCreditID                   string  `json:"purpose_of_credit_id" bson:"purpose_of_credit_id"`
	CurrencyCode                        string  `json:"currency_code" bson:"currency_code"`
	ContractStatus                      string  `json:"contract_status" bson:"contract_status"`
	ContractStatusID                    int     `json:"contract_status_id" bson:"contract_status_id"`
	DateOfApplication                   string  `json:"date_of_application" bson:"date_of_application"`
	DateOfCreditStart                   string  `json:"date_of_creditstart" bson:"date_of_creditstart"`
	DateOfCreditEnd                     string  `json:"date_of_creditend" bson:"date_of_creditend"`
	DateOfRealRepayment                 string  `json:"date_of_real_repayment" bson:"date_of_real_repayment"`
	DateAgreementGuarantee              string  `json:"date_agreement_guarantee" bson:"date_agreement_guarantee"`
	GuaranteeEvent                      string  `json:"guarantee_event" bson:"guarantee_event"`
	LastUpdate                          string  `json:"last_update" bson:"last_update"`
	ClassificationOfContract            string  `json:"classification_of_contract" bson:"classification_of_contract"`
	TotalAmount                         float64 `json:"total_amount" bson:"total_amount"`
	Amount                              float64 `json:"amount" bson:"amount"`
	CreditUsage                         string  `json:"credit_usage" bson:"credit_usage"`
	CreditUsageID                       string  `json:"credit_usage_id" bson:"credit_usage_id"`
	ResidualAmount                      float64 `json:"residual_amount" bson:"residual_amount"`
	CreditLimit                         float64 `json:"credit_limit" bson:"credit_limit"`
	NumberOfOutstandingInstalments      int     `json:"number_of_outstanding_instalments" bson:"number_of_outstanding_instalments"`
	NumberOfInstalments                 int     `json:"number_of_instalments" bson:"number_of_instalments"`
	OutstandingAmount                   float64 `json:"outstanding_amount" bson:"outstanding_amount"`
	PeriodicityOfPayments               string  `json:"periodicity_of_payments" bson:"periodicity_of_payments"`
	PeriodicityOfPaymentsID             int     `json:"periodicity_of_payments_id" bson:"periodicity_of_payments_id"`
	MethodOfPayments                    string  `json:"method_of_payments" bson:"method_of_payments"`
	MethodOfPaymentsID                  int     `json:"method_of_payments_id" bson:"method_of_payments_id"`
	NumberOfOverdueInstalments          int     `json:"number_of_overdue_instalments" bson:"number_of_overdue_instalments"`
	OverdueAmount                       float64 `json:"overdue_amount" bson:"overdue_amount"`
	MonthlyInstalmentAmount             float64 `json:"monthly_instalment_amount" bson:"monthly_instalment_amount"`
	InterestRate                        float64 `json:"interest_rate" bson:"interest_rate"`
	SubjectRole                         string  `json:"subject_role" bson:"subject_role"`
	SubjectRoleNumber                   int     `json:"subject_role_number" bson:"subject_role_number"`
	FinancialInstitution                string  `json:"financial_institution" bson:"financial_institution"`
	FinancialInstitutionID              int     `json:"financial_institution_id" bson:"financial_institution_id"`
	SpecialRelationship                 string  `json:"special_relationship" bson:"special_relationship"`
	AnnualEffectiveRate                 float64 `json:"annual_effective_rate" bson:"annual_effective_rate"`
	NominalRate                         float64 `json:"nominal_rate" bson:"nominal_rate"`
	AmountProvisions                    float64 `json:"amount_provisions" bson:"amount_provisions"`
	LoanAccount                         string  `json:"loan_account" bson:"loan_account"`
	GracePrincipal                      string  `json:"grace_principal" bson:"grace_principal"`
	GracePay                            string  `json:"grace_pay" bson:"grace_pay"`
	PlaceOfDisbursement                 string  `json:"place_of_disbursement" bson:"place_of_disbursement"`
	PlaceOfDisbursementID               string  `json:"place_of_disbursement_id" bson:"place_of_disbursement_id"`
	ContractThirdParty                  string  `json:"contract_third_party" bson:"contract_third_party"`
	ParentContractCode                  string  `json:"parent_contract_code" bson:"parent_contract_code"`
	ParentProvider                      string  `json:"parent_provider" bson:"parent_provider"`
	ParentProviderID                    string  `json:"parent_provider_id" bson:"parent_provider_id"`
	ParentContractStatus                string  `json:"parent_contract_status" bson:"parent_contract_status"`
	ParentContractStatusID              string  `json:"parent_contract_status_id" bson:"parent_contract_status_id"`
	ParentOperationDate                 string  `json:"parent_operatio_ndate" bson:"parent_operatio_ndate"`
	Fine                                float64 `json:"fine" bson:"fine"`
	Penalty                             float64 `json:"penalty" bson:"penalty"`
	BranchLocation                      string  `json:"branch_location" bson:"branch_location"`
	ProlongationCount                   int     `json:"prolongation_count" bson:"prolongation_count"`
	NumberOfTransactions                int     `json:"number_of_transactions" bson:"number_of_transactions"`
	InstalmentAmount                    float64 `json:"instalment_amount" bson:"instalment_amount"`
	ActualDate                          string  `json:"actual_date" bson:"actual_date"`
	DateOfInserted                      string  `json:"date_of_inserted" bson:"date_of_inserted"`
	NumberOfOverdueInstalmentsMax       int     `json:"number_of_overdue_instalments_max" bson:"number_of_overdue_instalments_max"`
	NumberOfOverdueInstalmentsMaxDate   string  `json:"number_of_overdue_instalments_max_date" bson:"number_of_overdue_instalments_max_date"`
	NumberOfOverdueInstalmentsMaxAmount float64 `json:"number_of_overdue_instalments_max_amount" bson:"number_of_overdue_instalments_max_amount"`
	OverdueAmountMax                    float64 `json:"overdue_amount_max" bson:"overdue_amount_max"`
	OverdueAmountMaxDate                string  `json:"overdue_amount_max_date" bson:"overdue_amount_max_date"`
	OverdueAmountMaxCount               float64 `json:"overdue_amount_max_count" bson:"overdue_amount_max_count"`
	CreditObject                        string  `json:"credit_object" bson:"credit_object"`
	CreditObjectID                      string  `json:"credit_object_id" bson:"credit_object_id"`
	InterconnectedSubjects              string  `json:"interconnected_subjects" bson:"interconnected_subjects"`
	InterconnectedSubjectsName          string  `json:"interconnected_subjects_name" bson:"interconnected_subjects_name"`
	Schema                              string  `json:"schema" bson:"schema"`
	FinancialInstitutionBIN             string  `json:"financial_institution_bin" bson:"financial_institution_bin"`
	LastPaymentDate                     string  `json:"last_payment_date" bson:"last_payment_date"`
}

type ExtDataPkbReportSummaryInformationApplicationStatuses struct {
	ExtDataPkbReportID              int    `json:"ext_data_pkb_report_id" bson:"ext_data_pkb_report_id"`
	SumInfoApplicationsUID          string `json:"sum_info_applications_uid" bson:"sum_info_applications_uid"`
	SummaryInformationApplicationID int    `json:"summary_information_application_id" bson:"summary_information_application_id"`
	Count                           int    `json:"count" bson:"count"`
	Value                           string `json:"value" bson:"value"`
}

type ExtDataPkbReportSummaryInformationApplications struct {
	ContractType           string  `json:"contract_type" bson:"contract_type"`
	ExtDataPkbReportID     int     `json:"ext_data_pkb_report_id" bson:"ext_data_pkb_report_id"`
	SumInfoApplicationsUID string  `json:"sum_info_applications_uid" bson:"sum_info_applications_uid"`
	Title                  string  `json:"title" bson:"title"`
	Type                   string  `json:"type" bson:"type"`
	NumberOfContracts      int     `json:"number_of_contracts" bson:"number_of_contracts"`
	NumberOfCreditlines    int     `json:"number_of_creditlines" bson:"number_of_creditlines"`
	TotalOutstandingDebt   float64 `json:"total_outstanding_debt" bson:"total_outstanding_debt"`
	TotalDebtOverdue       float64 `json:"total_debt_overdue" bson:"total_debt_overdue"`
	TotalFine              float64 `json:"total_fine" bson:"total_fine"`
	TotalPenalty           float64 `json:"total_penalty" bson:"total_penalty"`
}

type ExtDataPkbReportSUSN struct {
	NotFoundText  string `json:"notfoundtext" bson:"notfoundtext"`
	CategoryTitle string `json:"category_title" bson:"category_title"`
	Date          string `json:"date" bson:"date"`
	DateTitle     string `json:"date_title" bson:"date_title"`
	IIN           string `json:"iin" bson:"iin"`
	IINTitle      string `json:"iin_title" bson:"iin_title"`
	Name          string `json:"name" bson:"name"`
	Title         string `json:"title" bson:"title"`
}
