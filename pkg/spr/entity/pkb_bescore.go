package entity

type PkbScore struct {
	ExtDataPkbScore []ExtDataPkbScore `json:"ext_data_pkb_score" bson:"ext_data_pkb_score"`
}

type ExtDataPkbScore struct {
	Score                       float64 `json:"score" bson:"score"`
	IDQuery                     int     `json:"id_query" bson:"id_query"`
	ErrorCode                   int     `json:"error_code" bson:"error_code"`
	RiskGrade                   string  `json:"risk_grade" bson:"risk_grade"`
	CausesName                  string  `json:"causes_name" bson:"causes_name"`
	CausesText                  string  `json:"causes_text" bson:"causes_text"`
	ErrorString                 string  `json:"error_string" bson:"error_string"`
	OneYearProbabilityOfDefault string  `json:"one_year_probability_of_default" bson:"one_year_probability_of_default"`
	IsFrozen                    int     `json:"is_frozen" bson:"is_frozen"`
}
