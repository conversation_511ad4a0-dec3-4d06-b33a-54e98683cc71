package entity

type JuicyScore struct {
	ExtDataJuicyScore []ExtDataJuicyScore `json:"ext_data_juicyscore" bson:"ext_data_juicyscore"`
}

type ExtDataJuicyScore struct {
	RequestLogID                                        int32   `json:"request_log_id" bson:"request_log_id"`
	AntiFraudScore                                      float64 `json:"anti_fraud_score" bson:"anti_fraud_score"`
	AdditionalInfo                                      string  `json:"additional_info" bson:"additional_info"`
	DeviceID                                            int64   `json:"device_id" bson:"device_id"`
	ExactDeviceID                                       int64   `json:"exact_device_id" bson:"exact_device_id"`
	BrowserHash                                         string  `json:"browser_hash" bson:"browser_hash"`
	UserID                                              int64   `json:"user_id" bson:"user_id"`
	Success                                             int32   `json:"success" bson:"success"`
	Time                                                float64 `json:"time" bson:"time"`
	Idx1StopMarkers                                     int32   `json:"idx1_stop_markers" bson:"idx1_stop_markers"`
	Idx2UserBehaviourMarkers                            int32   `json:"idx2_user_behaviour_markers" bson:"idx2_user_behaviour_markers"`
	Idx3DeviceMarkers                                   int32   `json:"idx3_device_markers" bson:"idx3_device_markers"`
	Idx4ConnectionMarkers                               int32   `json:"idx4_connection_markers" bson:"idx4_connection_markers"`
	Idx5DeviceQuality                                   int32   `json:"idx5_device_quality" bson:"idx5_device_quality"`
	Idx6InternetInfrastructureQuality                   int32   `json:"idx6_internet_infrastructure_quality" bson:"idx6_internet_infrastructure_quality"`
	Idx7DeviceApplicationsQuality                       int32   `json:"idx7_device_applications_quality" bson:"idx7_device_applications_quality"`
	Idx8DeviceCredentialsVariability                    int32   `json:"idx8_device_credentials_variability" bson:"idx8_device_credentials_variability"`
	JavaScriptExecutable                                int32   `json:"java_script_executable" bson:"java_script_executable"`
	UseragentAvailable                                  int32   `json:"useragent_available" bson:"useragent_available"`
	SupportingDataAvailable                             int32   `json:"supporting_data_available" bson:"supporting_data_available"`
	IsLocalCountry                                      int32   `json:"is_local_country" bson:"is_local_country"`
	TimezoneMismatch                                    int32   `json:"timezone_mismatch" bson:"timezone_mismatch"`
	IPSimRegionsMatch                                   int32   `json:"ip_sim_regions_match" bson:"ip_sim_regions_match"`
	DuplicatingDevice                                   int32   `json:"duplicating_device" bson:"duplicating_device"`
	DuplicatingIP                                       int32   `json:"duplicating_ip" bson:"duplicating_ip"`
	DuplicatingUser                                     int32   `json:"duplicating_user" bson:"duplicating_user"`
	SeenBefore                                          int32   `json:"seen_before" bson:"seen_before"`
	SamePhone                                           int32   `json:"same_phone" bson:"same_phone"`
	SameRegion                                          int32   `json:"same_region" bson:"same_region"`
	SameDevice                                          int32   `json:"same_device" bson:"same_device"`
	TotalApplicationsNumber                             int32   `json:"total_applications_number" bson:"total_applications_number"`
	ApplicationsNumber                                  int32   `json:"applications_number" bson:"applications_number"`
	TotalNumOfApplicationsWithBrowserHashIn1Day         int32   `json:"total_num_of_applications_with_browser_hash_in_1_day" bson:"total_num_of_applications_with_browser_hash_in_1_day"`
	TotalNumOfApplicationsWithBrowserHashIn1Hour        int32   `json:"total_num_of_applications_with_browser_hash_in_1_hour" bson:"total_num_of_applications_with_browser_hash_in_1_hour"`
	TotalNumOfApplicationsWithBrowserHashIn7Days        int32   `json:"total_num_of_applications_with_browser_hash_in_7_days" bson:"total_num_of_applications_with_browser_hash_in_7_days"`
	TotalNumOfApplicationsWithBrowserHashIn30Days       int32   `json:"total_num_of_applications_with_browser_hash_in_30_days" bson:"total_num_of_applications_with_browser_hash_in_30_days"`
	PhonesNumber                                        int32   `json:"phones_number" bson:"phones_number"`
	DevicesNumber                                       int32   `json:"devices_number" bson:"devices_number"`
	CitiesNumber                                        int32   `json:"cities_number" bson:"cities_number"`
	ZipCodesNumber                                      int32   `json:"zip_codes_number" bson:"zip_codes_number"`
	CardsNumber                                         int32   `json:"cards_number" bson:"cards_number"`
	LessTenorDays                                       int32   `json:"less_tenor_days" bson:"less_tenor_days"`
	DeviceAgeingWithUser                                int32   `json:"device_ageing_with_user" bson:"device_ageing_with_user"`
	TotalNumOfShortTermCreditApplicationsIn1Day         int32   `json:"total_num_of_short_term_credit_applications_in_1_day" bson:"total_num_of_short_term_credit_applications_in_1_day"`
	TotalNumOfShortTermCreditApplicationsIn7Days        int32   `json:"total_num_of_short_term_credit_applications_in_7_days" bson:"total_num_of_short_term_credit_applications_in_7_days"`
	TotalNumOfShortTermCreditApplicationsIn30Days       int32   `json:"total_num_of_short_term_credit_applications_in_30_days" bson:"total_num_of_short_term_credit_applications_in_30_days"`
	TotalNumOfShortTermCreditApplicationsFromIPIn1Day   int32   `json:"total_num_of_short_term_credit_applications_from_ip_in_1_day" bson:"total_num_of_short_term_credit_applications_from_ip_in_1_day"`
	TotalNumOfShortTermCreditApplicationsFromIPIn7Days  int32   `json:"total_num_of_short_term_credit_applications_from_ip_in_7_days" bson:"total_num_of_short_term_credit_applications_from_ip_in_7_days"`
	TotalNumOfShortTermCreditApplicationsFromIPIn30Days int32   `json:"total_num_of_short_term_credit_applications_from_ip_in_30_days" bson:"total_num_of_short_term_credit_applications_from_ip_in_30_days"`
	TotalNumOfBankingCreditApplicationsIn1Day           int32   `json:"total_num_of_banking_credit_applications_in_1_day" bson:"total_num_of_banking_credit_applications_in_1_day"`
	TotalNumOfBankingCreditApplicationsIn7Days          int32   `json:"total_num_of_banking_credit_applications_in_7_days" bson:"total_num_of_banking_credit_applications_in_7_days"`
	TotalNumOfBankingCreditApplicationsIn30Days         int32   `json:"total_num_of_banking_credit_applications_in_30_days" bson:"total_num_of_banking_credit_applications_in_30_days"`
	TotalNumOfBankingCreditApplicationsFromIPIn1Day     int32   `json:"total_num_of_banking_credit_applications_from_ip_in_1_day" bson:"total_num_of_banking_credit_applications_from_ip_in_1_day"`
	TotalNumOfBankingCreditApplicationsFromIPIn7Days    int32   `json:"total_num_of_banking_credit_applications_from_ip_in_7_days" bson:"total_num_of_banking_credit_applications_from_ip_in_7_days"`
	TotalNumOfBankingCreditApplicationsFromIPIn30Days   int32   `json:"total_num_of_banking_credit_applications_from_ip_in_30_days" bson:"total_num_of_banking_credit_applications_from_ip_in_30_days"`
	TotalNumOfInsuranceApplicationsIn1Day               int32   `json:"total_num_of_insurance_applications_in_1_day" bson:"total_num_of_insurance_applications_in_1_day"`
	TotalNumOfInsuranceApplicationsIn7Days              int32   `json:"total_num_of_insurance_applications_in_7_days" bson:"total_num_of_insurance_applications_in_7_days"`
	TotalNumOfInsuranceApplicationsIn30Days             int32   `json:"total_num_of_insurance_applications_in_30_days" bson:"total_num_of_insurance_applications_in_30_days"`
	TotalNumOfInsuranceApplicationsFromIPIn1Day         int32   `json:"total_num_of_insurance_applications_from_ip_in_1_day" bson:"total_num_of_insurance_applications_from_ip_in_1_day"`
	TotalNumOfInsuranceApplicationsFromIPIn7Days        int32   `json:"total_num_of_insurance_applications_from_ip_in_7_days" bson:"total_num_of_insurance_applications_from_ip_in_7_days"`
	TotalNumOfInsuranceApplicationsFromIPIn30Days       int32   `json:"total_num_of_insurance_applications_from_ip_in_30_days" bson:"total_num_of_insurance_applications_from_ip_in_30_days"`
	ForeignLanguageUsed                                 int32   `json:"foreign_language_used" bson:"foreign_language_used"`
	Frd                                                 int32   `json:"frd" bson:"frd"`
	FrdFromDiffAcc                                      int32   `json:"frd_from_diff_acc" bson:"frd_from_diff_acc"`
	Npl90Plus                                           int32   `json:"npl90_plus" bson:"npl90_plus"`
	Npl90PlusFromDiffAcc                                int32   `json:"npl90_plus_from_diff_acc" bson:"npl90_plus_from_diff_acc"`
	IsEverFrd                                           int32   `json:"is_ever_frd" bson:"is_ever_frd"`
	IsMassFrd                                           int32   `json:"is_mass_frd" bson:"is_mass_frd"`
	GlobalIPBlacklist                                   int32   `json:"global_ip_blacklist" bson:"global_ip_blacklist"`
	GlobalISPBlacklist                                  int32   `json:"global_isp_blacklist" bson:"global_isp_blacklist"`
	UserDefinedIPBlacklist                              int32   `json:"user_defined_ip_blacklist" bson:"user_defined_ip_blacklist"`
	UserDefinedDeviceIDBlacklist                        int32   `json:"user_defined_device_id_blacklist" bson:"user_defined_device_id_blacklist"`
	UserDefinedUserIDBlacklist                          int32   `json:"user_defined_user_id_blacklist" bson:"user_defined_user_id_blacklist"`
	UserDefinedISPBlacklist                             int32   `json:"user_defined_isp_blacklist" bson:"user_defined_isp_blacklist"`
	UserDefinedSimilarDeviceIDBlacklist                 int32   `json:"user_defined_similar_device_id_blacklist" bson:"user_defined_similar_device_id_blacklist"`
	SuspiciousReferral                                  int32   `json:"suspicious_referral" bson:"suspicious_referral"`
	SuspiciousPlugins                                   int32   `json:"suspicious_plugins" bson:"suspicious_plugins"`
	SuspiciousFonts                                     int32   `json:"suspicious_fonts" bson:"suspicious_fonts"`
	SocialVectorLength                                  int32   `json:"social_vector_length" bson:"social_vector_length"`
	ShoppingVectorLength                                int32   `json:"shopping_vector_length" bson:"shopping_vector_length"`
	MicrolendingVectorLength                            int32   `json:"microlending_vector_length" bson:"microlending_vector_length"`
	GamblingVectorLength                                int32   `json:"gambling_vector_length" bson:"gambling_vector_length"`
	ProxyVectorLength                                   int32   `json:"proxy_vector_length" bson:"proxy_vector_length"`
	TimeOnPage                                          float64 `json:"time_on_page" bson:"time_on_page"`
	NumberOfHotKeys                                     int32   `json:"number_of_hot_keys" bson:"number_of_hot_keys"`
	NumberOfCorrections                                 int32   `json:"number_of_corrections" bson:"number_of_corrections"`
	BrowserHistoryCount                                 int32   `json:"browser_history_count" bson:"browser_history_count"`
	IsConsoleOpen                                       int32   `json:"is_console_open" bson:"is_console_open"`
	RAMProductivity                                     int32   `json:"ram_productivity" bson:"ram_productivity"`
	StorageProductivity                                 int32   `json:"storage_productivity" bson:"storage_productivity"`
	HDDUtils                                            float64 `json:"hdd_utils" bson:"hdd_utils"`
	SingleClick                                         int32   `json:"single_click" bson:"single_click"`
	DoubleClick                                         int32   `json:"double_click" bson:"double_click"`
	ContextMenu                                         int32   `json:"context_menu" bson:"context_menu"`
	LeavePage                                           int32   `json:"leave_page" bson:"leave_page"`
	ObservedQuarters                                    int32   `json:"observed_quarters" bson:"observed_quarters"`
	TouchDev                                            float64 `json:"touch_dev" bson:"touch_dev"`
	LoanLimitUtilization                                float64 `json:"loan_limit_utilization" bson:"loan_limit_utilization"`
	NumberOfSecondaryAntiFraudMarkers                   int32   `json:"number_of_secondary_anti_fraud_markers" bson:"number_of_secondary_anti_fraud_markers"`
	IPCountryCode                                       string  `json:"ip_country_code" bson:"ip_country_code"`
	IPCountryName                                       string  `json:"ip_country_name" bson:"ip_country_name"`
	IPRegionName                                        string  `json:"ip_region_name" bson:"ip_region_name"`
	IPCity                                              string  `json:"ip_city" bson:"ip_city"`
	IPLongitude                                         float64 `json:"ip_longitude" bson:"ip_longitude"`
	IPLatitude                                          float64 `json:"ip_latitude" bson:"ip_latitude"`
	IPOwner                                             string  `json:"ip_owner" bson:"ip_owner"`
	IPDomain                                            string  `json:"ip_domain" bson:"ip_domain"`
	IPMobileBrand                                       string  `json:"ip_mobile_brand" bson:"ip_mobile_brand"`
	IPUsageType                                         string  `json:"ip_usage_type" bson:"ip_usage_type"`
	IPNetSpeed                                          string  `json:"ip_net_speed" bson:"ip_net_speed"`
	Proxy                                               int32   `json:"proxy" bson:"proxy"`
	Tor                                                 int32   `json:"tor" bson:"tor"`
	RealIP                                              string  `json:"real_ip" bson:"real_ip"`
	RealIPv6                                            string  `json:"real_ipv6" bson:"real_ipv6"`
	IPv6Usage                                           int32   `json:"ipv6_usage" bson:"ipv6_usage"`
	IPMismatch                                          int32   `json:"ip_mismatch" bson:"ip_mismatch"`
	IPFirstSeenDate                                     string  `json:"ip_first_seen_date" bson:"ip_first_seen_date"`
	IPAgeingInMonths                                    int32   `json:"ip_ageing_in_months" bson:"ip_ageing_in_months"`
	DNSName                                             string  `json:"dns_name" bson:"dns_name"`
	DNSIP                                               string  `json:"dns_ip" bson:"dns_ip"`
	IsDNSLocal                                          int32   `json:"is_dns_local" bson:"is_dns_local"`
	IPZipCode                                           string  `json:"ip_zip_code" bson:"ip_zip_code"`
	IPZipCodeDistance                                   int32   `json:"ip_zip_code_distance" bson:"ip_zip_code_distance"`
	VirtualMachine                                      int32   `json:"virtual_machine" bson:"virtual_machine"`
	IsUseragentStructureIssue                           int32   `json:"is_useragent_structure_issue" bson:"is_useragent_structure_issue"`
	IsRandomizerIssue                                   int32   `json:"is_randomizer_issue" bson:"is_randomizer_issue"`
	IsCanvasBlocker                                     int32   `json:"is_canvas_blocker" bson:"is_canvas_blocker"`
	IsPrivateMode                                       int32   `json:"is_private_mode" bson:"is_private_mode"`
	Botnet                                              int32   `json:"botnet" bson:"botnet"`
	ConnectionViaApplication                            int32   `json:"connection_via_application" bson:"connection_via_application"`
	IsWebViewApplicationUsed                            int32   `json:"is_web_view_application_used" bson:"is_web_view_application_used"`
	DeviceType                                          string  `json:"device_type" bson:"device_type"`
	DeviceVendor                                        string  `json:"device_vendor" bson:"device_vendor"`
	DeviceModel                                         string  `json:"device_model" bson:"device_model"`
	DisplayWidthViaPixel                                int32   `json:"display_width_via_pixel" bson:"display_width_via_pixel"`
	DisplayHeightViaPixel                               int32   `json:"display_height_via_pixel" bson:"display_height_via_pixel"`
	ScreenGeometryMismatch                              int32   `json:"screen_geometry_mismatch" bson:"screen_geometry_mismatch"`
	IsRegularScreenResolution                           int32   `json:"is_regular_screen_resolution" bson:"is_regular_screen_resolution"`
	DisplayPixelRatio                                   float64 `json:"display_pixel_ratio" bson:"display_pixel_ratio"`
	ColorDepthViaPixel                                  int32   `json:"color_depth_via_pixel" bson:"color_depth_via_pixel"`
	RAMSize                                             float64 `json:"ram_size" bson:"ram_size"`
	HardwareConcurrency                                 int32   `json:"hardware_concurrency" bson:"hardware_concurrency"`
	NFC                                                 int32   `json:"nfc" bson:"nfc"`
	IPTimeZone                                          float64 `json:"ip_time_zone" bson:"ip_time_zone"`
	IPTimeZoneName                                      string  `json:"ip_time_zone_name" bson:"ip_time_zone_name"`
	PixelTimeZone                                       int32   `json:"pixel_time_zone" bson:"pixel_time_zone"`
	DeviceLanguage                                      string  `json:"device_language" bson:"device_language"`
	PixelLanguage                                       string  `json:"pixel_language" bson:"pixel_language"`
	OSName                                              string  `json:"os_name" bson:"os_name"`
	OSVersion                                           string  `json:"os_version" bson:"os_version"`
	BrowserName                                         string  `json:"browser_name" bson:"browser_name"`
	BrowserVersion                                      string  `json:"browser_version" bson:"browser_version"`
	RenderingEngine                                     string  `json:"rendering_engine" bson:"rendering_engine"`
	Idx9DeviceApplicationsRisk                          int32   `json:"idx9_device_applications_risk" bson:"idx9_device_applications_risk"`
	Idx10EIEstimation                                   int32   `json:"idx10_ei_estimation" bson:"idx10_ei_estimation"`
	BrowserMimesLength                                  int32   `json:"browser_mimes_length" bson:"browser_mimes_length"`
	IsWifiSwitchedOn                                    int32   `json:"is_wifi_switched_on" bson:"is_wifi_switched_on"`
	IsCellularSwitchedOn                                int32   `json:"is_cellular_switched_on" bson:"is_cellular_switched_on"`
	IsBluetoothSwitchedOn                               int32   `json:"is_bluetooth_switched_on" bson:"is_bluetooth_switched_on"`
	IsWebRTCSwitchedOff                                 int32   `json:"is_web_rtc_switched_off" bson:"is_web_rtc_switched_off"`
	IsWebGLSwitchedOff                                  int32   `json:"is_web_gl_switched_off" bson:"is_web_gl_switched_off"`
	IPZipBillingCountriesMatch                          int32   `json:"ip_zip_billing_countries_match" bson:"ip_zip_billing_countries_match"`
	SuspiciousLanguageUsed                              int32   `json:"suspicious_language_used" bson:"suspicious_language_used"`
	BrowserLanguagesLength                              int32   `json:"browser_languages_length" bson:"browser_languages_length"`
	Npl30Plus                                           int32   `json:"npl30_plus" bson:"npl30_plus"`
	Npl30PlusFromDiffAcc                                int32   `json:"npl30_plus_from_diff_acc" bson:"npl30_plus_from_diff_acc"`
	TotalNumOfFrdAndNpl90PlusApplicationsFromDevice     int32   `json:"total_num_of_frd_and_npl90_plus_applications_from_device" bson:"total_num_of_frd_and_npl90_plus_applications_from_device"`
	TotalNumOfFrdAndNpl90PlusApplicationsFromIP         int32   `json:"total_num_of_frd_and_npl90_plus_applications_from_ip" bson:"total_num_of_frd_and_npl90_plus_applications_from_ip"`
	TotalNumOfFrdAndNpl90PlusApplicationsFromIPv3       int32   `json:"total_num_of_frd_and_npl90_plus_applications_from_ipv3" bson:"total_num_of_frd_and_npl90_plus_applications_from_ipv3"`
	FontsRandomizationLevel                             int32   `json:"fonts_randomization_level" bson:"fonts_randomization_level"`
	NonOfficialMobileApplication                        int32   `json:"non_official_mobile_application" bson:"non_official_mobile_application"`
	WifiLevel                                           int32   `json:"wifi_level" bson:"wifi_level"`
	BatteryLevel                                        int32   `json:"battery_level" bson:"battery_level"`
	BatteryStatus                                       int32   `json:"battery_status" bson:"battery_status"`
	DeviceUptimeSinceLastReboot                         int32   `json:"device_uptime_since_last_reboot" bson:"device_uptime_since_last_reboot"`
	SuspiciousGeometryUsed                              int32   `json:"suspicious_geometry_used" bson:"suspicious_geometry_used"`
	IsLocalTimeZone                                     int32   `json:"is_local_time_zone" bson:"is_local_time_zone"`
	SameZipBilling                                      int32   `json:"same_zip_billing" bson:"same_zip_billing"`
	UncommonLanguageUsedInBrowser                       int32   `json:"uncommon_language_used_in_browser" bson:"uncommon_language_used_in_browser"`
	UserDefinedClientIDBlacklist                        int32   `json:"user_defined_client_id_blacklist" bson:"user_defined_client_id_blacklist"`
	IsSessionClone                                      int32   `json:"is_session_clone" bson:"is_session_clone"`
	CursorMovementSpeed                                 float64 `json:"cursor_movement_speed" bson:"cursor_movement_speed"`
	CursorDistanceCovered                               float64 `json:"cursor_distance_covered" bson:"cursor_distance_covered"`
	ScrollMovementSpeed                                 float64 `json:"scroll_movement_speed" bson:"scroll_movement_speed"`
	ScrollDistanceCovered                               float64 `json:"scroll_distance_covered" bson:"scroll_distance_covered"`
	AvgScreenIdleTime                                   float64 `json:"avg_screen_idle_time" bson:"avg_screen_idle_time"`
	IsDataMatching                                      int32   `json:"is_data_matching" bson:"is_data_matching"`
	InternetConnectionType                              string  `json:"internet_connection_type" bson:"internet_connection_type"`
	InternetConnectionSpeed                             string  `json:"internet_connection_speed" bson:"internet_connection_speed"`
	NumberOfBrowserAnomalies                            int32   `json:"number_of_browser_anomalies" bson:"number_of_browser_anomalies"`
	SuspiciousScripts                                   int32   `json:"suspicious_scripts" bson:"suspicious_scripts"`
	IsRootedDevice                                      int32   `json:"is_rooted_device" bson:"is_rooted_device"`
	IsFakeGeoLocationUsed                               int32   `json:"is_fake_geo_location_used" bson:"is_fake_geo_location_used"`
	DNTModeUsed                                         int32   `json:"dnt_mode_used" bson:"dnt_mode_used"`
	BrowserVersionAgeing                                int32   `json:"browser_version_ageing" bson:"browser_version_ageing"`
	IsLocalTimeZoneName                                 int32   `json:"is_local_time_zone_name" bson:"is_local_time_zone_name"`
	NumOfDaysFromPrevSession                            int32   `json:"num_of_days_from_prev_session" bson:"num_of_days_from_prev_session"`
	NetfilterVectorLength                               int32   `json:"netfilter_vector_length" bson:"netfilter_vector_length"`
	IsWebRTCDataMatching                                int32   `json:"is_webrtc_data_matching" bson:"is_webrtc_data_matching"`
	IsTLSRandomizer                                     int32   `json:"is_tls_randomizer" bson:"is_tls_randomizer"`
	IsLimitedNoiseRandomizer                            int32   `json:"is_limited_noise_randomizer" bson:"is_limited_noise_randomizer"`
	IsRootedDeviceCurrent                               int32   `json:"is_rooted_device_current" bson:"is_rooted_device_current"`
	IsRootedDeviceBefore                                int32   `json:"is_rooted_device_before" bson:"is_rooted_device_before"`
	NumberOfBotBehAnomalies                             int32   `json:"number_of_bot_beh_anomalies" bson:"number_of_bot_beh_anomalies"`
	RDPVectorLength                                     int32   `json:"rdp_vector_length" bson:"rdp_vector_length"`
	ProxyType                                           string  `json:"proxy_type" bson:"proxy_type"`
	ChDownlink                                          int32   `json:"ch_downlink" bson:"ch_downlink"`
	IsPoorVCSupport                                     int32   `json:"is_poor_vc_support" bson:"is_poor_vc_support"`
	IsStatDFPRUnique                                    int32   `json:"is_stat_dfpr_unique" bson:"is_stat_dfpr_unique"`
	IsSuspAspectRatio                                   int32   `json:"is_susp_aspect_ratio" bson:"is_susp_aspect_ratio"`
	IsTLSNotUnique                                      int32   `json:"is_tls_not_unique" bson:"is_tls_not_unique"`
	IsActiveCall                                        int32   `json:"is_active_call" bson:"is_active_call"`
	IsActiveVoIPCall                                    int32   `json:"is_active_voip_call" bson:"is_active_voip_call"`
	IsOnHoldCall                                        int32   `json:"is_onhold_call" bson:"is_onhold_call"`
	IsFactoryReset                                      int32   `json:"is_factory_reset" bson:"is_factory_reset"`
	OSBootCount                                         int32   `json:"os_boot_count" bson:"os_boot_count"`
	MaxSimTrfPerSec                                     float64 `json:"max_sim_trf_per_sec" bson:"max_sim_trf_per_sec"`
	MaxWifiTrfPerSec                                    float64 `json:"max_wifi_trf_per_sec" bson:"max_wifi_trf_per_sec"`
	IsLockDownMode                                      int32   `json:"is_lock_down_mode" bson:"is_lock_down_mode"`
	DuplicatingExactDeviceID                            int32   `json:"duplicating_exact_device_id" bson:"duplicating_exact_device_id"`
	ApplicationsNumberOnExactDeviceID                   int32   `json:"applications_number_on_exact_device_id" bson:"applications_number_on_exact_device_id"`
	TotalNumOfShortTermSessionsIn1Hour                  int32   `json:"total_num_of_short_term_sessions_in_1_hour" bson:"total_num_of_short_term_sessions_in_1_hour"`
	TotalNumOfBankingSessionsIn1Hour                    int32   `json:"total_num_of_banking_sessions_in_1_hour" bson:"total_num_of_banking_sessions_in_1_hour"`
	TotalNumOfInsuranceSessionsIn1Hour                  int32   `json:"total_num_of_insurance_sessions_in_1_hour" bson:"total_num_of_insurance_sessions_in_1_hour"`
	AntivirusVectorLength                               int32   `json:"antivirus_vector_length" bson:"antivirus_vector_length"`
	AvgTouchSize                                        float64 `json:"avg_touch_size" bson:"avg_touch_size"`
	KeystrokeDwellTime                                  float64 `json:"keystroke_dwell_time" bson:"keystroke_dwell_time"`
	KeystrokeFlightTime                                 float64 `json:"keystroke_flight_time" bson:"keystroke_flight_time"`
	AvgTypingSpeed                                      float64 `json:"avg_typing_speed" bson:"avg_typing_speed"`
	MobileOSVirtualMachine                              int32   `json:"mobile_os_virtual_machine" bson:"mobile_os_virtual_machine"`
	NumberOfMissingDeviceStaticFingerprints             int32   `json:"number_of_missing_device_static_fingerprints" bson:"number_of_missing_device_static_fingerprints"`
	IsHighRiskTLS                                       int32   `json:"is_high_risk_tls" bson:"is_high_risk_tls"`
	IsRemoteAccess                                      int32   `json:"is_remote_access" bson:"is_remote_access"`
	IsPhoneCarrierLocal                                 int32   `json:"is_phone_carrier_local" bson:"is_phone_carrier_local"`
	NumberOfSimCardsOnDevice                            int32   `json:"number_of_sim_cards_on_device" bson:"number_of_sim_cards_on_device"`
	AvgReceivedTrafficViaWifi                           float64 `json:"avg_received_traffic_via_wifi" bson:"avg_received_traffic_via_wifi"`
	AvgTransmittedTrafficViaWifi                        float64 `json:"avg_transmitted_traffic_via_wifi" bson:"avg_transmitted_traffic_via_wifi"`
	AvgMobileReceivedTrafficWoWifi                      float64 `json:"avg_mobile_received_traffic_wo_wif" bson:"avg_mobile_received_traffic_wo_wif"`
	AvgMobileTransmittedTrafficWoWifi                   float64 `json:"avg_mobile_transmitted_traffic_wo_wifi" bson:"avg_mobile_transmitted_traffic_wo_wifi"`
	DeviceYearReleased                                  int32   `json:"device_year_released" bson:"device_year_released"`
	ESIM                                                int32   `json:"esim" bson:"esim"`
	JSDownlink                                          int32   `json:"js_downlink" bson:"js_downlink"`
	PixelTimezoneName                                   string  `json:"pixel_timezone_name" bson:"pixel_timezone_name"`
	TotalNumOfBankingCreditApplicationsIn1Hour          int32   `json:"total_num_of_banking_credit_applications_in_1_hour" bson:"total_num_of_banking_credit_applications_in_1_hour"`
	TotalNumOfGamblingApplicationsIn1Hour               int32   `json:"total_num_of_gambling_applications_in_1_hour" bson:"total_num_of_gambling_applications_in_1_hour"`
	TotalNumOfInsuranceApplicationsIn1Hour              int32   `json:"total_num_of_Insurance_applications_in_1_hour" bson:"total_num_of_Insurance_applications_in_1_hour"`
	TotalNumOfShortTermCreditApplicationsIn1Hour        int32   `json:"total_num_of_shortterm_credit_applications_in_1_hour" bson:"total_num_of_shortterm_credit_applications_in_1_hour"`
}
