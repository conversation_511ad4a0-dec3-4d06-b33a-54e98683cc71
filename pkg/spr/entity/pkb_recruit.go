package entity

type PkbRecruit struct {
	ExtDataRecruit []ExtDataRecruit `json:"ext_data_recruit" bson:"ext_data_recruit"`
}

type ExtDataRecruit struct {
	RequestLogID int    `json:"request_log_id" bson:"request_log_id"`
	IsFound      int    `json:"is_found" bson:"is_found"`
	Iin          string `json:"iin" bson:"iin"`
	Status       int    `json:"status" bson:"status"`
	Date         string `json:"date" bson:"date"`
}
