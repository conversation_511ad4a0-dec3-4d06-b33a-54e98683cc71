package spr

import (
	"git.redmadrobot.com/backend-go/rmr-pkg/core/cfg"
	"git.redmadrobot.com/backend-go/rmr-pkg/core/cfg/viperx"
	"github.com/spf13/viper"
)

const (
	ExecuteExtEndpoint = "/api/execute-ext"
)

const (
	CfgSPRBaseURL   cfg.Key = "SPR_BASE_URL"
	CfgSPRTokenAuth cfg.Key = "SPR_TOKEN_AUTH" // #nosec G101
	CfgSPRTypeAuth  cfg.Key = "SPR_TYPE_AUTH"

	defaultSPRBaseURL  = "https://api.spr.kz"
	defaultSPRTypeAuth = "Basic"
)

type Config struct {
	BaseURL string

	TypeAuthorization string

	TokenAuthorization string
}

func CfgFromViper(loader *viper.Viper, keyMapping ...cfg.KeyMap) *Config {
	loader.SetDefault(CfgSPRBaseURL.String(), defaultSPRBaseURL)
	loader.SetDefault(CfgSPRTypeAuth.String(), defaultSPRTypeAuth)
	loader.SetDefault(CfgSPRTokenAuth.String(), "")

	return &Config{
		BaseURL:            viperx.Get(loader, CfgSPRBaseURL.Map(keyMapping...), defaultSPRBaseURL),
		TypeAuthorization:  viperx.Get(loader, CfgSPRTypeAuth.Map(keyMapping...), defaultSPRTypeAuth),
		TokenAuthorization: viperx.Get(loader, CfgSPRTokenAuth.Map(keyMapping...), ""),
	}
}
