package spr

import (
	"context"
	"net/http"

	"git.redmadrobot.com/zaman/backend/zaman/pkg/spr/entity"
)

var _ SPRProvider = (*Client)(nil)

type SPRProvider interface {
	GetExecuteExt(ctx context.Context, request *entity.SprRequest) (*entity.SprResponse, error)
}

type Client struct {
	HTTPClient *http.Client
	BaseURL    string
	TokenAuth  string
	TypeAuth   string
}

func NewClient(cfg *Config) *Client {
	return &Client{
		HTTPClient: &http.Client{},
		BaseURL:    cfg.BaseURL,
		TokenAuth:  cfg.TokenAuthorization,
		TypeAuth:   cfg.TypeAuthorization,
	}
}
