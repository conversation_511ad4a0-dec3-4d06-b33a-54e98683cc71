-- Скрипты нужны для добавления моковых данных в bts-bridge


-- Выбрать пользователя по иин
Select pd.id          as person_id,
       pd.iin         as person_iin,
       pd.firstname   as person_firstname,
       pd.lastname    as person_lastname,
       pd.patronymic  as person_patronymic,
       pd.birthdate   as person_birthdate,
       pd.gender      as person_gender,
       bp.city        as birth_plase_city,
       bp.country     as birth_plase_country,
       bp.district    as birth_plase_district,
       bp.region      as birth_plase_region,
       ra.country     as reg_adresses_country,
       ra.arcode      as reg_adresses_arcode,
       ra.begin_date  as reg_adresses_begin_date,
       ra.street      as reg_adresses_street,
       ra.flat        as reg_adresses_flat,
       ra.district    as reg_adresses_district,
       ra.region      as reg_adresses_region,
       ra.building    as reg_adresses_building,
       ra.city        as reg_adresses_city,
       ra.corpus      as reg_adresses_corpus,
       ra.invalidity  as reg_adresses_invalidity,
       ra.status      as reg_adresses_status,
       documents.*,
       pc.name_kaz    as personal_citizenships_name_kaz,
       pc.name_ru     as personal_citizenships_name_ru,
       pc.code        as personal_citizenships_code,
       pc.change_date as personal_citizenships_change_date,
       ls.name_ru     as life_status_name_ru,
       ls.name_kz     as life_status_name_kz,
       ls.code        as life_status_code,
       ls.change_date as life_status_change_date
from personal_data as pd
       join public.birth_places bp on pd.id = bp.personal_data_birth_place
       join public.reg_addresses ra on pd.id = ra.personal_data_reg_addresses
       join public.documents on pd.id = documents.personal_data_documents
       join public.person_citizenships pc on pd.id = pc.personal_data_person_citizenship
       join public.life_status ls on pd.id = ls.personal_data_life_status
where pd.iin = '************';


-- Добавить нового
WITH pd AS (
INSERT INTO personal_data (
  id, iin, create_time, update_time, firstname,
  lastname, patronymic, birthdate, deleted, gender
)
VALUES (gen_random_uuid(),
  '************',
  now(),
  now(),
  'test_rus_firstname',
  'test_rus_lastname',
  'test_rus_patronymic',
  '1982-11-06',
  false,
  '{
    "code": 1,
    "nameKz": "Еркек",
    "nameRu": "Мужской",
    "changeDate": "2008-03-01T13:21:45+05:00"
  }')
  RETURNING id),
  birth AS (
INSERT INTO birth_places (
  id, create_time, update_time, deleted, country,
  city, district, region, personal_data_birth_place
)
VALUES (gen_random_uuid(),
  now(),
  now(),
  false,
  '{
    "code": 398,
    "nameKz": "ҚАЗАҚСТАН",
    "nameRu": "КАЗАХСТАН",
    "changeDate": "2008-03-01T13:21:44+05:00"
  }',
  'Almaty',
  '{
    "code": 1910,
    "nameKz": "АЛМАТЫ",
    "nameRu": "АЛМАТЫ",
    "changeDate": "2008-03-01T13:21:45+05:00"
  }',
  '{
    "code": 1910274,
    "nameKz": "АЛМАЛЫ",
    "nameRu": "АЛМАЛИНСКИЙ",
    "changeDate": "2008-03-01T13:21:45+05:00"
  }',
  (select pd.id from pd))
  RETURNING id),
  reg AS (
INSERT INTO reg_addresses (
  id, create_time, update_time, country, begin_date, street, flat, district, region,
  building, city, corpus, personal_data_reg_addresses, status, invalidity, arcode
)
VALUES (gen_random_uuid(),
  now(),
  now(),
  '{
    "code": 398,
    "nameKz": "ҚАЗАҚСТАН",
    "nameRu": "КАЗАХСТАН",
    "changeDate": "2024-06-10T11:16:01+05:00"
  }',
  current_date,
  'Pushkin St.',
  '12',
  '{
    "code": 1930,
    "nameKz": "ҚАРАҒАНДЫ ОБЛЫСЫ",
    "nameRu": "КАРАГАНДИНСКАЯ ОБЛАСТЬ",
    "changeDate": "2024-06-10T11:16:01+05:00"
  }',
  '{
    "code": 1930401,
    "nameKz": "ҚАРАҒАНДЫ",
    "nameRu": "КАРАГАНДА",
    "changeDate": "2024-06-10T11:16:01+05:00"
  }',
  '5A',
  'Almaty',
  'Corpus 1',
  (select pd.id from pd),
  '{
    "code": 398,
    "nameKz": "ҚАЗАҚСТАН",
    "nameRu": "ҚАЗАҚСТАН",
    "changeDate": "2024-06-10T11:16:01+05:00"
  }',
  '{
    "code": 0,
    "nameKz": "",
    "nameRu": "",
    "changeDate": ""
  }',
  '12345')
  RETURNING id),
  docs AS (
INSERT INTO documents (
  id, create_time, update_time, deleted, number, begin_date, patronymic, end_date,
  surname, name, type_name_ru, type_code, type_name_kaz, type_change_date, birthdate,
  issue_organization, status, personal_data_documents
)
VALUES (gen_random_uuid(),
  now(), now(), false, 'A1234567', current_date, 'test_rus_patronymic', now(),
  'test_rus_lastname', 'test_rus_firstname', 'УДОСТОВЕРЕНИЕ РК', '002', 'ҚР ЖЕКЕ КУӘЛІГІ',
  current_date, '1982-11-06',
  '{
    "code": "002",
    "nameKz": "ҚР ІШКІ ІСТЕР МИНИСТРЛІГІ",
    "nameRu": "МИНИСТЕРСТВО ВНУТРЕННИХ ДЕЛ РК",
    "changeDate": "2008-03-01T13:21:45+05:00"
  }',
  '{
    "code": "00",
    "nameKz": "ҚҰЖАТ ЖАРАМДЫ",
    "nameRu": "ДОКУМЕНТ ДЕЙСТВИТЕЛЕН",
    "changeDate": "2008-03-01T13:21:45+05:00"
  }',
  (select pd.id from pd))
  RETURNING id),
  citizenship AS (
INSERT INTO person_citizenships (
  id, create_time, update_time, code, name_ru, name_kaz, change_date, deleted,
  personal_data_person_citizenship
)
VALUES (gen_random_uuid(), now(), now(), '398', 'КАЗАХСТАН', 'ҚАЗАҚСТАН', current_date, false,
  (select pd.id from pd))
  RETURNING id),
  life AS (
INSERT INTO life_status (
  id, create_time, update_time, name_ru, name_kz, code, change_date, deleted,
  personal_data_life_status
)
VALUES (gen_random_uuid(), now(), now(), 'Нормальный', 'Қалыпты', '0', current_date, false,
  (select pd.id from pd))
  RETURNING id)
SELECT pd.id
FROM pd;


-- Обновить personal_data
UPDATE personal_data
SET firstname   = 'updated_firstname',
    lastname    = 'updated_lastname',
    patronymic  = 'updated_patronymic',
    birthdate   = '1983-01-01',
    update_time = now(),
    gender      = '{
      "code": 2,
      "nameKz": "Әйел",
      "nameRu": "Женский",
      "changeDate": "2010-01-01T00:00:00+05:00"
    }',
    deleted     = false
WHERE iin = '************'
  RETURNING id;

-- birth_places
UPDATE birth_places
SET city        = 'Updated City',
    district    = '{
      "code": 9999,
      "nameKz": "ЖАҢА АУДАН",
      "nameRu": "НОВЫЙ РАЙОН",
      "changeDate": "2024-01-01T00:00:00+05:00"
    }',
    region      = '{
      "code": 8888,
      "nameKz": "ЖАҢА ОБЛЫС",
      "nameRu": "НОВАЯ ОБЛАСТЬ",
      "changeDate": "2024-01-01T00:00:00+05:00"
    }',
    country     = '{
      "code": 398,
      "nameKz": "ҚАЗАҚСТАН",
      "nameRu": "КАЗАХСТАН",
      "changeDate": "2008-03-01T13:21:44+05:00"
    }',
    update_time = now(),
    deleted     = false
WHERE personal_data_birth_place = (SELECT id
                                   FROM personal_data
                                   WHERE iin = '************');

-- reg_addresses
UPDATE reg_addresses
SET street      = 'Updated Street',
    flat        = '99',
    building    = '9B',
    city        = 'Updated Almaty',
    district    = '{
      "code": 1234,
      "nameKz": "ЖАҢА ҚАЛА",
      "nameRu": "НОВЫЙ ГОРОД",
      "changeDate": "2025-01-01T00:00:00+05:00"
    }',
    update_time = now(),
    country     = '{
      "code": 398,
      "nameKz": "ҚАЗАҚСТАН",
      "nameRu": "КАЗАХСТАН",
      "changeDate": "2024-06-10T11:16:01+05:00"
    }',
    street      = 'Pushkin St.',
    flat='12',
    district    = '{
      "code": 1234,
      "nameKz": "ЖАҢА ҚАЛА",
      "nameRu": "НОВЫЙ ГОРОД",
      "changeDate": "2025-01-01T00:00:00+05:00"
    }',
    region='{
      "code": 1930401,
      "nameKz": "ҚАРАҒАНДЫ",
      "nameRu": "КАРАГАНДА",
      "changeDate": "2024-06-10T11:16:01+05:00"
    }',
    building    = '5A',
    city        = 'Almaty',
    corpus      = '''Corpus 1''',
    status= '{
      "code": 398,
      "nameKz": "ҚАЗАҚСТАН",
      "nameRu": "ҚАЗАҚСТАН",
      "changeDate": "2024-06-10T11:16:01+05:00"
    }',

    invalidity= '{
      "code": 398,
      "nameKz": "ҚАЗАҚСТАН",
      "nameRu": "ҚАЗАҚСТАН",
      "changeDate": "2024-06-10T11:16:01+05:00"
    }',
    arcode      = '12345'
WHERE personal_data_reg_addresses = (SELECT id
                                     FROM personal_data
                                     WHERE iin = '************');

-- documents
UPDATE documents
SET number             = 'B7654321',
    surname            = 'UpdatedSurname',
    name               = 'UpdatedName',
    type_name_ru       = 'Паспорт',
    type_code          = '001',
    issue_organization = '{
      "code": "001",
      "nameKz": "ЖАҢА МЕКЕМЕ",
      "nameRu": "НОВАЯ ОРГАНИЗАЦИЯ",
      "changeDate": "2025-06-01T00:00:00+05:00"
    }',

    update_time        = now(),
    deleted            = false,
    number             = 'B7654321',
    patronymic         = 'test_rus_patronymic',
    end_date           = now(),
    surname            = 'test_rus_surname',
    name               = 'test_rus_name',
    type_name_ru       = 'УДОСТОВЕРЕНИЕ РК',
    type_code          = '002',
    type_name_kaz      = 'ҚР ЖЕКЕ КУӘЛІГІ',
    type_change_date   = current_date,
    birthdate          = '1982-11-06',
    issue_organization = '{
      "code": "002",
      "nameKz": "ҚР ІШКІ ІСТЕР МИНИСТРЛІГІ",
      "nameRu": "МИНИСТЕРСТВО ВНУТРЕННИХ ДЕЛ РК",
      "changeDate": "2008-03-01T13:21:45+05:00"
    }',
    status= '{
      "code": "00",
      "nameKz": "ҚҰЖАТ ЖАРАМДЫ",
      "nameRu": "ДОКУМЕНТ ДЕЙСТВИТЕЛЕН",
      "changeDate": "2008-03-01T13:21:45+05:00"
    }'
WHERE personal_data_documents = (SELECT id
                                 FROM personal_data
                                 WHERE iin = '************');

-- person_citizenships
UPDATE person_citizenships
SET name_ru     = 'НОВОЕ ГРАЖДАНСТВО',
    name_kaz    = 'ЖАҢА АЗАМАТТЫҚ',
    code        = '999',
    change_date = current_date,
    update_time = now(),
    code        = '999',
    name_ru     = 'НОВОЕ ГРАЖДАНСТВО',
    name_kaz    = 'ЖАҢА АЗАМАТТЫҚ',
    change_date = current_date,
    deleted     = false
WHERE personal_data_person_citizenship = (SELECT id
                                          FROM personal_data
                                          WHERE iin = '************');

-- life_status
UPDATE life_status
SET name_ru     = 'Обновлённый статус',
    name_kz     = 'Жаңартылған статус',
    code        = '1',
    change_date = current_date,
    update_time = now(),
    deleted     = false
WHERE personal_data_life_status = (SELECT id
                                   FROM personal_data
                                   WHERE iin = '************');
