{"id": "aaaa0000-0000-0000-0000-11110000aaaa", "realm": "zaman-dev", "notBefore": 0, "defaultSignatureAlgorithm": "HS512", "enabled": true, "components": {"org.keycloak.userprofile.UserProfileProvider": [{"id": "5c255473-9ac5-46ed-801b-d2902acb2e29", "providerId": "declarative-user-profile", "subComponents": {}, "config": {"kc.user.profile.config": ["{\"attributes\":[{\"name\":\"username\",\"displayName\":\"${username}\",\"validations\":{\"length\":{\"min\":3,\"max\":255},\"username-prohibited-characters\":{},\"up-username-not-idn-homograph\":{}},\"permissions\":{\"view\":[\"admin\",\"user\"],\"edit\":[\"admin\",\"user\"]},\"multivalued\":false},{\"name\":\"email\",\"displayName\":\"${email}\",\"validations\":{\"email\":{},\"length\":{\"max\":255}},\"annotations\":{},\"permissions\":{\"view\":[\"admin\",\"user\"],\"edit\":[\"admin\",\"user\"]},\"multivalued\":false},{\"name\":\"firstName\",\"displayName\":\"${firstName}\",\"validations\":{\"length\":{\"max\":255},\"person-name-prohibited-characters\":{}},\"annotations\":{},\"permissions\":{\"view\":[\"admin\",\"user\"],\"edit\":[\"admin\",\"user\"]},\"multivalued\":false},{\"name\":\"lastName\",\"displayName\":\"${lastName}\",\"validations\":{\"length\":{\"max\":255},\"person-name-prohibited-characters\":{}},\"annotations\":{},\"permissions\":{\"view\":[\"admin\",\"user\"],\"edit\":[\"admin\",\"user\"]},\"multivalued\":false},{\"name\":\"phone\",\"displayName\":\"Phone\",\"validations\":{},\"annotations\":{},\"permissions\":{\"view\":[\"admin\",\"user\"],\"edit\":[\"admin\"]},\"multivalued\":false}],\"groups\":[{\"name\":\"user-metadata\",\"displayHeader\":\"User metadata\",\"displayDescription\":\"Attributes, which refer to user metadata\"}]}"]}}]}, "roles": {"realm": [{"id": "aaaa0000-0000-0000-0000-eb6f91167e9c", "name": "blocked", "description": "Blocked user with failed checks", "composite": false, "clientRole": false, "containerId": "40df08b2-8820-4d7b-81d0-166d0856c9af", "attributes": {}}, {"id": "aaaa0000-0000-0000-0000-cdcd213f5c53", "name": "active", "description": "Active user with valid checks", "composite": false, "clientRole": false, "containerId": "40df08b2-8820-4d7b-81d0-166d0856c9af", "attributes": {}}, {"id": "aaaa0000-0000-0000-0000-a3fdf8e23bd6", "name": "zaman-admin", "description": "", "composite": false, "clientRole": false, "containerId": "40df08b2-8820-4d7b-81d0-166d0856c9af", "attributes": {}}, {"id": "aaaa0000-0000-0000-0000-a6fe15923301", "name": "not_identified", "description": "User after login without needed identification checks", "composite": false, "clientRole": false, "containerId": "40df08b2-8820-4d7b-81d0-166d0856c9af", "attributes": {}}], "client": {"realm-management": [{"id": "c4a432d9-f239-4a53-ac96-c819b99eb61b", "name": "manage-authorization", "description": "${role_manage-authorization}", "composite": false, "clientRole": true, "containerId": "af0bb6b5-9119-4860-a102-dd5e1bdf0443", "attributes": {}}, {"id": "d6a34869-fd22-46b7-9a27-aa73c2961677", "name": "query-realms", "description": "${role_query-realms}", "composite": false, "clientRole": true, "containerId": "af0bb6b5-9119-4860-a102-dd5e1bdf0443", "attributes": {}}, {"id": "654a1472-c250-48c4-91e0-195c5e2f7242", "name": "view-authorization", "description": "${role_view-authorization}", "composite": false, "clientRole": true, "containerId": "af0bb6b5-9119-4860-a102-dd5e1bdf0443", "attributes": {}}, {"id": "d88bac92-0443-4a99-94a1-213e013bcd23", "name": "impersonation", "description": "${role_impersonation}", "composite": false, "clientRole": true, "containerId": "af0bb6b5-9119-4860-a102-dd5e1bdf0443", "attributes": {}}, {"id": "386b9f4b-5b46-4be3-a0a3-1168f0f5a467", "name": "query-clients", "description": "${role_query-clients}", "composite": false, "clientRole": true, "containerId": "af0bb6b5-9119-4860-a102-dd5e1bdf0443", "attributes": {}}, {"id": "cc122afa-d180-4630-b4f3-0562f7896550", "name": "view-identity-providers", "description": "${role_view-identity-providers}", "composite": false, "clientRole": true, "containerId": "af0bb6b5-9119-4860-a102-dd5e1bdf0443", "attributes": {}}, {"id": "34df0660-665e-494b-b7ff-e371350d87a4", "name": "query-groups", "description": "${role_query-groups}", "composite": false, "clientRole": true, "containerId": "af0bb6b5-9119-4860-a102-dd5e1bdf0443", "attributes": {}}, {"id": "17e39820-c663-44da-97de-ffc7b3008550", "name": "view-events", "description": "${role_view-events}", "composite": false, "clientRole": true, "containerId": "af0bb6b5-9119-4860-a102-dd5e1bdf0443", "attributes": {}}, {"id": "fac7932b-5dad-415f-8134-0bdcec6772eb", "name": "view-users", "description": "${role_view-users}", "composite": true, "composites": {"client": {"realm-management": ["query-groups", "query-users"]}}, "clientRole": true, "containerId": "af0bb6b5-9119-4860-a102-dd5e1bdf0443", "attributes": {}}, {"id": "91812956-4764-498c-9d36-06a9484ae0cb", "name": "manage-realm", "description": "${role_manage-realm}", "composite": false, "clientRole": true, "containerId": "af0bb6b5-9119-4860-a102-dd5e1bdf0443", "attributes": {}}, {"id": "18861d95-1de7-41ab-bd8f-b830c75b9c6e", "name": "create-client", "description": "${role_create-client}", "composite": false, "clientRole": true, "containerId": "af0bb6b5-9119-4860-a102-dd5e1bdf0443", "attributes": {}}, {"id": "0023e8fc-3ee1-4f7c-9566-54bb6add16c9", "name": "query-users", "description": "${role_query-users}", "composite": false, "clientRole": true, "containerId": "af0bb6b5-9119-4860-a102-dd5e1bdf0443", "attributes": {}}, {"id": "fe710e36-05c7-48e5-9403-bdd0f0dadd9b", "name": "manage-events", "description": "${role_manage-events}", "composite": false, "clientRole": true, "containerId": "af0bb6b5-9119-4860-a102-dd5e1bdf0443", "attributes": {}}, {"id": "200e7f95-9e73-4d3a-a1ab-3d8a8275b158", "name": "view-clients", "description": "${role_view-clients}", "composite": true, "composites": {"client": {"realm-management": ["query-clients"]}}, "clientRole": true, "containerId": "af0bb6b5-9119-4860-a102-dd5e1bdf0443", "attributes": {}}, {"id": "7f7fc138-b9c8-4631-8f65-547521adbfe8", "name": "manage-clients", "description": "${role_manage-clients}", "composite": false, "clientRole": true, "containerId": "af0bb6b5-9119-4860-a102-dd5e1bdf0443", "attributes": {}}, {"id": "7f170c72-004a-4f9f-8dd0-d0cf37572882", "name": "view-realm", "description": "${role_view-realm}", "composite": false, "clientRole": true, "containerId": "af0bb6b5-9119-4860-a102-dd5e1bdf0443", "attributes": {}}, {"id": "773e0a22-e7f8-4414-9ba7-b0075841f20b", "name": "manage-identity-providers", "description": "${role_manage-identity-providers}", "composite": false, "clientRole": true, "containerId": "af0bb6b5-9119-4860-a102-dd5e1bdf0443", "attributes": {}}, {"id": "f4cb04c0-8b4c-4a74-8395-3545fc3051e9", "name": "realm-admin", "description": "${role_realm-admin}", "composite": true, "composites": {"client": {"realm-management": ["manage-authorization", "query-realms", "view-authorization", "query-clients", "impersonation", "view-identity-providers", "view-events", "query-groups", "view-users", "manage-realm", "create-client", "query-users", "manage-events", "view-clients", "view-realm", "manage-clients", "manage-identity-providers", "manage-users"]}}, "clientRole": true, "containerId": "af0bb6b5-9119-4860-a102-dd5e1bdf0443", "attributes": {}}, {"id": "a3b6d861-9883-4e4d-b919-d24e70b97798", "name": "manage-users", "description": "${role_manage-users}", "composite": false, "clientRole": true, "containerId": "af0bb6b5-9119-4860-a102-dd5e1bdf0443", "attributes": {}}], "zaman-app-admin": [], "security-admin-console": [], "admin-cli": [], "account-console": [], "broker": [{"id": "c2bbd3d6-be3a-4dd8-9c3f-9b7d464e5329", "name": "read-token", "description": "${role_read-token}", "composite": false, "clientRole": true, "containerId": "37aca496-07b1-47b2-b75c-96f1a6490d14", "attributes": {}}], "account": [{"id": "7c75a216-603e-4325-89bc-0101f916d61d", "name": "view-groups", "description": "${role_view-groups}", "composite": false, "clientRole": true, "containerId": "a9592530-d3e4-41a0-a325-6324926bb0b2", "attributes": {}}, {"id": "144ce3d3-1677-4dad-a233-a5e919164e3a", "name": "view-consent", "description": "${role_view-consent}", "composite": false, "clientRole": true, "containerId": "a9592530-d3e4-41a0-a325-6324926bb0b2", "attributes": {}}, {"id": "46fdb857-faa4-4310-866d-769c4580edca", "name": "manage-account-links", "description": "${role_manage-account-links}", "composite": false, "clientRole": true, "containerId": "a9592530-d3e4-41a0-a325-6324926bb0b2", "attributes": {}}, {"id": "b1f62ef7-bd27-4c4d-81f0-3698c84a76d1", "name": "view-applications", "description": "${role_view-applications}", "composite": false, "clientRole": true, "containerId": "a9592530-d3e4-41a0-a325-6324926bb0b2", "attributes": {}}, {"id": "3b591452-35a8-4b88-b7be-a535d83364e5", "name": "delete-account", "description": "${role_delete-account}", "composite": false, "clientRole": true, "containerId": "a9592530-d3e4-41a0-a325-6324926bb0b2", "attributes": {}}, {"id": "8513a627-5619-47dd-b43a-b622018fad33", "name": "manage-consent", "description": "${role_manage-consent}", "composite": true, "composites": {"client": {"account": ["view-consent"]}}, "clientRole": true, "containerId": "a9592530-d3e4-41a0-a325-6324926bb0b2", "attributes": {}}, {"id": "3c4521ba-a3b8-4e8a-badd-bf7fcccc9a83", "name": "view-profile", "description": "${role_view-profile}", "composite": false, "clientRole": true, "containerId": "a9592530-d3e4-41a0-a325-6324926bb0b2", "attributes": {}}, {"id": "9ac02aae-4487-4275-bc64-fd8aec070bb9", "name": "manage-account", "description": "${role_manage-account}", "composite": true, "composites": {"client": {"account": ["manage-account-links"]}}, "clientRole": true, "containerId": "a9592530-d3e4-41a0-a325-6324926bb0b2", "attributes": {}}], "zaman-app-internal": []}}, "defaultRole": {"id": "58cc7f3a-c8c3-4cec-a4ce-d380a376696f", "name": "default-roles-zaman-dev", "description": "${role_default-roles}", "composite": true, "clientRole": false, "containerId": "40df08b2-8820-4d7b-81d0-166d0856c9af"}, "scopeMappings": [{"client": "zaman-app-internal", "roles": ["blocked", "not_identified", "active"]}], "clients": [{"id": "a9592530-d3e4-41a0-a325-6324926bb0b2", "clientId": "account", "name": "${client_account}", "rootUrl": "${authBaseUrl}", "baseUrl": "/realms/zaman-dev/account/", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "redirectUris": ["/realms/zaman-dev/account/*"], "webOrigins": [], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": true, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {"post.logout.redirect.uris": "+"}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "defaultClientScopes": ["web-origins", "acr", "profile", "roles", "basic", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}, {"id": "33306a0e-b6a3-4136-8f89-eb46b4d6187e", "clientId": "account-console", "name": "${client_account-console}", "rootUrl": "${authBaseUrl}", "baseUrl": "/realms/zaman-dev/account/", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "redirectUris": ["/realms/zaman-dev/account/*"], "webOrigins": [], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": true, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {"post.logout.redirect.uris": "+", "pkce.code.challenge.method": "S256"}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "protocolMappers": [{"id": "********-3685-4ead-a471-aa1362a4dfee", "name": "audience resolve", "protocol": "openid-connect", "protocolMapper": "oidc-audience-resolve-mapper", "consentRequired": false, "config": {}}], "defaultClientScopes": ["web-origins", "acr", "profile", "roles", "basic", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}, {"id": "9e5f3d74-638c-45f4-8ae6-79ba0f22af12", "clientId": "admin-cli", "name": "${client_admin-cli}", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "redirectUris": [], "webOrigins": [], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": false, "implicitFlowEnabled": false, "directAccessGrantsEnabled": true, "serviceAccountsEnabled": false, "publicClient": true, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "defaultClientScopes": ["web-origins", "acr", "profile", "roles", "basic", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}, {"id": "37aca496-07b1-47b2-b75c-96f1a6490d14", "clientId": "broker", "name": "${client_broker}", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "redirectUris": [], "webOrigins": [], "notBefore": 0, "bearerOnly": true, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": false, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "defaultClientScopes": ["web-origins", "acr", "profile", "roles", "basic", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}, {"id": "af0bb6b5-9119-4860-a102-dd5e1bdf0443", "clientId": "realm-management", "name": "${client_realm-management}", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "redirectUris": [], "webOrigins": [], "notBefore": 0, "bearerOnly": true, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "authorizationServicesEnabled": true, "publicClient": false, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "defaultClientScopes": ["web-origins", "acr", "profile", "roles", "basic", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"], "authorizationSettings": {"allowRemoteResourceManagement": false, "policyEnforcementMode": "ENFORCING", "resources": [{"name": "client.resource.54d22494-19ea-4aed-88eb-fe9c011a4b0b", "type": "Client", "ownerManagedAccess": false, "attributes": {}, "uris": [], "scopes": [{"name": "view"}, {"name": "map-roles-client-scope"}, {"name": "configure"}, {"name": "map-roles"}, {"name": "manage"}, {"name": "map-roles-composite"}, {"name": "token-exchange"}]}, {"name": "Users", "ownerManagedAccess": false, "attributes": {}, "uris": [], "scopes": [{"name": "user-impersonated"}, {"name": "manage-group-membership"}, {"name": "view"}, {"name": "impersonate"}, {"name": "map-roles"}, {"name": "manage"}]}], "policies": [{"name": "zaman-app-admin-policy", "description": "policy to allow exchange from zaman-admin-app client to users tclient", "type": "client", "logic": "POSITIVE", "decisionStrategy": "UNANIMOUS", "config": {"clients": "[\"zaman-app-admin\"]"}}, {"name": "manage.permission.client.54d22494-19ea-4aed-88eb-fe9c011a4b0b", "type": "scope", "logic": "POSITIVE", "decisionStrategy": "UNANIMOUS", "config": {"resources": "[\"client.resource.54d22494-19ea-4aed-88eb-fe9c011a4b0b\"]", "scopes": "[\"manage\"]"}}, {"name": "configure.permission.client.54d22494-19ea-4aed-88eb-fe9c011a4b0b", "type": "scope", "logic": "POSITIVE", "decisionStrategy": "UNANIMOUS", "config": {"resources": "[\"client.resource.54d22494-19ea-4aed-88eb-fe9c011a4b0b\"]", "scopes": "[\"configure\"]"}}, {"name": "view.permission.client.54d22494-19ea-4aed-88eb-fe9c011a4b0b", "type": "scope", "logic": "POSITIVE", "decisionStrategy": "UNANIMOUS", "config": {"resources": "[\"client.resource.54d22494-19ea-4aed-88eb-fe9c011a4b0b\"]", "scopes": "[\"view\"]"}}, {"name": "map-roles.permission.client.54d22494-19ea-4aed-88eb-fe9c011a4b0b", "type": "scope", "logic": "POSITIVE", "decisionStrategy": "UNANIMOUS", "config": {"resources": "[\"client.resource.54d22494-19ea-4aed-88eb-fe9c011a4b0b\"]", "scopes": "[\"map-roles\"]"}}, {"name": "map-roles-client-scope.permission.client.54d22494-19ea-4aed-88eb-fe9c011a4b0b", "type": "scope", "logic": "POSITIVE", "decisionStrategy": "UNANIMOUS", "config": {"resources": "[\"client.resource.54d22494-19ea-4aed-88eb-fe9c011a4b0b\"]", "scopes": "[\"map-roles-client-scope\"]"}}, {"name": "map-roles-composite.permission.client.54d22494-19ea-4aed-88eb-fe9c011a4b0b", "type": "scope", "logic": "POSITIVE", "decisionStrategy": "UNANIMOUS", "config": {"resources": "[\"client.resource.54d22494-19ea-4aed-88eb-fe9c011a4b0b\"]", "scopes": "[\"map-roles-composite\"]"}}, {"name": "token-exchange.permission.client.54d22494-19ea-4aed-88eb-fe9c011a4b0b", "description": "permission to exchange zaman-admin token to user tokens", "type": "scope", "logic": "POSITIVE", "decisionStrategy": "UNANIMOUS", "config": {"resources": "[\"client.resource.54d22494-19ea-4aed-88eb-fe9c011a4b0b\"]", "scopes": "[\"token-exchange\"]", "applyPolicies": "[\"zaman-app-admin-policy\"]"}}, {"name": "manage.permission.users", "type": "scope", "logic": "POSITIVE", "decisionStrategy": "UNANIMOUS", "config": {"resources": "[\"Users\"]", "scopes": "[\"manage\"]"}}, {"name": "view.permission.users", "type": "scope", "logic": "POSITIVE", "decisionStrategy": "UNANIMOUS", "config": {"resources": "[\"Users\"]", "scopes": "[\"view\"]"}}, {"name": "map-roles.permission.users", "type": "scope", "logic": "POSITIVE", "decisionStrategy": "UNANIMOUS", "config": {"resources": "[\"Users\"]", "scopes": "[\"map-roles\"]"}}, {"name": "manage-group-membership.permission.users", "type": "scope", "logic": "POSITIVE", "decisionStrategy": "UNANIMOUS", "config": {"resources": "[\"Users\"]", "scopes": "[\"manage-group-membership\"]"}}, {"name": "admin-impersonating.permission.users", "description": "", "type": "scope", "logic": "POSITIVE", "decisionStrategy": "UNANIMOUS", "config": {"resources": "[\"Users\"]", "scopes": "[\"impersonate\"]", "applyPolicies": "[\"zaman-app-admin-policy\"]"}}, {"name": "user-impersonated.permission.users", "type": "scope", "logic": "POSITIVE", "decisionStrategy": "UNANIMOUS", "config": {"resources": "[\"Users\"]", "scopes": "[\"user-impersonated\"]"}}], "scopes": [{"name": "manage"}, {"name": "view"}, {"name": "map-roles"}, {"name": "map-roles-client-scope"}, {"name": "map-roles-composite"}, {"name": "configure"}, {"name": "token-exchange"}, {"name": "impersonate"}, {"name": "user-impersonated"}, {"name": "manage-group-membership"}], "decisionStrategy": "UNANIMOUS"}}, {"id": "2bf0703e-353a-46d5-8822-6d9ec2e2d463", "clientId": "security-admin-console", "name": "${client_security-admin-console}", "rootUrl": "${authAdminUrl}", "baseUrl": "/admin/zaman-dev/console/", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "redirectUris": ["/admin/zaman-dev/console/*"], "webOrigins": ["+"], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": true, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {"post.logout.redirect.uris": "+", "pkce.code.challenge.method": "S256"}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "protocolMappers": [{"id": "906bb89f-26b7-441d-9b17-0db1aaa907d7", "name": "locale", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "locale", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "locale", "jsonType.label": "String"}}], "defaultClientScopes": ["web-origins", "acr", "profile", "roles", "basic", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}, {"id": "9584f230-33be-4cfe-9825-c4af0d6e2f97", "clientId": "zaman-app-admin", "name": "Client for app with admin access", "description": "", "rootUrl": "", "adminUrl": "", "baseUrl": "", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "secret": "SLjF8CuDrsL8OeqhfcPORPVdjojzmKkA", "redirectUris": ["/*"], "webOrigins": ["/*"], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": true, "serviceAccountsEnabled": false, "publicClient": false, "frontchannelLogout": true, "protocol": "openid-connect", "attributes": {"oidc.ciba.grant.enabled": "false", "client.secret.creation.time": "**********", "backchannel.logout.session.required": "true", "oauth2.device.authorization.grant.enabled": "false", "backchannel.logout.revoke.offline.tokens": "false"}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": true, "nodeReRegistrationTimeout": -1, "defaultClientScopes": ["web-origins", "acr", "profile", "roles", "basic", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}, {"id": "54d22494-19ea-4aed-88eb-fe9c011a4b0b", "clientId": "zaman-app-internal", "name": "", "description": "", "rootUrl": "", "adminUrl": "", "baseUrl": "", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "secret": "**********", "redirectUris": ["/*"], "webOrigins": ["/*"], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": true, "serviceAccountsEnabled": false, "publicClient": false, "frontchannelLogout": true, "protocol": "openid-connect", "attributes": {"oidc.ciba.grant.enabled": "false", "client.secret.creation.time": "**********", "backchannel.logout.session.required": "true", "oauth2.device.authorization.grant.enabled": "false", "backchannel.logout.revoke.offline.tokens": "false"}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": -1, "protocolMappers": [{"id": "73e2befe-b7bc-4835-948f-cb5470937b8e", "name": "user-realm-role-mapper", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-realm-role-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "multivalued": "true", "userinfo.token.claim": "true", "id.token.claim": "true", "lightweight.claim": "false", "access.token.claim": "true", "claim.name": "roles", "jsonType.label": "String"}}, {"id": "ce9c3f6c-8072-4b7b-b162-ad4cb0250d91", "name": "User phone mapper", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "phone", "id.token.claim": "true", "lightweight.claim": "false", "access.token.claim": "true", "claim.name": "phone", "jsonType.label": "String"}}], "defaultClientScopes": ["basic"], "optionalClientScopes": ["web-origins", "acr", "address", "phone", "profile", "roles", "offline_access", "microprofile-jwt", "email"]}], "clientScopes": [{"id": "38f60171-613c-4b7a-a351-5bf696029cfa", "name": "profile", "description": "OpenID Connect built-in scope: profile", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "consent.screen.text": "${profileScopeConsentText}", "display.on.consent.screen": "true"}, "protocolMappers": [{"id": "2e59c9e9-a58a-40fd-86bf-b5cf88513782", "name": "middle name", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "middleName", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "middle_name", "jsonType.label": "String"}}, {"id": "20ed1d24-0200-4dc5-9b50-34d253ed80e1", "name": "gender", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "gender", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "gender", "jsonType.label": "String"}}, {"id": "8261034d-1739-4640-b6a0-9b27d576ed00", "name": "website", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "website", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "website", "jsonType.label": "String"}}, {"id": "931178a3-3f2a-4f9c-8f88-4e140b4a4475", "name": "locale", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "locale", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "locale", "jsonType.label": "String"}}, {"id": "4a6a18ab-0604-4f06-989f-a3bf673c2335", "name": "picture", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "picture", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "picture", "jsonType.label": "String"}}, {"id": "97c0a5e9-ddd6-4ec5-be3e-4338a68f76ab", "name": "updated at", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "updatedAt", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "updated_at", "jsonType.label": "long"}}, {"id": "9d767d29-b390-47a6-98a9-512299b64b21", "name": "full name", "protocol": "openid-connect", "protocolMapper": "oidc-full-name-mapper", "consentRequired": false, "config": {"id.token.claim": "true", "access.token.claim": "true", "introspection.token.claim": "true", "userinfo.token.claim": "true"}}, {"id": "d41b03f0-670e-4904-98ed-3b5f8c994995", "name": "profile", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "profile", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "profile", "jsonType.label": "String"}}, {"id": "028173e8-2070-4f8d-a6ed-1042646b78e6", "name": "username", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "username", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "preferred_username", "jsonType.label": "String"}}]}, {"id": "84247574-e51c-4003-a18a-d702fb76ca38", "name": "roles", "description": "OpenID Connect scope for add user roles to the access token", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "false", "consent.screen.text": "${rolesScopeConsentText}", "display.on.consent.screen": "true"}, "protocolMappers": [{"id": "24b0bb1b-0a68-4530-99c6-493333cef29e", "name": "realm roles", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-realm-role-mapper", "consentRequired": false, "config": {"user.attribute": "foo", "introspection.token.claim": "true", "access.token.claim": "true", "claim.name": "realm_access.roles", "jsonType.label": "String", "multivalued": "true"}}, {"id": "31021e63-f9c0-4e7c-a6d4-a279a6394296", "name": "client roles", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-client-role-mapper", "consentRequired": false, "config": {"user.attribute": "foo", "introspection.token.claim": "true", "access.token.claim": "true", "claim.name": "resource_access.${client_id}.roles", "jsonType.label": "String", "multivalued": "true"}}, {"id": "952f6eb1-6045-4bbc-be11-7491b846ec03", "name": "audience resolve", "protocol": "openid-connect", "protocolMapper": "oidc-audience-resolve-mapper", "consentRequired": false, "config": {"access.token.claim": "true", "introspection.token.claim": "true"}}]}, {"id": "57683603-c7a2-41af-a707-dd56e9048815", "name": "offline_access", "description": "OpenID Connect built-in scope: offline_access", "protocol": "openid-connect", "attributes": {"consent.screen.text": "${offlineAccessScopeConsentText}", "display.on.consent.screen": "true"}}, {"id": "ff1a29b0-c433-4187-bd22-3121f5d21691", "name": "web-origins", "description": "OpenID Connect scope for add allowed web origins to the access token", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "false", "consent.screen.text": "", "display.on.consent.screen": "false"}, "protocolMappers": [{"id": "ffacc819-726d-4ee1-a2ff-2357e2d82671", "name": "allowed web origins", "protocol": "openid-connect", "protocolMapper": "oidc-allowed-origins-mapper", "consentRequired": false, "config": {"access.token.claim": "true", "introspection.token.claim": "true"}}]}, {"id": "0bbfec62-a5e2-477d-b4b9-7021ff2a7263", "name": "basic", "description": "OpenID Connect scope for add all basic claims to the token", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "false", "display.on.consent.screen": "false"}, "protocolMappers": [{"id": "e48cb1b5-2b89-4570-b920-5c6d5654dc9c", "name": "auth_time", "protocol": "openid-connect", "protocolMapper": "oidc-usersessionmodel-note-mapper", "consentRequired": false, "config": {"user.session.note": "AUTH_TIME", "id.token.claim": "true", "introspection.token.claim": "true", "access.token.claim": "true", "claim.name": "auth_time", "jsonType.label": "long"}}, {"id": "62d21890-f045-4147-96e9-97916de3dd59", "name": "sub", "protocol": "openid-connect", "protocolMapper": "oidc-sub-mapper", "consentRequired": false, "config": {"access.token.claim": "true", "introspection.token.claim": "true"}}]}, {"id": "e86c6f1e-31c1-4263-9e25-51fee45b1154", "name": "role_list", "description": "SAML role list", "protocol": "saml", "attributes": {"consent.screen.text": "${samlRoleListScopeConsentText}", "display.on.consent.screen": "true"}, "protocolMappers": [{"id": "968547ed-4116-47cd-9ca0-6c79ce3b8cf8", "name": "role list", "protocol": "saml", "protocolMapper": "saml-role-list-mapper", "consentRequired": false, "config": {"single": "false", "attribute.nameformat": "Basic", "attribute.name": "Role"}}]}, {"id": "37be04ed-53be-447a-9687-807110458e32", "name": "email", "description": "OpenID Connect built-in scope: email", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "consent.screen.text": "${emailScopeConsentText}", "display.on.consent.screen": "true"}, "protocolMappers": [{"id": "459e8811-e7fa-4acf-924e-176a5ee2fa03", "name": "email", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "email", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "email", "jsonType.label": "String"}}]}, {"id": "36dc990d-9816-4037-bd5e-15dc06452ece", "name": "microprofile-jwt", "description": "Microprofile - JWT built-in scope", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "display.on.consent.screen": "false"}, "protocolMappers": [{"id": "e5ef2274-53cd-4e3d-8838-a3f99a893c0e", "name": "upn", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "username", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "upn", "jsonType.label": "String"}}, {"id": "b809456b-529f-49f9-b0ef-c6075d17a2ce", "name": "groups", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-realm-role-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "multivalued": "true", "user.attribute": "foo", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "groups", "jsonType.label": "String"}}]}, {"id": "73b48306-970d-430a-a79f-30ceaeba54c3", "name": "phone", "description": "OpenID Connect built-in scope: phone", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "consent.screen.text": "${phoneScopeConsentText}", "display.on.consent.screen": "true"}, "protocolMappers": [{"id": "3b5adb6a-30ed-485d-a356-ce7b9f96d481", "name": "phone number", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "phoneNumber", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "phone_number", "jsonType.label": "String"}}, {"id": "4af5aa93-e4ee-4714-b31e-7d3c372d6bea", "name": "phone number verified", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "phoneNumberVerified", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "phone_number_verified", "jsonType.label": "boolean"}}]}, {"id": "7a36fc65-92d8-4af9-839f-cf9afbfca87c", "name": "acr", "description": "OpenID Connect scope for add acr (authentication context class reference) to the token", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "false", "display.on.consent.screen": "false"}, "protocolMappers": [{"id": "b95919e4-d819-4a75-90ed-a3939f0de8d8", "name": "acr loa level", "protocol": "openid-connect", "protocolMapper": "oidc-acr-mapper", "consentRequired": false, "config": {"id.token.claim": "true", "access.token.claim": "true", "introspection.token.claim": "true"}}]}], "eventsEnabled": false, "keycloakVersion": "25.0.4", "users": [{"id": "1c872738-4187-455f-acde-c758670e93b9", "username": "70001112200", "firstName": "user", "lastName": "blocked", "emailVerified": false, "attributes": {"phone": ["+70001112200"]}, "createdTimestamp": 1726140796920, "enabled": true, "totp": false, "credentials": [], "disableableCredentialTypes": [], "requiredActions": [], "realmRoles": ["blocked", "default-roles-zaman-dev"], "notBefore": 0, "groups": []}, {"id": "3c1c3266-5411-4616-b1d3-f754861a0171", "username": "70001112233", "firstName": "user", "lastName": "active", "emailVerified": false, "attributes": {"phone": ["+70001112233"]}, "createdTimestamp": 1726140732796, "enabled": true, "totp": false, "credentials": [], "disableableCredentialTypes": [], "requiredActions": [], "realmRoles": ["default-roles-zaman-dev", "active"], "notBefore": 0, "groups": []}, {"id": "57cc7c04-3918-4c36-b40f-f32d2506d68f", "username": "70001112244", "firstName": "user", "lastName": "not_identified", "emailVerified": false, "attributes": {"phone": ["+70001112244"]}, "createdTimestamp": 1726140770207, "enabled": true, "totp": false, "credentials": [], "disableableCredentialTypes": [], "requiredActions": [], "realmRoles": ["default-roles-zaman-dev", "not_identified"], "notBefore": 0, "groups": []}, {"id": "665b3394-7f08-42d7-9b22-f9ac907d0786", "username": "zaman-app", "emailVerified": true, "createdTimestamp": 1725360265919, "enabled": true, "totp": false, "credentials": [{"id": "a98ca5d8-5e16-4518-99a0-5e4e46d86384", "type": "password", "userLabel": "My password", "createdDate": 1725360284886, "secretData": "{\"value\":\"1KQgqZtVgsqnWkXWvUJwMGZV9hPck8MlDPsOcXXESIg=\",\"salt\":\"GP515dhVVkD4EVNcnnFY9A==\",\"additionalParameters\":{}}", "credentialData": "{\"hashIterations\":5,\"algorithm\":\"argon2\",\"additionalParameters\":{\"hashLength\":[\"32\"],\"memory\":[\"7168\"],\"type\":[\"id\"],\"version\":[\"1.3\"],\"parallelism\":[\"1\"]}}"}], "disableableCredentialTypes": [], "requiredActions": [], "realmRoles": ["default-roles-zaman-dev"], "clientRoles": {"realm-management": ["realm-admin"]}, "notBefore": 0, "groups": []}]}