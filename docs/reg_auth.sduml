participant "Mobile" as mobile
participant "Gateway" as gw
participant "Users Service" as sUsers
participant "OTP Service" as sOtp
participant "Identity Manager" as im
participant "Notification Service" as sNotf

frame Вход после подтверждения
autonumber 1

mobile->gw:Ввод OTP\nauthAttemptID,otp\ndeviceInfo
gw->sUsers:authAttemptID,otp
sUsers->sOtp:authAttemptID,otp
box over sOtp: проверка кода
sOtp->sUsers:результат проверки\nphone,IIN
alt #red Неверный код, превышено число попыток
linear
sUsers->gw:
gw->mobile:Ошибка,попробуйте еще раз позже
linear off
end

space 5
box over sUsers:Проверка существования пользователя в базе сервиса\nphone,IIN, его статуса
alt #red Частичное совпадение данных
box over sUsers:Ошибка. Пользователь найден с другими учетными данными.\nПовторите авторизацию
linear
sUsers->gw:
gw->mobile: ошибка
linear off
end
alt #green Полное совпадение данных, статус active
sUsers->im:generate tokens
linear
im->sUsers:tokens [authorized]
sUsers->gw:
gw->mobile: токены доступа
linear off
end
alt Полное совпадение данных, статус pending
box over sUsers:Получение статусов проверок
alt #red Есть failed проверка. Срок актуальности проверки не истёк
box over sUsers:Ошибка. Невозможно завершить процесс регистрации
linear
sUsers->gw:
gw->mobile: ошибка
linear off
end
alt #green Проверки пройдены
sUsers->im:generate tokens
linear
im->sUsers:tokens [authorized]
sUsers->gw:
gw->mobile: токены доступа
linear off
end
end

box over sUsers:Создание учетки пользователя pending в БД
sUsers->im: createUser
im->sUsers:tokens [preauthorized]
sUsers->sNotf:регистрация устройства для push-oповещений\ndeviceInfo
space -3
sNotf->sUsers:
sUsers->gw:
gw->mobile:tokens,isNew


