@startuml registration
!include https://raw.githubusercontent.com/plantuml-stdlib/C4-PlantUML/master/C4_Container.puml

!define FONTAWESOME https://raw.githubusercontent.com/tupadr3/plantuml-icon-font-sprites/master/font-awesome-5
!include FONTAWESOME/mobile.puml

skinparam linetype ortho
skinparam {
    shadowing true
    padding 7
    ArrowThickness 1
    ArrowFontSize 10
}

AddElementTag("adapter", $bgColor = "#83B0D6")
AddElementTag("storage", $shape=database, $bgColor = "#D6AD3C")
AddElementTag("box",  $bgColor = "#D6823C")

System(mobile, "Mobile App", $sprite="mobile")
System_Ext(exKGD, "КГД")
System_Ext(exGBDFL, "ГБД ФЛ")
System_Ext(exAML, "AML")
System_Ext(exBMG, "БМГ + ЦД")
System_Ext(exVRGM, "Verigram")

Boundary(system, "Zaman backend") {
    Boundary(services, "Business Services"){
        Container(api, "API Gateway", "KrakenD", "BFF", $tags="box")
        Container(im, "Identity Manager", "KeyCloak", "Авторизация", $tags="box")
        Container(cache, "Cache", "Redis", $tags="storage")
        ContainerQueue(eb, "Event Bus", "Kafka", "Шина сообщений, CDC", $tags="box")
        Container(fs, "File Storage", "s3", $tags="storage")

        Container(sDOCS, "Documents", "", "Документы и шаблоны")
        ContainerDb(dbDOCS, "Docs DB", "Postgres", $tags="storage")
        Container(sUSERS, "Users\n", "", "Пользователи\nРегистрация")
        ContainerDb(dbUSERS, "Users DB", "Postgres", $tags="storage")
        Container(sOTP, "OTP", "", "Одноразовые пароли")
        Container(sNOTF, "Notifications", "", "Уведомления")
        ContainerDb(dbNOTF, "Notifications DB", "Postgres", $tags="storage")
        Container(sACCNT, "Accounts", "", "Счета")

        Container(aPush, "Push Sender", "", "Отправка пушей", $tags="adapter")
        Container(aSMS, "SMS Sender", "", "Отправка СМС", $tags="adapter")
        Container(aEmail, "Email Sender", "", "Отправка почты", $tags="adapter")
    }
    Container(aBMG, "БМГ", "", $tags="adapter")
    Container(aGBDFL, "ГБД ФЛ", "", $tags="adapter")
    Container(aKGD, "КГД", "", $tags="adapter")
    Container(aAML, "AML", "", $tags="adapter")
    Container(aVRGM, "Verigram", "", $tags="adapter")
}
Rel_Down(mobile, api, "REST")
Rel(api, sDOCS, " ")
Rel(api, sUSERS, " ")
Rel(api, sACCNT, " ")
Rel(api, sOTP, " ")
Rel_Right(api, im, "authorizes in")

Rel(sNOTF, dbNOTF, " ")
Rel(sNOTF, aPush, "delegates")
Rel(sNOTF, aSMS, "delegates")
Rel(sNOTF, aEmail, "delegates")

Rel_Up(sUSERS, im, " ")
Rel(sUSERS, sDOCS, " ")
Rel(sUSERS, sACCNT, " ")
Rel(sUSERS, sOTP, " ")
Rel(sUSERS, sNOTF, " ")
Rel(sUSERS, aBMG, " ")
Rel(sUSERS, aGBDFL, " ")
Rel(sUSERS, aKGD, " ")
Rel(sUSERS, aAML, " ")
Rel(sUSERS, aVRGM, " ")
Rel_Left(sUSERS, dbUSERS, "stores")

Rel(sDOCS, eb, "produce")
'Rel(sUSERS, eb, "consume")
Rel(sACCNT, eb, "consume")

Rel(sDOCS, sOTP, " ")
Rel(sDOCS, sNOTF, " ")
Rel_Left(sDOCS, dbDOCS, " ")
Rel_U(sDOCS, fs, " ")

Rel(sOTP, cache, "stores")

Rel(aBMG, exBMG, " ")
Rel(aGBDFL, exGBDFL, " ")
Rel(aKGD, exKGD, " ")
Rel(aAML, exAML, " ")
Rel(aVRGM, exVRGM, " ")

SHOW_FLOATING_LEGEND()
@enduml
