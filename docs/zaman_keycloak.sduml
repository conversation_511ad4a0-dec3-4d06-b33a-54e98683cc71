title keycloak new user registration

app->gw: POST /auth/confirm\nattemptID, code
gw->users: attemptID, code
users->otp: attemptID, code
otp->users: OK, phone, deviceInfo
users->adapter:create user by phone
adapter->adapter: check adminToken in local cache
alt get adminToken if not exists or expired
adapter->keycloak: get admin token by creds\nPOST /realms/zaman-dev/protocol/openid-connect/token
keycloak->adapter: appAdminToken
adapter->adapter: save appAdminToken
end
adapter->keycloak: create user\nPOST admin/realms/zaman-dev/users
keycloak-->adapter:
adapter->keycloak: assign user role [not_identified]\nPOST /admin/realms/zaman-dev/users/<user-id>/role-mappings/realm
keycloak-->adapter:
adapter->users: OK
users->adapter: create tokens
adapter->keycloak: token exchange to impersonate \nPOST realms/zaman-dev/protocol/openid-connect/token
keycloak->adapter: JWT tokens
adapter->users: JWT tokens
users->gw: JWT tokens
gw->app: JWT tokens