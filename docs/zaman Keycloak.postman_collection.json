{"info": {"_postman_id": "057b0049-9c2e-42b5-ae5f-88ea8ed9ac1b", "name": "Zaman Keycloak", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "7586721"}, "item": [{"name": "1. Get app-admin token", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Save access_token\", function () {", "    var jsonData = pm.response.json();", "    //pm.environment.set(\"admin_token\", jsonData.access_token);", "    pm.globals.set(\"admin_token\", jsonData.access_token);", "});"], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "client_id", "value": "zaman-app-admin", "type": "text"}, {"key": "client_secret", "value": "SLjF8CuDrsL8OeqhfcPORPVdjojzmKkA", "type": "text"}, {"key": "username", "value": "zaman-app", "type": "text"}, {"key": "password", "value": "zaman", "type": "text"}, {"key": "grant_type", "value": "password", "type": "text"}]}, "url": {"raw": "https://keycloak.zaman.redmadrobot.com/realms/zaman-dev/protocol/openid-connect/token", "protocol": "https", "host": ["keycloak", "zaman", "redmadrobot", "com"], "path": ["realms", "zaman-dev", "protocol", "openid-connect", "token"]}}, "response": []}, {"name": "2. Get user impersonated token by app-admin token", "event": [{"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}, {"listen": "test", "script": {"exec": ["pm.test(\"Save user_token\", function () {", "    var jsonData = pm.response.json();", "    pm.globals.set(\"user_access_token\", jsonData.access_token);", "    pm.globals.set(\"user_refresh_token\", jsonData.refresh_token);", "});"], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "client_id", "value": "zaman-app-admin", "type": "text"}, {"key": "client_secret", "value": "SLjF8CuDrsL8OeqhfcPORPVdjojzmKkA", "type": "text"}, {"key": "subject_token", "value": "{{admin_token}}", "type": "text"}, {"key": "grant_type", "value": "urn:ietf:params:oauth:grant-type:token-exchange", "type": "text"}, {"key": "audience", "value": "zaman-app-internal", "type": "text"}, {"key": "requested_subject", "value": "70001112233", "type": "text"}]}, "url": {"raw": "https://keycloak.zaman.redmadrobot.com/realms/zaman-dev/protocol/openid-connect/token", "protocol": "https", "host": ["keycloak", "zaman", "redmadrobot", "com"], "path": ["realms", "zaman-dev", "protocol", "openid-connect", "token"]}}, "response": []}, {"name": "3. Create user", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{admin_token}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"id\": \"\",\n    \"username\": \"+79991112235\",\n    \"enabled\": true,\n    \"attributes\": {\n        \"phone\": [\n            \"+79991112235\"\n        ]\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://keycloak.zaman.redmadrobot.com/admin/realms/zaman-dev/users", "protocol": "https", "host": ["keycloak", "zaman", "redmadrobot", "com"], "path": ["admin", "realms", "zaman-dev", "users"]}}, "response": []}, {"name": "7. Delete session", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{admin_token}}", "type": "string"}]}, "method": "DELETE", "header": [], "url": {"raw": "https://keycloak.zaman.redmadrobot.com/admin/realms/zaman-dev/sessions/45fdf4be-4104-4538-9cad-d5e81e0b29d9", "protocol": "https", "host": ["keycloak", "zaman", "redmadrobot", "com"], "path": ["admin", "realms", "zaman-dev", "sessions", "45fdf4be-4104-4538-9cad-d5e81e0b29d9"]}}, "response": []}, {"name": "4. Assign user role", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{admin_token}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "[\n    {\"id\":\"d32142f8-fcee-4bcf-bec3-cdcd213f5c53\",\"name\":\"active\"}\n]", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://keycloak.zaman.redmadrobot.com/admin/realms/zaman-dev/users/9189c723-a5d3-4b4e-8755-74d228be1ebb/role-mappings/realm", "protocol": "https", "host": ["keycloak", "zaman", "redmadrobot", "com"], "path": ["admin", "realms", "zaman-dev", "users", "9189c723-a5d3-4b4e-8755-74d228be1ebb", "role-mappings", "realm"]}}, "response": []}, {"name": "5. Unassign user role", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{admin_token}}", "type": "string"}]}, "method": "DELETE", "header": [], "body": {"mode": "raw", "raw": "[\n    {\"id\":\"d32142f8-fcee-4bcf-bec3-cdcd213f5c53\",\"name\":\"active\"}\n]", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://keycloak.zaman.redmadrobot.com/admin/realms/zaman-dev/users/9189c723-a5d3-4b4e-8755-74d228be1ebb/role-mappings/realm", "protocol": "https", "host": ["keycloak", "zaman", "redmadrobot", "com"], "path": ["admin", "realms", "zaman-dev", "users", "9189c723-a5d3-4b4e-8755-74d228be1ebb", "role-mappings", "realm"]}}, "response": []}, {"name": "6. Refresh user Tokens", "request": {"method": "POST", "header": [], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "client_id", "value": "zaman-app-admin", "type": "text"}, {"key": "client_secret", "value": "SLjF8CuDrsL8OeqhfcPORPVdjojzmKkA", "type": "text"}, {"key": "refresh_token", "value": "{{user_refresh_token}}", "type": "text"}, {"key": "grant_type", "value": "refresh_token", "type": "text"}, {"key": "audience", "value": "zaman-app-internal", "type": "text", "disabled": true}, {"key": "client_secret", "value": "rKQvhPBwmIc2DWAFmMUtqjQJ5FVyfWLc", "type": "text", "disabled": true}, {"key": "client_id", "value": "zaman-app-internal", "type": "text", "disabled": true}]}, "url": {"raw": "https://keycloak.zaman.redmadrobot.com/realms/zaman-dev/protocol/openid-connect/token", "protocol": "https", "host": ["keycloak", "zaman", "redmadrobot", "com"], "path": ["realms", "zaman-dev", "protocol", "openid-connect", "token"]}}, "response": []}, {"name": "0. Get app-interntal token (необязательная проверка)", "request": {"method": "POST", "header": [], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "client_id", "value": "zaman-app-admin", "type": "text"}, {"key": "client_secret", "value": "SLjF8CuDrsL8OeqhfcPORPVdjojzmKkA", "type": "text"}, {"key": "data-urlencode", "value": "grant_type=urn:ietf:params:oauth:grant-type:token-exchange", "type": "text", "disabled": true}, {"key": "subject_token", "value": "eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJud0M3aFFkeGNNenQtTXdpTXY5dkh2WGRoUU92VmhBd3RqblV2MUxVc0lBIn0.*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.XGG1Pyo9dI2gcAyvnc_yD8nKlJi5096Kw9_tOvGt9BEe-PiosfQyxwPSQV__AFOHtdv2fVjBNNED3NLCwmEO0RFplhq8xCIzOiqoKYMWs8Ta9ISHFzWYsD7DLt9zRbMcjUZ8cDvMEzCEIXKRVMTQAzjx_s2uh1Z6aN2eybqbCpfZUSBRhMLdE_7NAugYAkXwOjPmmRY3xSobCgNhF4COjH4G3gDzbYRyqc0owkn_fq6KURItKK4WTyjW7FL9-K09-cQ3leOQyWhmhqBgs1j2e2lneHUKt7-bSgc7TGVa59UOnw2z1yRIlfNkLHGgPXt8x9ug5lLZEbpZ0e5QJRRd6g", "type": "text"}, {"key": "grant_type", "value": "urn:ietf:params:oauth:grant-type:token-exchange", "type": "text"}, {"key": "audience", "value": "zaman-app-internal", "type": "text"}]}, "url": {"raw": "https://keycloak.zaman.redmadrobot.com/realms/zaman-dev/protocol/openid-connect/token", "protocol": "https", "host": ["keycloak", "zaman", "redmadrobot", "com"], "path": ["realms", "zaman-dev", "protocol", "openid-connect", "token"]}}, "response": []}, {"name": "0. Get roles", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{admin_token}}", "type": "string"}]}, "method": "GET", "header": [], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://keycloak.zaman.redmadrobot.com/admin/realms/zaman-dev/roles", "protocol": "https", "host": ["keycloak", "zaman", "redmadrobot", "com"], "path": ["admin", "realms", "zaman-dev", "roles"]}}, "response": []}]}