package collection

// Добавь в этот файл коды ошибок для сервиса в формате Code{ServiceName} - после запусти генерацию ошибок для сервиса
// meroving gen-errs - после того - раскоментируй код с функцией и заполни errMap как указано в примере

const (
// CodeCollectionSomeErr = "SomeError"
)

/*
   func CollectionErrs() CollectionErrors {
   	return &CollectionErrorsList{
   		errMap: map[string]internalErrs.Reason{
   			CodeCollectionSomeErr:                 {1, CodeCollectionSomeErr, errs.TypeFailedPrecondition, "Your error description"},
   		},
   	}
   }
*/
