// Code generated by git.redmadrobot.com/backend-go/meroving. DO NOT EDIT.
package errs

import "git.redmadrobot.com/backend-go/rmr-pkg/core/errs"

type Reason struct {
	ErrNum      int
	Code        string
	Type        errs.Type
	Description string
}

func (e Reason) Err() error {
	return errs.Reasons(e.Code, e.Type, e.ErrNum)
}

func (e Reason) WithDetails(details map[string]string) error {
	return errs.Reasons(e.Code, e.Type, e.ErrNum, details).WithDetails(details)
}
