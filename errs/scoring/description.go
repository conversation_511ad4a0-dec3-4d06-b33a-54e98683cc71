package scoring

// Добавь в этот файл коды ошибок для сервиса в формате Code{ServiceName} - после запусти генерацию ошибок для сервиса
// meroving gen-errs - после того - раскоментируй код с функцией и заполни errMap как указано в примере

const (
// CodeScoringSomeErr = "SomeError"
)

/*
   func ScoringErrs() ScoringErrors {
   	return &ScoringErrorsList{
   		errMap: map[string]internalErrs.Reason{
   			CodeScoringSomeErr:                 {1, CodeScoringSomeErr, errs.TypeFailedPrecondition, "Your error description"},
   		},
   	}
   }
*/
