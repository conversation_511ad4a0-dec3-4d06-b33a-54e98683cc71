package bsasBridge

// Добавь в этот файл коды ошибок для сервиса в формате Code{ServiceName} - после запусти генерацию ошибок для сервиса
// meroving gen-errs - после того - раскоментируй код с функцией и заполни errMap как указано в примере

const (
// CodeBsasBridgeSomeErr = "SomeError"
)

/*
   func BsasBridgeErrs() BsasBridgeErrors {
   	return &BsasBridgeErrorsList{
   		errMap: map[string]internalErrs.Reason{
   			CodeBsasBridgeSomeErr:                 {1, CodeBsasBridgeSomeErr, errs.TypeFailedPrecondition, "Your error description"},
   		},
   	}
   }
*/
