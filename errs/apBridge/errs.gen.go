// Code generated by git.redmadrobot.com/backend-go/meroving. DO NOT EDIT.
package apBridge

import (
	internalErrs "git.redmadrobot.com/zaman/backend/zaman/errs"
)

type (
	ApBridgeErrorsList struct {
		errMap map[string]internalErrs.Reason
	}

	ApBridgeErrors interface {
	        PhoneNotFoundError() error
	        PhoneNotFoundDetailedError(details map[string]string) error
	}
)
func (e *ApBridgeErrorsList) PhoneNotFoundError() error {
	return e.errMap[CodeApBridgePhoneNotFound].Err()
}

func (e *ApBridgeErrorsList) PhoneNotFoundDetailedError(details map[string]string) error {
	err := e.errMap[CodeApBridgePhoneNotFound]
	return err.WithDetails(details)
}