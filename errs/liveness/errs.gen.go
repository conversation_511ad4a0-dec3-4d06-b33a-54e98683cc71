// Code generated by git.redmadrobot.com/backend-go/meroving. DO NOT EDIT.
package liveness

import (
	internalErrs "git.redmadrobot.com/zaman/backend/zaman/errs"
)

type (
	LivenessErrorsList struct {
		errMap map[string]internalErrs.Reason
	}

	LivenessErrors interface {
	        InputDataIncorrectError() error
	        InputDataIncorrectDetailedError(details map[string]string) error
	        PassedByQRLinkError() error
	        PassedByQRLinkDetailedError(details map[string]string) error
	        VerifyFailedError() error
	        VerifyFailedDetailedError(details map[string]string) error
	}
)
func (e *LivenessErrorsList) InputDataIncorrectError() error {
	return e.errMap[CodeLivenessInputDataIncorrect].Err()
}

func (e *LivenessErrorsList) InputDataIncorrectDetailedError(details map[string]string) error {
	err := e.errMap[CodeLivenessInputDataIncorrect]
	return err.WithDetails(details)
}
func (e *LivenessErrorsList) PassedByQRLinkError() error {
	return e.errMap[CodeLivenessPassedByQRLink].Err()
}

func (e *LivenessErrorsList) PassedByQRLinkDetailedError(details map[string]string) error {
	err := e.errMap[CodeLivenessPassedByQRLink]
	return err.WithDetails(details)
}
func (e *LivenessErrorsList) VerifyFailedError() error {
	return e.errMap[CodeLivenessVerifyFailed].Err()
}

func (e *LivenessErrorsList) VerifyFailedDetailedError(details map[string]string) error {
	err := e.errMap[CodeLivenessVerifyFailed]
	return err.WithDetails(details)
}