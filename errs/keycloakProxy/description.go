package keycloakProxy

import (
	"git.redmadrobot.com/backend-go/rmr-pkg/core/errs"
	internalErrs "git.redmadrobot.com/zaman/backend/zaman/errs"
)

const (
	CodeKeycloakProxyInvalidRefreshToken        = "InvalidRefreshToken"
	CodeKeycloakProxyRefreshTokenExpired        = "RefreshTokenExpired"
	CodeKeycloakProxyKeycloakUserNotFound       = "KeycloakUserNotFound"
	CodeKeycloakProxyRealmNotFoundNotFoundInCtx = "RealmNotFoundInCtx"
)

func KeycloakProxyErrs() KeycloakProxyErrors {
	return &KeycloakProxyErrorsList{
		errMap: map[string]internalErrs.Reason{
			CodeKeycloakProxyInvalidRefreshToken:        {1, CodeKeycloakProxyInvalidRefreshToken, errs.TypeIllegalArgument, "Недействительный refresh token"},
			CodeKeycloakProxyRefreshTokenExpired:        {0, CodeKeycloakProxyRefreshTokenExpired, errs.TypeInternal, "Истек срок refresh token"},
			CodeKeycloakProxyKeycloakUserNotFound:       {0, CodeKeycloakProxyKeycloakUserNotFound, errs.TypeInternal, "Пользователь не найден в клиенте Keycloak"},
			CodeKeycloakProxyRealmNotFoundNotFoundInCtx: {0, CodeKeycloakProxyRealmNotFoundNotFoundInCtx, errs.TypeInternal, "Realm не найден в контексте"},
		},
	}
}
