// Code generated by git.redmadrobot.com/backend-go/meroving. DO NOT EDIT.
package dictionary

import (
	internalErrs "git.redmadrobot.com/zaman/backend/zaman/errs"
)

type (
	DictionaryErrorsList struct {
		errMap map[string]internalErrs.Reason
	}

	DictionaryErrors interface {
	        DocNotFoundError() error
	        DocNotFoundDetailedError(details map[string]string) error
	        JobAlreadyRunningError() error
	        JobAlreadyRunningDetailedError(details map[string]string) error
	        JobNotExistsError() error
	        JobNotExistsDetailedError(details map[string]string) error
	        JobNotWorkedError() error
	        JobNotWorkedDetailedError(details map[string]string) error
	        NotFoundError() error
	        NotFoundDetailedError(details map[string]string) error
	        WrongDataSchemaError() error
	        WrongDataSchemaDetailedError(details map[string]string) error
	        WrongJSONError() error
	        WrongJSONDetailedError(details map[string]string) error
	        WrongUUIDError() error
	        WrongUUIDDetailedError(details map[string]string) error
	}
)
func (e *DictionaryErrorsList) DocNotFoundError() error {
	return e.errMap[CodeDictionaryDocNotFound].Err()
}

func (e *DictionaryErrorsList) DocNotFoundDetailedError(details map[string]string) error {
	err := e.errMap[CodeDictionaryDocNotFound]
	return err.WithDetails(details)
}
func (e *DictionaryErrorsList) JobAlreadyRunningError() error {
	return e.errMap[CodeDictionaryJobAlreadyRunning].Err()
}

func (e *DictionaryErrorsList) JobAlreadyRunningDetailedError(details map[string]string) error {
	err := e.errMap[CodeDictionaryJobAlreadyRunning]
	return err.WithDetails(details)
}
func (e *DictionaryErrorsList) JobNotExistsError() error {
	return e.errMap[CodeDictionaryJobNotExists].Err()
}

func (e *DictionaryErrorsList) JobNotExistsDetailedError(details map[string]string) error {
	err := e.errMap[CodeDictionaryJobNotExists]
	return err.WithDetails(details)
}
func (e *DictionaryErrorsList) JobNotWorkedError() error {
	return e.errMap[CodeDictionaryJobNotWorked].Err()
}

func (e *DictionaryErrorsList) JobNotWorkedDetailedError(details map[string]string) error {
	err := e.errMap[CodeDictionaryJobNotWorked]
	return err.WithDetails(details)
}
func (e *DictionaryErrorsList) NotFoundError() error {
	return e.errMap[CodeDictionaryNotFound].Err()
}

func (e *DictionaryErrorsList) NotFoundDetailedError(details map[string]string) error {
	err := e.errMap[CodeDictionaryNotFound]
	return err.WithDetails(details)
}
func (e *DictionaryErrorsList) WrongDataSchemaError() error {
	return e.errMap[CodeDictionaryWrongDataSchema].Err()
}

func (e *DictionaryErrorsList) WrongDataSchemaDetailedError(details map[string]string) error {
	err := e.errMap[CodeDictionaryWrongDataSchema]
	return err.WithDetails(details)
}
func (e *DictionaryErrorsList) WrongJSONError() error {
	return e.errMap[CodeDictionaryWrongJSON].Err()
}

func (e *DictionaryErrorsList) WrongJSONDetailedError(details map[string]string) error {
	err := e.errMap[CodeDictionaryWrongJSON]
	return err.WithDetails(details)
}
func (e *DictionaryErrorsList) WrongUUIDError() error {
	return e.errMap[CodeDictionaryWrongUUID].Err()
}

func (e *DictionaryErrorsList) WrongUUIDDetailedError(details map[string]string) error {
	err := e.errMap[CodeDictionaryWrongUUID]
	return err.WithDetails(details)
}