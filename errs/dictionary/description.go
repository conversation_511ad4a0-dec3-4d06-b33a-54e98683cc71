package dictionary

import (
	"git.redmadrobot.com/backend-go/rmr-pkg/core/errs"
	internalErrs "git.redmadrobot.com/zaman/backend/zaman/errs"
)

// Добавь в этот файл коды ошибок для сервиса в формате Status{ServiceName} - после запусти генерацию ошибок для сервиса
// meroving gen-errs - после того - раскомментируй код с функцией и заполни errMap как указано в примере

const (
	CodeDictionaryNotFound          = "DictionaryNotFound"
	CodeDictionaryDocNotFound       = "DocumentNotFound"
	CodeDictionaryWrongDataSchema   = "WrongDocumentDataSchema"
	CodeDictionaryWrongUUID         = "WrongUUIDFormat"
	CodeDictionaryWrongJSON         = "WrongJSONFormat"
	CodeDictionaryJobNotExists      = "JobNotExists"
	CodeDictionaryJobAlreadyRunning = "JobAlreadyRunning"
	CodeDictionaryJobNotWorked      = "JobNotWorked"
)

func DictionaryErrs() DictionaryErrors {
	return &DictionaryErrorsList{
		errMap: map[string]internalErrs.Reason{
			CodeDictionaryNotFound:          {1, CodeDictionaryNotFound, errs.TypeNotFound, "Dictionary not found"},
			CodeDictionaryDocNotFound:       {2, CodeDictionaryDocNotFound, errs.TypeNotFound, "Document not found"},
			CodeDictionaryWrongDataSchema:   {3, CodeDictionaryWrongDataSchema, errs.TypeIllegalArgument, "Document data not valid"},
			CodeDictionaryWrongUUID:         {4, CodeDictionaryWrongUUID, errs.TypeIllegalArgument, "Wrong UUID format in query"},
			CodeDictionaryWrongJSON:         {5, CodeDictionaryWrongJSON, errs.TypeIllegalArgument, "Wrong JSON format in query"},
			CodeDictionaryJobNotExists:      {10, CodeDictionaryJobNotExists, errs.TypeNotFound, "Job not found"},
			CodeDictionaryJobAlreadyRunning: {11, CodeDictionaryJobAlreadyRunning, errs.TypeConflict, "Job already running"},
			CodeDictionaryJobNotWorked:      {12, CodeDictionaryJobNotWorked, errs.TypeConflict, "Job not worked"},
		},
	}
}
