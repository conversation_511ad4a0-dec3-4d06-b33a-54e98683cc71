// Code generated by git.redmadrobot.com/backend-go/meroving. DO NOT EDIT.
package juicyscoreBridge

import (
	internalErrs "git.redmadrobot.com/zaman/backend/zaman/errs"
)

type (
	JuicyscoreBridgeErrorsList struct {
		errMap map[string]internalErrs.Reason
	}

	JuicyscoreBridgeErrors interface {
	        CodeJuiceScoreRequestErrorError() error
	        CodeJuiceScoreRequestErrorDetailedError(details map[string]string) error
	}
)
func (e *JuicyscoreBridgeErrorsList) CodeJuiceScoreRequestErrorError() error {
	return e.errMap[CodeJuiceScoreRequestError].Err()
}

func (e *JuicyscoreBridgeErrorsList) CodeJuiceScoreRequestErrorDetailedError(details map[string]string) error {
	err := e.errMap[CodeJuiceScoreRequestError]
	return err.WithDetails(details)
}