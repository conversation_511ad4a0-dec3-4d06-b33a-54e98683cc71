package juicyscoreBridge

import (
	"git.redmadrobot.com/backend-go/rmr-pkg/core/errs"

	internalErrs "git.redmadrobot.com/zaman/backend/zaman/errs"
)

const (
	CodeJuiceScoreRequestError = "JuicyScoreRequestError"
)

func JuicyscoreBridgeErrs() JuicyscoreBridgeErrors {
	return &JuicyscoreBridgeErrorsList{
		errMap: map[string]internalErrs.Reason{
			CodeJuiceScoreRequestError: {1, CodeJuiceScoreRequestError, errs.TypeInternal, "JuicyScore request error"},
		},
	}
}
