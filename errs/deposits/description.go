package deposits

import (
	"git.redmadrobot.com/backend-go/rmr-pkg/core/errs"
	internalErrs "git.redmadrobot.com/zaman/backend/zaman/errs"
)

// Добавь в этот файл коды ошибок для сервиса в формате Code{ServiceName} - после запусти генерацию ошибок для сервиса
// meroving gen-errs - после того - раскоментируй код с функцией и заполни errMap как указано в примере

const (
	CodeDepositProductNotFound   = "DepositProductNotFound"
	CodeDepositConditionNotFound = "DepositConditionNotFound"
)

func DepositsErrs() DepositsErrors {
	return &DepositsErrorsList{
		errMap: map[string]internalErrs.Reason{
			CodeDepositProductNotFound:   {0, CodeDepositProductNotFound, errs.TypeIllegalArgument, "Продукт депозита не найден"},
			CodeDepositConditionNotFound: {0, CodeDepositConditionNotFound, errs.TypeIllegalArgument, "Условия по депозиту не найдены"},
		},
	}
}
