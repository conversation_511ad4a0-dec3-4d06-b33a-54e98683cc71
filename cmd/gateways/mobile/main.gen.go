// Code generated by git.redmadrobot.com/backend-go/meroving. DO NOT EDIT.
package main

import (
	"context"
	"fmt"
	"log"
	"time"

	"github.com/google/uuid"

	gw "git.redmadrobot.com/zaman/backend/zaman/gateways/mobile"
	"git.redmadrobot.com/zaman/backend/zaman/gateways/mobile/openapi"

	"git.redmadrobot.com/backend-go/rmr-pkg/grpcx"

	"git.redmadrobot.com/zaman/backend/zaman/config/gateways/mobile"
	cfgAltScoreBridge "git.redmadrobot.com/zaman/backend/zaman/config/services/alt-score-bridge"
	cfgAmlBridge "git.redmadrobot.com/zaman/backend/zaman/config/services/aml-bridge"
	cfgApBridge "git.redmadrobot.com/zaman/backend/zaman/config/services/ap-bridge"
	cfgBsasBridge "git.redmadrobot.com/zaman/backend/zaman/config/services/bsas-bridge"
	cfgBtsBridge "git.redmadrobot.com/zaman/backend/zaman/config/services/bts-bridge"
	cfgCardsAccounts "git.redmadrobot.com/zaman/backend/zaman/config/services/cards-accounts"
	cfgCollection "git.redmadrobot.com/zaman/backend/zaman/config/services/collection"
	cfgColvirBridge "git.redmadrobot.com/zaman/backend/zaman/config/services/colvir-bridge"
	cfgDeposits "git.redmadrobot.com/zaman/backend/zaman/config/services/deposits"
	cfgDictionary "git.redmadrobot.com/zaman/backend/zaman/config/services/dictionary"
	cfgDocuments "git.redmadrobot.com/zaman/backend/zaman/config/services/documents"
	cfgFileGuard "git.redmadrobot.com/zaman/backend/zaman/config/services/file-guard"
	cfgJiraBridge "git.redmadrobot.com/zaman/backend/zaman/config/services/jira-bridge"
	cfgJuicyscoreBridge "git.redmadrobot.com/zaman/backend/zaman/config/services/juicyscore-bridge"
	cfgKeycloakProxy "git.redmadrobot.com/zaman/backend/zaman/config/services/keycloak-proxy"
	cfgKgdBridge "git.redmadrobot.com/zaman/backend/zaman/config/services/kgd-bridge"
	cfgLiveness "git.redmadrobot.com/zaman/backend/zaman/config/services/liveness"
	cfgLoans "git.redmadrobot.com/zaman/backend/zaman/config/services/loans"
	cfgNotifications "git.redmadrobot.com/zaman/backend/zaman/config/services/notifications"
	cfgOtp "git.redmadrobot.com/zaman/backend/zaman/config/services/otp"
	cfgPayments "git.redmadrobot.com/zaman/backend/zaman/config/services/payments"
	cfgPaymentsSme "git.redmadrobot.com/zaman/backend/zaman/config/services/payments-sme"
	cfgPkbBridge "git.redmadrobot.com/zaman/backend/zaman/config/services/pkb-bridge"
	cfgProcessingBridge "git.redmadrobot.com/zaman/backend/zaman/config/services/processing-bridge"
	cfgQazpostBridge "git.redmadrobot.com/zaman/backend/zaman/config/services/qazpost-bridge"
	cfgScoring "git.redmadrobot.com/zaman/backend/zaman/config/services/scoring"
	cfgSeonBridge "git.redmadrobot.com/zaman/backend/zaman/config/services/seon-bridge"
	cfgSmsBridge "git.redmadrobot.com/zaman/backend/zaman/config/services/sms-bridge"
	cfgSprBridge "git.redmadrobot.com/zaman/backend/zaman/config/services/spr-bridge"
	cfgTaskManager "git.redmadrobot.com/zaman/backend/zaman/config/services/task-manager"
	cfgUsers "git.redmadrobot.com/zaman/backend/zaman/config/services/users"
	pbAltScoreBridge "git.redmadrobot.com/zaman/backend/zaman/specs/proto/alt-score-bridge"
	pbAmlBridge "git.redmadrobot.com/zaman/backend/zaman/specs/proto/aml-bridge"
	pbApBridge "git.redmadrobot.com/zaman/backend/zaman/specs/proto/ap-bridge"
	pbBsasBridge "git.redmadrobot.com/zaman/backend/zaman/specs/proto/bsas-bridge"
	pbBtsBridge "git.redmadrobot.com/zaman/backend/zaman/specs/proto/bts-bridge"
	pbCardsAccounts "git.redmadrobot.com/zaman/backend/zaman/specs/proto/cards-accounts"
	pbCollection "git.redmadrobot.com/zaman/backend/zaman/specs/proto/collection"
	pbColvirBridge "git.redmadrobot.com/zaman/backend/zaman/specs/proto/colvir-bridge"
	pbDeposits "git.redmadrobot.com/zaman/backend/zaman/specs/proto/deposits"
	pbDictionary "git.redmadrobot.com/zaman/backend/zaman/specs/proto/dictionary"
	pbDocuments "git.redmadrobot.com/zaman/backend/zaman/specs/proto/documents"
	pbFileGuard "git.redmadrobot.com/zaman/backend/zaman/specs/proto/file-guard"
	pbJiraBridge "git.redmadrobot.com/zaman/backend/zaman/specs/proto/jira-bridge"
	pbJuicyscoreBridge "git.redmadrobot.com/zaman/backend/zaman/specs/proto/juicyscore-bridge"
	pbKeycloakProxy "git.redmadrobot.com/zaman/backend/zaman/specs/proto/keycloak-proxy"
	pbKgdBridge "git.redmadrobot.com/zaman/backend/zaman/specs/proto/kgd-bridge"
	pbLiveness "git.redmadrobot.com/zaman/backend/zaman/specs/proto/liveness"
	pbLoans "git.redmadrobot.com/zaman/backend/zaman/specs/proto/loans"
	pbNotifications "git.redmadrobot.com/zaman/backend/zaman/specs/proto/notifications"
	pbOtp "git.redmadrobot.com/zaman/backend/zaman/specs/proto/otp"
	pbPayments "git.redmadrobot.com/zaman/backend/zaman/specs/proto/payments"
	pbPaymentsSme "git.redmadrobot.com/zaman/backend/zaman/specs/proto/payments-sme"
	pbPkbBridge "git.redmadrobot.com/zaman/backend/zaman/specs/proto/pkb-bridge"
	pbProcessingBridge "git.redmadrobot.com/zaman/backend/zaman/specs/proto/processing-bridge"
	pbQazpostBridge "git.redmadrobot.com/zaman/backend/zaman/specs/proto/qazpost-bridge"
	pbScoring "git.redmadrobot.com/zaman/backend/zaman/specs/proto/scoring"
	pbSeonBridge "git.redmadrobot.com/zaman/backend/zaman/specs/proto/seon-bridge"
	pbSmsBridge "git.redmadrobot.com/zaman/backend/zaman/specs/proto/sms-bridge"
	pbSprBridge "git.redmadrobot.com/zaman/backend/zaman/specs/proto/spr-bridge"
	pbTaskManager "git.redmadrobot.com/zaman/backend/zaman/specs/proto/task-manager"
	pbUsers "git.redmadrobot.com/zaman/backend/zaman/specs/proto/users"

	"git.redmadrobot.com/backend-go/rmr-pkg/httpx"
	"git.redmadrobot.com/backend-go/rmr-pkg/providers/otelx"
	"git.redmadrobot.com/backend-go/rmr-pkg/providers/sentry"
	"git.redmadrobot.com/backend-go/rmr-pkg/providers/vault"
	"go.opentelemetry.io/otel"

	"git.redmadrobot.com/backend-go/rmr-pkg/core/logs"

	sentryHttp "github.com/getsentry/sentry-go/http"

	"git.redmadrobot.com/zaman/backend/zaman/cmd/gateways/mobile/providers"
)

func main() {
	time.Local = time.UTC

	rootCtx := context.Background()
	cfg, envs := initConfigs(rootCtx)
	// Initialize Sentry
	sentryClient, err := sentry.NewSentry(rootCtx, cfg.Sentry, nil)
	if err != nil {
		log.Printf("unable to connect to sentry: %v", err)
		return
	}

	// Initialize OTEL
	traceProvider, err := otelx.NewOTELTraceProvider(rootCtx, true, cfg.Trace)
	if err != nil {
		log.Printf("Failed to create OTEL trace provider: %v", err)
		return
	}
	otel.SetTracerProvider(traceProvider)

	// Create initial span
	tracer := otel.Tracer("paymentsSme")
	rootCtx, span := tracer.Start(rootCtx, "main")

	defer span.End()

	// Create logger
	log := logs.Logger(
		rootCtx,
		cfg.Logger,
		logs.InstanceIDTag.Option(uuid.New()), logs.Options(),
	)
	defer log.Fatal().Msgf("application stopped")

	ctx, cancel := context.WithCancel(log.WithContext(rootCtx))
	defer cancel()

	if err := run(ctx, cancel, cfg, sentryClient, envs); err != nil {
		log.Info().Err(err).Msg("unable to start application")
	}
}

func initConfigs(ctx context.Context) (*mobile.Config, []string) {
	cfg, envs := mobile.LoadFromEnv()

	// Initialize Vault
	vClient, err := vault.NewVaultClientKV2(cfg.Vault, cfg.App.AppPrefix)
	if err == nil {
		logs.FromContext(ctx).Info().Msgf("Successfully created Vault client for %s", cfg.App.AppPrefix)
		envSyncer := vault.NewEnvSyncer(vClient)
		if syncErr := envSyncer.SyncEnvWithVault(ctx, envs); syncErr != nil {
			logs.FromContext(ctx).Fatal().Msgf("Error synchronizing environment variables with Vault: %v", syncErr)
		}
		cfg, _ = mobile.LoadFromEnv()
	}

	return cfg, envs
}

func run(ctx context.Context, cancel context.CancelFunc, cfg *mobile.Config, sentryHandler *sentryHttp.Handler, envs []string) error {
	var err error
	locator := providers.NewServiceLocator(ctx)
	// Initialize users gRPC client
	cfgusers, envsusers := cfgUsers.LoadFromEnv()
	vClientUsers, err := vault.NewVaultClientKV2(cfg.Vault, cfgusers.App.AppPrefix)
	if err == nil {
		log.Printf("Successfully created Vault client for %s", cfgusers.App.AppPrefix)
		envSyncer := vault.NewEnvSyncer(vClientUsers)
		if syncErr := envSyncer.SyncEnvWithVault(ctx, envsusers); syncErr != nil {
			log.Printf("Error synchronizing environment variables with Vault: %v", syncErr)
		}
		cfgusers, _ = cfgUsers.LoadFromEnv()
	}

	usersServer, err := grpcx.ConnectServer(cfgusers.GRPC)
	if err != nil {
		logs.FromContext(ctx).Fatal().Msgf("Failed to create gRPC client: %v", err)
	}
	defer usersServer.Close()
	users := pbUsers.NewUsersClient(usersServer)
	locator.Register("Users", users)

	// Initialize otp gRPC client
	cfgotp, envsotp := cfgOtp.LoadFromEnv()
	vClientOtp, err := vault.NewVaultClientKV2(cfg.Vault, cfgotp.App.AppPrefix)
	if err == nil {
		log.Printf("Successfully created Vault client for %s", cfgotp.App.AppPrefix)
		envSyncer := vault.NewEnvSyncer(vClientOtp)
		if syncErr := envSyncer.SyncEnvWithVault(ctx, envsotp); syncErr != nil {
			log.Printf("Error synchronizing environment variables with Vault: %v", syncErr)
		}
		cfgotp, _ = cfgOtp.LoadFromEnv()
	}

	otpServer, err := grpcx.ConnectServer(cfgotp.GRPC)
	if err != nil {
		logs.FromContext(ctx).Fatal().Msgf("Failed to create gRPC client: %v", err)
	}
	defer otpServer.Close()
	otp := pbOtp.NewOtpClient(otpServer)
	locator.Register("Otp", otp)

	// Initialize documents gRPC client
	cfgdocuments, envsdocuments := cfgDocuments.LoadFromEnv()
	vClientDocuments, err := vault.NewVaultClientKV2(cfg.Vault, cfgdocuments.App.AppPrefix)
	if err == nil {
		log.Printf("Successfully created Vault client for %s", cfgdocuments.App.AppPrefix)
		envSyncer := vault.NewEnvSyncer(vClientDocuments)
		if syncErr := envSyncer.SyncEnvWithVault(ctx, envsdocuments); syncErr != nil {
			log.Printf("Error synchronizing environment variables with Vault: %v", syncErr)
		}
		cfgdocuments, _ = cfgDocuments.LoadFromEnv()
	}

	documentsServer, err := grpcx.ConnectServer(cfgdocuments.GRPC)
	if err != nil {
		logs.FromContext(ctx).Fatal().Msgf("Failed to create gRPC client: %v", err)
	}
	defer documentsServer.Close()
	documents := pbDocuments.NewDocumentsClient(documentsServer)
	locator.Register("Documents", documents)

	// Initialize notifications gRPC client
	cfgnotifications, envsnotifications := cfgNotifications.LoadFromEnv()
	vClientNotifications, err := vault.NewVaultClientKV2(cfg.Vault, cfgnotifications.App.AppPrefix)
	if err == nil {
		log.Printf("Successfully created Vault client for %s", cfgnotifications.App.AppPrefix)
		envSyncer := vault.NewEnvSyncer(vClientNotifications)
		if syncErr := envSyncer.SyncEnvWithVault(ctx, envsnotifications); syncErr != nil {
			log.Printf("Error synchronizing environment variables with Vault: %v", syncErr)
		}
		cfgnotifications, _ = cfgNotifications.LoadFromEnv()
	}

	notificationsServer, err := grpcx.ConnectServer(cfgnotifications.GRPC)
	if err != nil {
		logs.FromContext(ctx).Fatal().Msgf("Failed to create gRPC client: %v", err)
	}
	defer notificationsServer.Close()
	notifications := pbNotifications.NewNotificationsClient(notificationsServer)
	locator.Register("Notifications", notifications)

	// Initialize keycloakProxy gRPC client
	cfgkeycloakProxy, envskeycloakProxy := cfgKeycloakProxy.LoadFromEnv()
	vClientKeycloakProxy, err := vault.NewVaultClientKV2(cfg.Vault, cfgkeycloakProxy.App.AppPrefix)
	if err == nil {
		log.Printf("Successfully created Vault client for %s", cfgkeycloakProxy.App.AppPrefix)
		envSyncer := vault.NewEnvSyncer(vClientKeycloakProxy)
		if syncErr := envSyncer.SyncEnvWithVault(ctx, envskeycloakProxy); syncErr != nil {
			log.Printf("Error synchronizing environment variables with Vault: %v", syncErr)
		}
		cfgkeycloakProxy, _ = cfgKeycloakProxy.LoadFromEnv()
	}

	keycloakProxyServer, err := grpcx.ConnectServer(cfgkeycloakProxy.GRPC)
	if err != nil {
		logs.FromContext(ctx).Fatal().Msgf("Failed to create gRPC client: %v", err)
	}
	defer keycloakProxyServer.Close()
	keycloakproxy := pbKeycloakProxy.NewKeycloakproxyClient(keycloakProxyServer)
	locator.Register("Keycloakproxy", keycloakproxy)

	// Initialize kgdBridge gRPC client
	cfgkgdBridge, envskgdBridge := cfgKgdBridge.LoadFromEnv()
	vClientKgdBridge, err := vault.NewVaultClientKV2(cfg.Vault, cfgkgdBridge.App.AppPrefix)
	if err == nil {
		log.Printf("Successfully created Vault client for %s", cfgkgdBridge.App.AppPrefix)
		envSyncer := vault.NewEnvSyncer(vClientKgdBridge)
		if syncErr := envSyncer.SyncEnvWithVault(ctx, envskgdBridge); syncErr != nil {
			log.Printf("Error synchronizing environment variables with Vault: %v", syncErr)
		}
		cfgkgdBridge, _ = cfgKgdBridge.LoadFromEnv()
	}

	kgdBridgeServer, err := grpcx.ConnectServer(cfgkgdBridge.GRPC)
	if err != nil {
		logs.FromContext(ctx).Fatal().Msgf("Failed to create gRPC client: %v", err)
	}
	defer kgdBridgeServer.Close()
	kgdbridge := pbKgdBridge.NewKgdbridgeClient(kgdBridgeServer)
	locator.Register("Kgdbridge", kgdbridge)

	// Initialize btsBridge gRPC client
	cfgbtsBridge, envsbtsBridge := cfgBtsBridge.LoadFromEnv()
	vClientBtsBridge, err := vault.NewVaultClientKV2(cfg.Vault, cfgbtsBridge.App.AppPrefix)
	if err == nil {
		log.Printf("Successfully created Vault client for %s", cfgbtsBridge.App.AppPrefix)
		envSyncer := vault.NewEnvSyncer(vClientBtsBridge)
		if syncErr := envSyncer.SyncEnvWithVault(ctx, envsbtsBridge); syncErr != nil {
			log.Printf("Error synchronizing environment variables with Vault: %v", syncErr)
		}
		cfgbtsBridge, _ = cfgBtsBridge.LoadFromEnv()
	}

	btsBridgeServer, err := grpcx.ConnectServer(cfgbtsBridge.GRPC)
	if err != nil {
		logs.FromContext(ctx).Fatal().Msgf("Failed to create gRPC client: %v", err)
	}
	defer btsBridgeServer.Close()
	btsbridge := pbBtsBridge.NewBtsbridgeClient(btsBridgeServer)
	locator.Register("Btsbridge", btsbridge)

	// Initialize smsBridge gRPC client
	cfgsmsBridge, envssmsBridge := cfgSmsBridge.LoadFromEnv()
	vClientSmsBridge, err := vault.NewVaultClientKV2(cfg.Vault, cfgsmsBridge.App.AppPrefix)
	if err == nil {
		log.Printf("Successfully created Vault client for %s", cfgsmsBridge.App.AppPrefix)
		envSyncer := vault.NewEnvSyncer(vClientSmsBridge)
		if syncErr := envSyncer.SyncEnvWithVault(ctx, envssmsBridge); syncErr != nil {
			log.Printf("Error synchronizing environment variables with Vault: %v", syncErr)
		}
		cfgsmsBridge, _ = cfgSmsBridge.LoadFromEnv()
	}

	smsBridgeServer, err := grpcx.ConnectServer(cfgsmsBridge.GRPC)
	if err != nil {
		logs.FromContext(ctx).Fatal().Msgf("Failed to create gRPC client: %v", err)
	}
	defer smsBridgeServer.Close()
	smsbridge := pbSmsBridge.NewSmsbridgeClient(smsBridgeServer)
	locator.Register("Smsbridge", smsbridge)

	// Initialize loans gRPC client
	cfgloans, envsloans := cfgLoans.LoadFromEnv()
	vClientLoans, err := vault.NewVaultClientKV2(cfg.Vault, cfgloans.App.AppPrefix)
	if err == nil {
		log.Printf("Successfully created Vault client for %s", cfgloans.App.AppPrefix)
		envSyncer := vault.NewEnvSyncer(vClientLoans)
		if syncErr := envSyncer.SyncEnvWithVault(ctx, envsloans); syncErr != nil {
			log.Printf("Error synchronizing environment variables with Vault: %v", syncErr)
		}
		cfgloans, _ = cfgLoans.LoadFromEnv()
	}

	loansServer, err := grpcx.ConnectServer(cfgloans.GRPC)
	if err != nil {
		logs.FromContext(ctx).Fatal().Msgf("Failed to create gRPC client: %v", err)
	}
	defer loansServer.Close()
	loans := pbLoans.NewLoansClient(loansServer)
	locator.Register("Loans", loans)

	// Initialize colvirBridge gRPC client
	cfgcolvirBridge, envscolvirBridge := cfgColvirBridge.LoadFromEnv()
	vClientColvirBridge, err := vault.NewVaultClientKV2(cfg.Vault, cfgcolvirBridge.App.AppPrefix)
	if err == nil {
		log.Printf("Successfully created Vault client for %s", cfgcolvirBridge.App.AppPrefix)
		envSyncer := vault.NewEnvSyncer(vClientColvirBridge)
		if syncErr := envSyncer.SyncEnvWithVault(ctx, envscolvirBridge); syncErr != nil {
			log.Printf("Error synchronizing environment variables with Vault: %v", syncErr)
		}
		cfgcolvirBridge, _ = cfgColvirBridge.LoadFromEnv()
	}

	colvirBridgeServer, err := grpcx.ConnectServer(cfgcolvirBridge.GRPC)
	if err != nil {
		logs.FromContext(ctx).Fatal().Msgf("Failed to create gRPC client: %v", err)
	}
	defer colvirBridgeServer.Close()
	colvirbridge := pbColvirBridge.NewColvirbridgeClient(colvirBridgeServer)
	locator.Register("Colvirbridge", colvirbridge)

	// Initialize payments gRPC client
	cfgpayments, envspayments := cfgPayments.LoadFromEnv()
	vClientPayments, err := vault.NewVaultClientKV2(cfg.Vault, cfgpayments.App.AppPrefix)
	if err == nil {
		log.Printf("Successfully created Vault client for %s", cfgpayments.App.AppPrefix)
		envSyncer := vault.NewEnvSyncer(vClientPayments)
		if syncErr := envSyncer.SyncEnvWithVault(ctx, envspayments); syncErr != nil {
			log.Printf("Error synchronizing environment variables with Vault: %v", syncErr)
		}
		cfgpayments, _ = cfgPayments.LoadFromEnv()
	}

	paymentsServer, err := grpcx.ConnectServer(cfgpayments.GRPC)
	if err != nil {
		logs.FromContext(ctx).Fatal().Msgf("Failed to create gRPC client: %v", err)
	}
	defer paymentsServer.Close()
	payments := pbPayments.NewPaymentsClient(paymentsServer)
	locator.Register("Payments", payments)

	// Initialize cardsAccounts gRPC client
	cfgcardsAccounts, envscardsAccounts := cfgCardsAccounts.LoadFromEnv()
	vClientCardsAccounts, err := vault.NewVaultClientKV2(cfg.Vault, cfgcardsAccounts.App.AppPrefix)
	if err == nil {
		log.Printf("Successfully created Vault client for %s", cfgcardsAccounts.App.AppPrefix)
		envSyncer := vault.NewEnvSyncer(vClientCardsAccounts)
		if syncErr := envSyncer.SyncEnvWithVault(ctx, envscardsAccounts); syncErr != nil {
			log.Printf("Error synchronizing environment variables with Vault: %v", syncErr)
		}
		cfgcardsAccounts, _ = cfgCardsAccounts.LoadFromEnv()
	}

	cardsAccountsServer, err := grpcx.ConnectServer(cfgcardsAccounts.GRPC)
	if err != nil {
		logs.FromContext(ctx).Fatal().Msgf("Failed to create gRPC client: %v", err)
	}
	defer cardsAccountsServer.Close()
	cardsaccounts := pbCardsAccounts.NewCardsaccountsClient(cardsAccountsServer)
	locator.Register("Cardsaccounts", cardsaccounts)

	// Initialize dictionary gRPC client
	cfgdictionary, envsdictionary := cfgDictionary.LoadFromEnv()
	vClientDictionary, err := vault.NewVaultClientKV2(cfg.Vault, cfgdictionary.App.AppPrefix)
	if err == nil {
		log.Printf("Successfully created Vault client for %s", cfgdictionary.App.AppPrefix)
		envSyncer := vault.NewEnvSyncer(vClientDictionary)
		if syncErr := envSyncer.SyncEnvWithVault(ctx, envsdictionary); syncErr != nil {
			log.Printf("Error synchronizing environment variables with Vault: %v", syncErr)
		}
		cfgdictionary, _ = cfgDictionary.LoadFromEnv()
	}

	dictionaryServer, err := grpcx.ConnectServer(cfgdictionary.GRPC)
	if err != nil {
		logs.FromContext(ctx).Fatal().Msgf("Failed to create gRPC client: %v", err)
	}
	defer dictionaryServer.Close()
	dictionary := pbDictionary.NewDictionaryClient(dictionaryServer)
	locator.Register("Dictionary", dictionary)

	// Initialize pkbBridge gRPC client
	cfgpkbBridge, envspkbBridge := cfgPkbBridge.LoadFromEnv()
	vClientPkbBridge, err := vault.NewVaultClientKV2(cfg.Vault, cfgpkbBridge.App.AppPrefix)
	if err == nil {
		log.Printf("Successfully created Vault client for %s", cfgpkbBridge.App.AppPrefix)
		envSyncer := vault.NewEnvSyncer(vClientPkbBridge)
		if syncErr := envSyncer.SyncEnvWithVault(ctx, envspkbBridge); syncErr != nil {
			log.Printf("Error synchronizing environment variables with Vault: %v", syncErr)
		}
		cfgpkbBridge, _ = cfgPkbBridge.LoadFromEnv()
	}

	pkbBridgeServer, err := grpcx.ConnectServer(cfgpkbBridge.GRPC)
	if err != nil {
		logs.FromContext(ctx).Fatal().Msgf("Failed to create gRPC client: %v", err)
	}
	defer pkbBridgeServer.Close()
	pkbbridge := pbPkbBridge.NewPkbbridgeClient(pkbBridgeServer)
	locator.Register("Pkbbridge", pkbbridge)

	// Initialize amlBridge gRPC client
	cfgamlBridge, envsamlBridge := cfgAmlBridge.LoadFromEnv()
	vClientAmlBridge, err := vault.NewVaultClientKV2(cfg.Vault, cfgamlBridge.App.AppPrefix)
	if err == nil {
		log.Printf("Successfully created Vault client for %s", cfgamlBridge.App.AppPrefix)
		envSyncer := vault.NewEnvSyncer(vClientAmlBridge)
		if syncErr := envSyncer.SyncEnvWithVault(ctx, envsamlBridge); syncErr != nil {
			log.Printf("Error synchronizing environment variables with Vault: %v", syncErr)
		}
		cfgamlBridge, _ = cfgAmlBridge.LoadFromEnv()
	}

	amlBridgeServer, err := grpcx.ConnectServer(cfgamlBridge.GRPC)
	if err != nil {
		logs.FromContext(ctx).Fatal().Msgf("Failed to create gRPC client: %v", err)
	}
	defer amlBridgeServer.Close()
	amlbridge := pbAmlBridge.NewAmlbridgeClient(amlBridgeServer)
	locator.Register("Amlbridge", amlbridge)

	// Initialize liveness gRPC client
	cfgliveness, envsliveness := cfgLiveness.LoadFromEnv()
	vClientLiveness, err := vault.NewVaultClientKV2(cfg.Vault, cfgliveness.App.AppPrefix)
	if err == nil {
		log.Printf("Successfully created Vault client for %s", cfgliveness.App.AppPrefix)
		envSyncer := vault.NewEnvSyncer(vClientLiveness)
		if syncErr := envSyncer.SyncEnvWithVault(ctx, envsliveness); syncErr != nil {
			log.Printf("Error synchronizing environment variables with Vault: %v", syncErr)
		}
		cfgliveness, _ = cfgLiveness.LoadFromEnv()
	}

	livenessServer, err := grpcx.ConnectServer(cfgliveness.GRPC)
	if err != nil {
		logs.FromContext(ctx).Fatal().Msgf("Failed to create gRPC client: %v", err)
	}
	defer livenessServer.Close()
	liveness := pbLiveness.NewLivenessClient(livenessServer)
	locator.Register("Liveness", liveness)

	// Initialize taskManager gRPC client
	cfgtaskManager, envstaskManager := cfgTaskManager.LoadFromEnv()
	vClientTaskManager, err := vault.NewVaultClientKV2(cfg.Vault, cfgtaskManager.App.AppPrefix)
	if err == nil {
		log.Printf("Successfully created Vault client for %s", cfgtaskManager.App.AppPrefix)
		envSyncer := vault.NewEnvSyncer(vClientTaskManager)
		if syncErr := envSyncer.SyncEnvWithVault(ctx, envstaskManager); syncErr != nil {
			log.Printf("Error synchronizing environment variables with Vault: %v", syncErr)
		}
		cfgtaskManager, _ = cfgTaskManager.LoadFromEnv()
	}

	taskManagerServer, err := grpcx.ConnectServer(cfgtaskManager.GRPC)
	if err != nil {
		logs.FromContext(ctx).Fatal().Msgf("Failed to create gRPC client: %v", err)
	}
	defer taskManagerServer.Close()
	taskmanager := pbTaskManager.NewTaskmanagerClient(taskManagerServer)
	locator.Register("Taskmanager", taskmanager)

	// Initialize juicyscoreBridge gRPC client
	cfgjuicyscoreBridge, envsjuicyscoreBridge := cfgJuicyscoreBridge.LoadFromEnv()
	vClientJuicyscoreBridge, err := vault.NewVaultClientKV2(cfg.Vault, cfgjuicyscoreBridge.App.AppPrefix)
	if err == nil {
		log.Printf("Successfully created Vault client for %s", cfgjuicyscoreBridge.App.AppPrefix)
		envSyncer := vault.NewEnvSyncer(vClientJuicyscoreBridge)
		if syncErr := envSyncer.SyncEnvWithVault(ctx, envsjuicyscoreBridge); syncErr != nil {
			log.Printf("Error synchronizing environment variables with Vault: %v", syncErr)
		}
		cfgjuicyscoreBridge, _ = cfgJuicyscoreBridge.LoadFromEnv()
	}

	juicyscoreBridgeServer, err := grpcx.ConnectServer(cfgjuicyscoreBridge.GRPC)
	if err != nil {
		logs.FromContext(ctx).Fatal().Msgf("Failed to create gRPC client: %v", err)
	}
	defer juicyscoreBridgeServer.Close()
	juicyscorebridge := pbJuicyscoreBridge.NewJuicyscorebridgeClient(juicyscoreBridgeServer)
	locator.Register("Juicyscorebridge", juicyscorebridge)

	// Initialize jiraBridge gRPC client
	cfgjiraBridge, envsjiraBridge := cfgJiraBridge.LoadFromEnv()
	vClientJiraBridge, err := vault.NewVaultClientKV2(cfg.Vault, cfgjiraBridge.App.AppPrefix)
	if err == nil {
		log.Printf("Successfully created Vault client for %s", cfgjiraBridge.App.AppPrefix)
		envSyncer := vault.NewEnvSyncer(vClientJiraBridge)
		if syncErr := envSyncer.SyncEnvWithVault(ctx, envsjiraBridge); syncErr != nil {
			log.Printf("Error synchronizing environment variables with Vault: %v", syncErr)
		}
		cfgjiraBridge, _ = cfgJiraBridge.LoadFromEnv()
	}

	jiraBridgeServer, err := grpcx.ConnectServer(cfgjiraBridge.GRPC)
	if err != nil {
		logs.FromContext(ctx).Fatal().Msgf("Failed to create gRPC client: %v", err)
	}
	defer jiraBridgeServer.Close()
	jirabridge := pbJiraBridge.NewJirabridgeClient(jiraBridgeServer)
	locator.Register("Jirabridge", jirabridge)

	// Initialize fileGuard gRPC client
	cfgfileGuard, envsfileGuard := cfgFileGuard.LoadFromEnv()
	vClientFileGuard, err := vault.NewVaultClientKV2(cfg.Vault, cfgfileGuard.App.AppPrefix)
	if err == nil {
		log.Printf("Successfully created Vault client for %s", cfgfileGuard.App.AppPrefix)
		envSyncer := vault.NewEnvSyncer(vClientFileGuard)
		if syncErr := envSyncer.SyncEnvWithVault(ctx, envsfileGuard); syncErr != nil {
			log.Printf("Error synchronizing environment variables with Vault: %v", syncErr)
		}
		cfgfileGuard, _ = cfgFileGuard.LoadFromEnv()
	}

	fileGuardServer, err := grpcx.ConnectServer(cfgfileGuard.GRPC)
	if err != nil {
		logs.FromContext(ctx).Fatal().Msgf("Failed to create gRPC client: %v", err)
	}
	defer fileGuardServer.Close()
	fileguard := pbFileGuard.NewFileguardClient(fileGuardServer)
	locator.Register("Fileguard", fileguard)

	// Initialize scoring gRPC client
	cfgscoring, envsscoring := cfgScoring.LoadFromEnv()
	vClientScoring, err := vault.NewVaultClientKV2(cfg.Vault, cfgscoring.App.AppPrefix)
	if err == nil {
		log.Printf("Successfully created Vault client for %s", cfgscoring.App.AppPrefix)
		envSyncer := vault.NewEnvSyncer(vClientScoring)
		if syncErr := envSyncer.SyncEnvWithVault(ctx, envsscoring); syncErr != nil {
			log.Printf("Error synchronizing environment variables with Vault: %v", syncErr)
		}
		cfgscoring, _ = cfgScoring.LoadFromEnv()
	}

	scoringServer, err := grpcx.ConnectServer(cfgscoring.GRPC)
	if err != nil {
		logs.FromContext(ctx).Fatal().Msgf("Failed to create gRPC client: %v", err)
	}
	defer scoringServer.Close()
	scoring := pbScoring.NewScoringClient(scoringServer)
	locator.Register("Scoring", scoring)

	// Initialize seonBridge gRPC client
	cfgseonBridge, envsseonBridge := cfgSeonBridge.LoadFromEnv()
	vClientSeonBridge, err := vault.NewVaultClientKV2(cfg.Vault, cfgseonBridge.App.AppPrefix)
	if err == nil {
		log.Printf("Successfully created Vault client for %s", cfgseonBridge.App.AppPrefix)
		envSyncer := vault.NewEnvSyncer(vClientSeonBridge)
		if syncErr := envSyncer.SyncEnvWithVault(ctx, envsseonBridge); syncErr != nil {
			log.Printf("Error synchronizing environment variables with Vault: %v", syncErr)
		}
		cfgseonBridge, _ = cfgSeonBridge.LoadFromEnv()
	}

	seonBridgeServer, err := grpcx.ConnectServer(cfgseonBridge.GRPC)
	if err != nil {
		logs.FromContext(ctx).Fatal().Msgf("Failed to create gRPC client: %v", err)
	}
	defer seonBridgeServer.Close()
	seonbridge := pbSeonBridge.NewSeonbridgeClient(seonBridgeServer)
	locator.Register("Seonbridge", seonbridge)

	// Initialize sprBridge gRPC client
	cfgsprBridge, envssprBridge := cfgSprBridge.LoadFromEnv()
	vClientSprBridge, err := vault.NewVaultClientKV2(cfg.Vault, cfgsprBridge.App.AppPrefix)
	if err == nil {
		log.Printf("Successfully created Vault client for %s", cfgsprBridge.App.AppPrefix)
		envSyncer := vault.NewEnvSyncer(vClientSprBridge)
		if syncErr := envSyncer.SyncEnvWithVault(ctx, envssprBridge); syncErr != nil {
			log.Printf("Error synchronizing environment variables with Vault: %v", syncErr)
		}
		cfgsprBridge, _ = cfgSprBridge.LoadFromEnv()
	}

	sprBridgeServer, err := grpcx.ConnectServer(cfgsprBridge.GRPC)
	if err != nil {
		logs.FromContext(ctx).Fatal().Msgf("Failed to create gRPC client: %v", err)
	}
	defer sprBridgeServer.Close()
	sprbridge := pbSprBridge.NewSprbridgeClient(sprBridgeServer)
	locator.Register("Sprbridge", sprbridge)

	// Initialize altScoreBridge gRPC client
	cfgaltScoreBridge, envsaltScoreBridge := cfgAltScoreBridge.LoadFromEnv()
	vClientAltScoreBridge, err := vault.NewVaultClientKV2(cfg.Vault, cfgaltScoreBridge.App.AppPrefix)
	if err == nil {
		log.Printf("Successfully created Vault client for %s", cfgaltScoreBridge.App.AppPrefix)
		envSyncer := vault.NewEnvSyncer(vClientAltScoreBridge)
		if syncErr := envSyncer.SyncEnvWithVault(ctx, envsaltScoreBridge); syncErr != nil {
			log.Printf("Error synchronizing environment variables with Vault: %v", syncErr)
		}
		cfgaltScoreBridge, _ = cfgAltScoreBridge.LoadFromEnv()
	}

	altScoreBridgeServer, err := grpcx.ConnectServer(cfgaltScoreBridge.GRPC)
	if err != nil {
		logs.FromContext(ctx).Fatal().Msgf("Failed to create gRPC client: %v", err)
	}
	defer altScoreBridgeServer.Close()
	altscorebridge := pbAltScoreBridge.NewAltscorebridgeClient(altScoreBridgeServer)
	locator.Register("Altscorebridge", altscorebridge)

	// Initialize qazpostBridge gRPC client
	cfgqazpostBridge, envsqazpostBridge := cfgQazpostBridge.LoadFromEnv()
	vClientQazpostBridge, err := vault.NewVaultClientKV2(cfg.Vault, cfgqazpostBridge.App.AppPrefix)
	if err == nil {
		log.Printf("Successfully created Vault client for %s", cfgqazpostBridge.App.AppPrefix)
		envSyncer := vault.NewEnvSyncer(vClientQazpostBridge)
		if syncErr := envSyncer.SyncEnvWithVault(ctx, envsqazpostBridge); syncErr != nil {
			log.Printf("Error synchronizing environment variables with Vault: %v", syncErr)
		}
		cfgqazpostBridge, _ = cfgQazpostBridge.LoadFromEnv()
	}

	qazpostBridgeServer, err := grpcx.ConnectServer(cfgqazpostBridge.GRPC)
	if err != nil {
		logs.FromContext(ctx).Fatal().Msgf("Failed to create gRPC client: %v", err)
	}
	defer qazpostBridgeServer.Close()
	qazpostbridge := pbQazpostBridge.NewQazpostbridgeClient(qazpostBridgeServer)
	locator.Register("Qazpostbridge", qazpostbridge)

	// Initialize deposits gRPC client
	cfgdeposits, envsdeposits := cfgDeposits.LoadFromEnv()
	vClientDeposits, err := vault.NewVaultClientKV2(cfg.Vault, cfgdeposits.App.AppPrefix)
	if err == nil {
		log.Printf("Successfully created Vault client for %s", cfgdeposits.App.AppPrefix)
		envSyncer := vault.NewEnvSyncer(vClientDeposits)
		if syncErr := envSyncer.SyncEnvWithVault(ctx, envsdeposits); syncErr != nil {
			log.Printf("Error synchronizing environment variables with Vault: %v", syncErr)
		}
		cfgdeposits, _ = cfgDeposits.LoadFromEnv()
	}

	depositsServer, err := grpcx.ConnectServer(cfgdeposits.GRPC)
	if err != nil {
		logs.FromContext(ctx).Fatal().Msgf("Failed to create gRPC client: %v", err)
	}
	defer depositsServer.Close()
	deposits := pbDeposits.NewDepositsClient(depositsServer)
	locator.Register("Deposits", deposits)

	// Initialize bsasBridge gRPC client
	cfgbsasBridge, envsbsasBridge := cfgBsasBridge.LoadFromEnv()
	vClientBsasBridge, err := vault.NewVaultClientKV2(cfg.Vault, cfgbsasBridge.App.AppPrefix)
	if err == nil {
		log.Printf("Successfully created Vault client for %s", cfgbsasBridge.App.AppPrefix)
		envSyncer := vault.NewEnvSyncer(vClientBsasBridge)
		if syncErr := envSyncer.SyncEnvWithVault(ctx, envsbsasBridge); syncErr != nil {
			log.Printf("Error synchronizing environment variables with Vault: %v", syncErr)
		}
		cfgbsasBridge, _ = cfgBsasBridge.LoadFromEnv()
	}

	bsasBridgeServer, err := grpcx.ConnectServer(cfgbsasBridge.GRPC)
	if err != nil {
		logs.FromContext(ctx).Fatal().Msgf("Failed to create gRPC client: %v", err)
	}
	defer bsasBridgeServer.Close()
	bsasbridge := pbBsasBridge.NewBsasbridgeClient(bsasBridgeServer)
	locator.Register("Bsasbridge", bsasbridge)

	// Initialize apBridge gRPC client
	cfgapBridge, envsapBridge := cfgApBridge.LoadFromEnv()
	vClientApBridge, err := vault.NewVaultClientKV2(cfg.Vault, cfgapBridge.App.AppPrefix)
	if err == nil {
		log.Printf("Successfully created Vault client for %s", cfgapBridge.App.AppPrefix)
		envSyncer := vault.NewEnvSyncer(vClientApBridge)
		if syncErr := envSyncer.SyncEnvWithVault(ctx, envsapBridge); syncErr != nil {
			log.Printf("Error synchronizing environment variables with Vault: %v", syncErr)
		}
		cfgapBridge, _ = cfgApBridge.LoadFromEnv()
	}

	apBridgeServer, err := grpcx.ConnectServer(cfgapBridge.GRPC)
	if err != nil {
		logs.FromContext(ctx).Fatal().Msgf("Failed to create gRPC client: %v", err)
	}
	defer apBridgeServer.Close()
	apbridge := pbApBridge.NewApbridgeClient(apBridgeServer)
	locator.Register("Apbridge", apbridge)

	// Initialize processingBridge gRPC client
	cfgprocessingBridge, envsprocessingBridge := cfgProcessingBridge.LoadFromEnv()
	vClientProcessingBridge, err := vault.NewVaultClientKV2(cfg.Vault, cfgprocessingBridge.App.AppPrefix)
	if err == nil {
		log.Printf("Successfully created Vault client for %s", cfgprocessingBridge.App.AppPrefix)
		envSyncer := vault.NewEnvSyncer(vClientProcessingBridge)
		if syncErr := envSyncer.SyncEnvWithVault(ctx, envsprocessingBridge); syncErr != nil {
			log.Printf("Error synchronizing environment variables with Vault: %v", syncErr)
		}
		cfgprocessingBridge, _ = cfgProcessingBridge.LoadFromEnv()
	}

	processingBridgeServer, err := grpcx.ConnectServer(cfgprocessingBridge.GRPC)
	if err != nil {
		logs.FromContext(ctx).Fatal().Msgf("Failed to create gRPC client: %v", err)
	}
	defer processingBridgeServer.Close()
	processingbridge := pbProcessingBridge.NewProcessingbridgeClient(processingBridgeServer)
	locator.Register("Processingbridge", processingbridge)

	// Initialize collection gRPC client
	cfgcollection, envscollection := cfgCollection.LoadFromEnv()
	vClientCollection, err := vault.NewVaultClientKV2(cfg.Vault, cfgcollection.App.AppPrefix)
	if err == nil {
		log.Printf("Successfully created Vault client for %s", cfgcollection.App.AppPrefix)
		envSyncer := vault.NewEnvSyncer(vClientCollection)
		if syncErr := envSyncer.SyncEnvWithVault(ctx, envscollection); syncErr != nil {
			log.Printf("Error synchronizing environment variables with Vault: %v", syncErr)
		}
		cfgcollection, _ = cfgCollection.LoadFromEnv()
	}

	collectionServer, err := grpcx.ConnectServer(cfgcollection.GRPC)
	if err != nil {
		logs.FromContext(ctx).Fatal().Msgf("Failed to create gRPC client: %v", err)
	}
	defer collectionServer.Close()
	collection := pbCollection.NewCollectionClient(collectionServer)
	locator.Register("Collection", collection)

	// Initialize paymentsSme gRPC client
	cfgpaymentsSme, envspaymentsSme := cfgPaymentsSme.LoadFromEnv()
	vClientPaymentsSme, err := vault.NewVaultClientKV2(cfg.Vault, cfgpaymentsSme.App.AppPrefix)
	if err == nil {
		log.Printf("Successfully created Vault client for %s", cfgpaymentsSme.App.AppPrefix)
		envSyncer := vault.NewEnvSyncer(vClientPaymentsSme)
		if syncErr := envSyncer.SyncEnvWithVault(ctx, envspaymentsSme); syncErr != nil {
			log.Printf("Error synchronizing environment variables with Vault: %v", syncErr)
		}
		cfgpaymentsSme, _ = cfgPaymentsSme.LoadFromEnv()
	}

	paymentsSmeServer, err := grpcx.ConnectServer(cfgpaymentsSme.GRPC)
	if err != nil {
		logs.FromContext(ctx).Fatal().Msgf("Failed to create gRPC client: %v", err)
	}
	defer paymentsSmeServer.Close()
	paymentssme := pbPaymentsSme.NewPaymentssmeClient(paymentsSmeServer)
	locator.Register("Paymentssme", paymentssme)

	// Adding custom providers to the locator using the NewCustomProviders method.
	// This method can be used to add providers that were not automatically generated
	// or require special configuration before being registered.
	providers.NewCustomProviders(ctx, *cfg, locator)

	// Initialize Server
	srv := gw.New(*cfg, *locator)
	api, err := gw.NewOpenAPI(*cfg)
	if err != nil {
		return fmt.Errorf("unable to load apps's external-api spec: %w", err)
	}

	options, mainGroup, err := srv.NewServerOptions(sentryHandler, api)
	if err != nil {
		return fmt.Errorf("unable to configure server: %w", err)
	}

	mainGroupOptions := options
	mainGroupOptions.BaseRouter = mainGroup
	openapi.HandlerWithOptions(srv, mainGroupOptions)

	err = httpx.StartServer(ctx, httpx.NewServer(cfg.HTTP, options.BaseRouter))
	if err != nil {
		return fmt.Errorf("unable to start server: %w", err)
	}

	return nil
}
