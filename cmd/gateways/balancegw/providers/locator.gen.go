// Code generated by git.redmadrobot.com/backend-go/meroving. DO NOT EDIT.
package providers

import (
	"context"
	"fmt"
	"reflect"
	"sync"

	"git.redmadrobot.com/backend-go/rmr-pkg/core/logs"

	pbAltScoreBridge "git.redmadrobot.com/zaman/backend/zaman/specs/proto/alt-score-bridge"
	pbAmlBridge "git.redmadrobot.com/zaman/backend/zaman/specs/proto/aml-bridge"
	pbApBridge "git.redmadrobot.com/zaman/backend/zaman/specs/proto/ap-bridge"
	pbBsasBridge "git.redmadrobot.com/zaman/backend/zaman/specs/proto/bsas-bridge"
	pbBtsBridge "git.redmadrobot.com/zaman/backend/zaman/specs/proto/bts-bridge"
	pbCardsAccounts "git.redmadrobot.com/zaman/backend/zaman/specs/proto/cards-accounts"
	pbCollection "git.redmadrobot.com/zaman/backend/zaman/specs/proto/collection"
	pbColvirBridge "git.redmadrobot.com/zaman/backend/zaman/specs/proto/colvir-bridge"
	pbDeposits "git.redmadrobot.com/zaman/backend/zaman/specs/proto/deposits"
	pbDictionary "git.redmadrobot.com/zaman/backend/zaman/specs/proto/dictionary"
	pbDocuments "git.redmadrobot.com/zaman/backend/zaman/specs/proto/documents"
	pbFileGuard "git.redmadrobot.com/zaman/backend/zaman/specs/proto/file-guard"
	pbJiraBridge "git.redmadrobot.com/zaman/backend/zaman/specs/proto/jira-bridge"
	pbJuicyscoreBridge "git.redmadrobot.com/zaman/backend/zaman/specs/proto/juicyscore-bridge"
	pbKeycloakProxy "git.redmadrobot.com/zaman/backend/zaman/specs/proto/keycloak-proxy"
	pbKgdBridge "git.redmadrobot.com/zaman/backend/zaman/specs/proto/kgd-bridge"
	pbLiveness "git.redmadrobot.com/zaman/backend/zaman/specs/proto/liveness"
	pbLoans "git.redmadrobot.com/zaman/backend/zaman/specs/proto/loans"
	pbNotifications "git.redmadrobot.com/zaman/backend/zaman/specs/proto/notifications"
	pbOtp "git.redmadrobot.com/zaman/backend/zaman/specs/proto/otp"
	pbPayments "git.redmadrobot.com/zaman/backend/zaman/specs/proto/payments"
	pbPaymentsSme "git.redmadrobot.com/zaman/backend/zaman/specs/proto/payments-sme"
	pbPkbBridge "git.redmadrobot.com/zaman/backend/zaman/specs/proto/pkb-bridge"
	pbProcessingBridge "git.redmadrobot.com/zaman/backend/zaman/specs/proto/processing-bridge"
	pbQazpostBridge "git.redmadrobot.com/zaman/backend/zaman/specs/proto/qazpost-bridge"
	pbScoring "git.redmadrobot.com/zaman/backend/zaman/specs/proto/scoring"
	pbSeonBridge "git.redmadrobot.com/zaman/backend/zaman/specs/proto/seon-bridge"
	pbSmsBridge "git.redmadrobot.com/zaman/backend/zaman/specs/proto/sms-bridge"
	pbSprBridge "git.redmadrobot.com/zaman/backend/zaman/specs/proto/spr-bridge"
	pbTaskManager "git.redmadrobot.com/zaman/backend/zaman/specs/proto/task-manager"
	pbUsers "git.redmadrobot.com/zaman/backend/zaman/specs/proto/users"
)

// ServiceLocator интерфейс для регистрации и получения провайдеров
type ServiceLocator interface {
	Register(providerName string, provider interface{}) error
}

// ServiceLocatorImpl реализует интерфейс ServiceLocator
type ServiceLocatorImpl struct {
	direct           // Пользовательские провайдеры
	Users            pbUsers.UsersClient
	Otp              pbOtp.OtpClient
	Documents        pbDocuments.DocumentsClient
	Notifications    pbNotifications.NotificationsClient
	Keycloakproxy    pbKeycloakProxy.KeycloakproxyClient
	Kgdbridge        pbKgdBridge.KgdbridgeClient
	Btsbridge        pbBtsBridge.BtsbridgeClient
	Smsbridge        pbSmsBridge.SmsbridgeClient
	Loans            pbLoans.LoansClient
	Colvirbridge     pbColvirBridge.ColvirbridgeClient
	Payments         pbPayments.PaymentsClient
	Cardsaccounts    pbCardsAccounts.CardsaccountsClient
	Dictionary       pbDictionary.DictionaryClient
	Pkbbridge        pbPkbBridge.PkbbridgeClient
	Amlbridge        pbAmlBridge.AmlbridgeClient
	Liveness         pbLiveness.LivenessClient
	Taskmanager      pbTaskManager.TaskmanagerClient
	Juicyscorebridge pbJuicyscoreBridge.JuicyscorebridgeClient
	Jirabridge       pbJiraBridge.JirabridgeClient
	Fileguard        pbFileGuard.FileguardClient
	Scoring          pbScoring.ScoringClient
	Seonbridge       pbSeonBridge.SeonbridgeClient
	Sprbridge        pbSprBridge.SprbridgeClient
	Altscorebridge   pbAltScoreBridge.AltscorebridgeClient
	Qazpostbridge    pbQazpostBridge.QazpostbridgeClient
	Deposits         pbDeposits.DepositsClient
	Bsasbridge       pbBsasBridge.BsasbridgeClient
	Apbridge         pbApBridge.ApbridgeClient
	Processingbridge pbProcessingBridge.ProcessingbridgeClient
	Collection       pbCollection.CollectionClient
	Paymentssme      pbPaymentsSme.PaymentssmeClient

	mu  sync.RWMutex // Мьютекс для синхронизации доступа к структуре
	ctx context.Context
}

// NewServiceLocator создает новый экземпляр ServiceLocator
func NewServiceLocator(ctx context.Context) *ServiceLocatorImpl {
	return &ServiceLocatorImpl{
		ctx: ctx,
	}
}

// Register регистрирует новый провайдер по его имени и типу и сохраняет его в соответствующем поле
func (s *ServiceLocatorImpl) Register(providerName string, provider interface{}) error {
	s.mu.Lock()         // Блокируем мьютекс для предотвращения гонок данных
	defer s.mu.Unlock() // Разблокируем мьютекс по завершении функции

	logger := logs.FromContext(s.ctx)

	// Получаем значение структуры (значения всех её полей)
	structValue := reflect.ValueOf(s).Elem()
	// Получаем тип структуры (типы всех её полей)
	structType := structValue.Type()

	// Перебираем все поля структуры
	for i := 0; i < structValue.NumField(); i++ {
		field := structValue.Field(i)    // Получаем значение текущего поля
		fieldType := structType.Field(i) // Получаем тип текущего поля

		// Сравниваем имя и тип поля с именем и типом переданного провайдера
		if fieldType.Name == providerName {
			if field.CanSet() { // Проверяем, можно ли установить значение для данного поля
				// Присваиваем значение провайдера полю структуры
				field.Set(reflect.ValueOf(provider))
				logger.Info().Msgf("Provider '%s' has been set", fieldType.Name)

				return nil
			} else {
				logger.Info().Msgf("Provider '%s' cannot be modified", fieldType.Name)

				return fmt.Errorf("provider '%s' cannot be modified", fieldType.Name)
			}
		}
	}

	// Проверяем поля структуры direct
	directValue := reflect.ValueOf(&s.direct).Elem()
	directType := reflect.TypeOf(s.direct)

	for i := 0; i < directValue.NumField(); i++ {
		field := directValue.Field(i)
		fieldType := directType.Field(i)

		if fieldType.Name == providerName {
			if field.CanSet() {
				field.Set(reflect.ValueOf(provider))
				fmt.Printf("Provider '%s' in direct has been set\n", fieldType.Name)
				return nil
			} else {
				return fmt.Errorf("provider '%s' in direct cannot be modified", fieldType.Name)
			}
		}
	}

	// Возвращаем ошибку, если не найдено подходящее поле для провайдера
	return fmt.Errorf("no suitable field found for provider with name '%s' and type '%s'", providerName, reflect.TypeOf(provider).Name())
}
