// Code generated by git.redmadrobot.com/backend-go/meroving. DO NOT EDIT.
package main

import (
	"context"
	"fmt"
	"log"
	"time"

	"git.redmadrobot.com/backend-go/rmr-pkg/providers/chronos"
	"git.redmadrobot.com/backend-go/rmr-pkg/providers/otelx"
	"git.redmadrobot.com/backend-go/rmr-pkg/providers/s3"
	"git.redmadrobot.com/backend-go/rmr-pkg/providers/sentry"
	"git.redmadrobot.com/backend-go/rmr-pkg/providers/vault"
	"github.com/google/uuid"
	"go.opentelemetry.io/otel"

	cfgbtsBridge "git.redmadrobot.com/zaman/backend/zaman/config/services/bts-bridge"
	cfgcolvirBridge "git.redmadrobot.com/zaman/backend/zaman/config/services/colvir-bridge"
	cfgloans "git.redmadrobot.com/zaman/backend/zaman/config/services/loans"
	cfgpkbBridge "git.redmadrobot.com/zaman/backend/zaman/config/services/pkb-bridge"
	cfgusers "git.redmadrobot.com/zaman/backend/zaman/config/services/users"
	pbbtsBridge "git.redmadrobot.com/zaman/backend/zaman/specs/proto/bts-bridge"
	pbcolvirBridge "git.redmadrobot.com/zaman/backend/zaman/specs/proto/colvir-bridge"
	pbloans "git.redmadrobot.com/zaman/backend/zaman/specs/proto/loans"
	pbpkbBridge "git.redmadrobot.com/zaman/backend/zaman/specs/proto/pkb-bridge"
	pbusers "git.redmadrobot.com/zaman/backend/zaman/specs/proto/users"

	"git.redmadrobot.com/backend-go/rmr-pkg/core"
	"git.redmadrobot.com/backend-go/rmr-pkg/core/logs"
	"git.redmadrobot.com/backend-go/rmr-pkg/providers/kafka/confluent"

	"git.redmadrobot.com/backend-go/rmr-pkg/grpcx"

	"git.redmadrobot.com/backend-go/rmr-pkg/db"
	"git.redmadrobot.com/backend-go/rmr-pkg/db/entx"

	"git.redmadrobot.com/zaman/backend/zaman/config/services/collection"
	"git.redmadrobot.com/zaman/backend/zaman/services/collection/server"
	"git.redmadrobot.com/zaman/backend/zaman/services/collection/storage"

	"git.redmadrobot.com/zaman/backend/zaman/cmd/services/collection/providers"
	"git.redmadrobot.com/zaman/backend/zaman/services/collection/usecase"
)

func main() {
	time.Local = time.UTC

	rootCtx := context.Background()
	cfg, envs := initConfigs(rootCtx)

	// Initialize Sentry
	err := sentry.NewGRPCSentry(rootCtx, cfg.Sentry, nil)
	if err != nil {
		log.Printf("unable to connect to sentry: %v", err)
		return
	}

	// Initialize OTEL
	traceProvider, err := otelx.NewOTELTraceProvider(rootCtx, true, cfg.Trace)
	if err != nil {
		log.Printf("Failed to create OTEL trace provider: %v", err)
		return
	}
	otel.SetTracerProvider(traceProvider)

	// Create initial span
	tracer := otel.Tracer("collection")
	rootCtx, span := tracer.Start(rootCtx, "main")

	defer span.End()

	// Create logger
	log := logs.Logger(
		rootCtx,
		cfg.Logger,
		logs.InstanceIDTag.Option(uuid.New()), logs.Options(),
	)
	defer log.Fatal().Msgf("application stopped")

	ctx, cancel := context.WithCancel(log.WithContext(rootCtx))
	defer cancel()

	if err := run(ctx, cancel, cfg, envs); err != nil {
		log.Info().Err(err).Msg("unable to start application")
	}
}

func initConfigs(ctx context.Context) (*collection.Config, []string) {
	cfg, envs := collection.LoadFromEnv()

	// Initialize Vault
	vClient, err := vault.NewVaultClientKV2(cfg.Vault, cfg.App.AppPrefix)
	if err == nil {
		logs.FromContext(ctx).Info().Msgf("Successfully created Vault client for %s", cfg.App.AppPrefix)
		envSyncer := vault.NewEnvSyncer(vClient)
		if syncErr := envSyncer.SyncEnvWithVault(ctx, envs); syncErr != nil {
			logs.FromContext(ctx).Fatal().Msgf("Error synchronizing environment variables with Vault: %v", syncErr)
		}
		cfg, _ = collection.LoadFromEnv()
	}

	return cfg, envs
}

func run(ctx context.Context, cancel context.CancelFunc, cfg *collection.Config, envs []string) error {
	var err error
	locator := providers.NewServiceLocator(ctx)

	// Initialize Database
	dbStorage, storageClose, err := databaseStorage(ctx, cfg)
	defer storageClose()
	if err != nil {
		return fmt.Errorf("unable to init db storage: %w", err)
	}
	locator.Register("Storage", dbStorage)

	// Initialize Kafka
	eventBus, err := confluent.NewEventBus(ctx, cfg.Kafka,
		core.ErrorCallbackFn(func(err error) bool {
			logs.FromContext(ctx).Err(err).Msg("failed event bus kafka")
			return true
		}))
	if err != nil {
		return fmt.Errorf("failed to create kafka client: %w", err)
	}
	defer eventBus.Close(ctx)
	defer cancel()
	locator.Register("Event", eventBus)

	// Initialize S3
	objectStorage, err := s3.NewClient(ctx, cfg.S3)
	if err != nil {
		return fmt.Errorf("unable to establish s3 connection: %w", err)
	}
	locator.Register("ObjectStorage", objectStorage)

	// Initialize Chronos client
	chronos, err := chronos.NewClient(cfg.Chronos)
	if err != nil {
		logs.FromContext(ctx).Fatal().Msgf("Failed to create Chronos client: %v", err)
	}
	locator.Register("Chronos", chronos)
	defer chronos.Stop()
	// Initialize colvir-bridge gRPC client
	cfgGrpccolvirBridge, envsGrpccolvirBridge := cfgcolvirBridge.LoadFromEnv()

	vClientcolvirBridge, err := vault.NewVaultClientKV2(cfg.Vault, cfgGrpccolvirBridge.App.AppPrefix)
	if err == nil {
		log.Printf("Successfully created Vault client for %s", cfgGrpccolvirBridge.App.AppPrefix)
		envSyncer := vault.NewEnvSyncer(vClientcolvirBridge)
		if syncErr := envSyncer.SyncEnvWithVault(ctx, envsGrpccolvirBridge); syncErr != nil {
			log.Printf("Error synchronizing environment variables with Vault: %v", syncErr)
		}
		cfgGrpccolvirBridge, _ = cfgcolvirBridge.LoadFromEnv()
	}

	colvirBridgeServer, err := grpcx.ConnectServer(cfgGrpccolvirBridge.GRPC)
	if err != nil {
		logs.FromContext(ctx).Fatal().Msgf("Failed to create gRPC client: %v", err)
	}
	defer colvirBridgeServer.Close()
	colvirBridge := pbcolvirBridge.NewColvirbridgeClient(colvirBridgeServer)
	locator.Register("Colvirbridge", colvirBridge)

	// Initialize pkb-bridge gRPC client
	cfgGrpcpkbBridge, envsGrpcpkbBridge := cfgpkbBridge.LoadFromEnv()

	vClientpkbBridge, err := vault.NewVaultClientKV2(cfg.Vault, cfgGrpcpkbBridge.App.AppPrefix)
	if err == nil {
		log.Printf("Successfully created Vault client for %s", cfgGrpcpkbBridge.App.AppPrefix)
		envSyncer := vault.NewEnvSyncer(vClientpkbBridge)
		if syncErr := envSyncer.SyncEnvWithVault(ctx, envsGrpcpkbBridge); syncErr != nil {
			log.Printf("Error synchronizing environment variables with Vault: %v", syncErr)
		}
		cfgGrpcpkbBridge, _ = cfgpkbBridge.LoadFromEnv()
	}

	pkbBridgeServer, err := grpcx.ConnectServer(cfgGrpcpkbBridge.GRPC)
	if err != nil {
		logs.FromContext(ctx).Fatal().Msgf("Failed to create gRPC client: %v", err)
	}
	defer pkbBridgeServer.Close()
	pkbBridge := pbpkbBridge.NewPkbbridgeClient(pkbBridgeServer)
	locator.Register("Pkbbridge", pkbBridge)

	// Initialize users gRPC client
	cfgGrpcusers, envsGrpcusers := cfgusers.LoadFromEnv()

	vClientusers, err := vault.NewVaultClientKV2(cfg.Vault, cfgGrpcusers.App.AppPrefix)
	if err == nil {
		log.Printf("Successfully created Vault client for %s", cfgGrpcusers.App.AppPrefix)
		envSyncer := vault.NewEnvSyncer(vClientusers)
		if syncErr := envSyncer.SyncEnvWithVault(ctx, envsGrpcusers); syncErr != nil {
			log.Printf("Error synchronizing environment variables with Vault: %v", syncErr)
		}
		cfgGrpcusers, _ = cfgusers.LoadFromEnv()
	}

	usersServer, err := grpcx.ConnectServer(cfgGrpcusers.GRPC)
	if err != nil {
		logs.FromContext(ctx).Fatal().Msgf("Failed to create gRPC client: %v", err)
	}
	defer usersServer.Close()
	users := pbusers.NewUsersClient(usersServer)
	locator.Register("Users", users)

	// Initialize loans gRPC client
	cfgGrpcloans, envsGrpcloans := cfgloans.LoadFromEnv()

	vClientloans, err := vault.NewVaultClientKV2(cfg.Vault, cfgGrpcloans.App.AppPrefix)
	if err == nil {
		log.Printf("Successfully created Vault client for %s", cfgGrpcloans.App.AppPrefix)
		envSyncer := vault.NewEnvSyncer(vClientloans)
		if syncErr := envSyncer.SyncEnvWithVault(ctx, envsGrpcloans); syncErr != nil {
			log.Printf("Error synchronizing environment variables with Vault: %v", syncErr)
		}
		cfgGrpcloans, _ = cfgloans.LoadFromEnv()
	}

	loansServer, err := grpcx.ConnectServer(cfgGrpcloans.GRPC)
	if err != nil {
		logs.FromContext(ctx).Fatal().Msgf("Failed to create gRPC client: %v", err)
	}
	defer loansServer.Close()
	loans := pbloans.NewLoansClient(loansServer)
	locator.Register("Loans", loans)

	// Initialize bts-bridge gRPC client
	cfgGrpcbtsBridge, envsGrpcbtsBridge := cfgbtsBridge.LoadFromEnv()

	vClientbtsBridge, err := vault.NewVaultClientKV2(cfg.Vault, cfgGrpcbtsBridge.App.AppPrefix)
	if err == nil {
		log.Printf("Successfully created Vault client for %s", cfgGrpcbtsBridge.App.AppPrefix)
		envSyncer := vault.NewEnvSyncer(vClientbtsBridge)
		if syncErr := envSyncer.SyncEnvWithVault(ctx, envsGrpcbtsBridge); syncErr != nil {
			log.Printf("Error synchronizing environment variables with Vault: %v", syncErr)
		}
		cfgGrpcbtsBridge, _ = cfgbtsBridge.LoadFromEnv()
	}

	btsBridgeServer, err := grpcx.ConnectServer(cfgGrpcbtsBridge.GRPC)
	if err != nil {
		logs.FromContext(ctx).Fatal().Msgf("Failed to create gRPC client: %v", err)
	}
	defer btsBridgeServer.Close()
	btsBridge := pbbtsBridge.NewBtsbridgeClient(btsBridgeServer)
	locator.Register("Btsbridge", btsBridge)

	// Adding custom providers to the locator using the NewCustomProviders method.
	// This method can be used to add providers that were not automatically generated
	// or require special configuration before being registered.
	err = providers.NewCustomProviders(ctx, *cfg, locator)
	if err != nil {
		log.Fatalf("Failed to create providers: %v", err)
	}

	// Initialize Use cases
	useCases := usecase.New(ctx, *locator, cfg)
	// Initialize Kafka Consumer
	useCases.InitConsumer(ctx)
	// Initialize Chronos tasks
	useCases.InitChronos(ctx)

	// Initialize gRPC server
	service := server.NewServerOptions(useCases, cfg)
	grpcServer, err := service.NewServer(cfg.GRPC)
	if err != nil {
		log.Fatalf("Failed to create gRPC Server: %v", err)
	}

	return grpcx.StartServer(ctx, cfg.GRPC, grpcServer)
}

func databaseStorage(ctx context.Context, cfg *collection.Config) (storage.Storage, func(), error) {
	// Migrate the database.
	err := db.Migrate(ctx, cfg.PostgresDB, db.MigrateDefault(cfg.PostgresDB))
	if err != nil {
		return nil, func() {}, fmt.Errorf("failed to migrate database: %w", err)
	}

	// Get the database driver.
	postgresDriver, err := entx.Driver(cfg.PostgresDB)
	if err != nil {
		return nil, func() {}, fmt.Errorf("failed connect to DB: %w", err)
	}

	// Initialize the ent client.
	postgresDBClient, err := storage.NewPostgresDBClient(ctx, postgresDriver, cfg.PostgresDB.Debug)
	if err != nil {
		return nil, func() {}, fmt.Errorf("failed to init ent client: %w", err)
	}

	// Define the function to close the database connections.
	storageClientsCloseFunc := func() {
		if err = postgresDriver.Close(); err != nil {
			logs.FromContext(ctx).Err(err).Msg("failed to close ent client")
		}
	}

	storageDeps := storage.StorageDependencies{
		PostgresClient: postgresDBClient,
		SQLClient:      postgresDriver,
	}

	// Return the storage.Storage object and the function to close the database connection.
	return storage.NewStorage(storageDeps), storageClientsCloseFunc, nil
}
