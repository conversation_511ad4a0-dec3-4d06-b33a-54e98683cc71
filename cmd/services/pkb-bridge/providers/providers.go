package providers

import (
	"context"
	"fmt"

	pkbbridge "git.redmadrobot.com/zaman/backend/zaman/config/services/pkb-bridge"
	"git.redmadrobot.com/zaman/backend/zaman/pkg/gkb"
	"git.redmadrobot.com/zaman/backend/zaman/pkg/pkb"
)

// direct - структура для хранения собственных провайдеров.
// Важно: имя полей должны полностью совпадать с именем провайдера при его регистрации.
type direct struct {
	PKBProvider pkb.PKBProvider
	GkbProvider gkb.GkbProvider
}

// NewCustomProviders - пример функции, которая добавляет в локатор собственные провайдеры, обходя кодогенерацию.
func NewCustomProviders(ctx context.Context, cfg pkbbridge.Config, locator *ServiceLocatorImpl) error {
	pkbProvider := pkb.NewClient(cfg.App.PKBProvider)
	if err := locator.Register("PKBProvider", pkbProvider); err != nil {
		return fmt.Errorf("failed to register MyProvider: %w", err)
	}

	gkbProvider := gkb.NewGkbProvider(cfg.App.GKBProvider)
	if err := locator.Register("GkbProvider", gkbProvider); err != nil {
		return fmt.Errorf("failed to register GkbProvider: %w", err)
	}

	return nil
}
