package providers

import (
	"context"
	"fmt"

	colvirbridge "git.redmadrobot.com/zaman/backend/zaman/config/services/colvir-bridge"
	"git.redmadrobot.com/zaman/backend/zaman/pkg/colvir"
)

type direct struct {
	ColvirProvider colvir.ColvirProvider
}

// NewCustomProviders - пример функции, которая добавляет в локатор собственные провайдеры, обходя кодогенерацию.
func NewCustomProviders(ctx context.Context, cfg colvirbridge.Config, locator *ServiceLocatorImpl) error {
	clvConfig := &colvir.Config{
		BaseURL:   cfg.App.BaseURL,
		V2BaseURL: cfg.App.V2BaseURL,
	}

	clvProvider := colvir.NewColvirProvider(clvConfig)
	if err := locator.Register("ColvirProvider", clvProvider); err != nil {
		return fmt.Errorf("failed to register ColvirProvider: %w", err)
	}

	return nil
}
