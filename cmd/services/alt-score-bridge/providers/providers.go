package providers

import (
	"context"
	"fmt"

	"git.redmadrobot.com/backend-go/rmr-pkg/core/logs"

	altscorebridge "git.redmadrobot.com/zaman/backend/zaman/config/services/alt-score-bridge"
	"git.redmadrobot.com/zaman/backend/zaman/pkg/altscore"
)

// direct - структура для хранения собственных провайдеров.
// Важно: имя полей должны полностью совпадать с именем провайдера при его регистрации.
type direct struct {
	AltScoreProvider altscore.AltScoreProvider
}

// NewCustomProviders - пример функции, которая добавляет в локатор собственные провайдеры, обходя кодогенерацию.
func NewCustomProviders(ctx context.Context, cfg altscorebridge.Config, locator *ServiceLocatorImpl) error {
	log := logs.FromContext(ctx)
	altScoreProvider := altscore.NewAltScoreProvider(cfg.App.AltScoreProviderCfg)
	if err := locator.Register("AltScoreProvider", altScoreProvider); err != nil {
		log.Error().Err(err).Msg("failed to register AltScoreProvider")
		return fmt.Errorf("failed to register AltScoreProvider: %w", err)
	}
	log.Debug().Msgf("altScoreProvider initialized with base URL: %v", altScoreProvider.BaseURL)

	return nil
}
