package providers

import (
	"context"
	"fmt"

	juicyscorebridge "git.redmadrobot.com/zaman/backend/zaman/config/services/juicyscore-bridge"
	"git.redmadrobot.com/zaman/backend/zaman/pkg/juicyscore"
)

type direct struct {
	JuicyScoreProvider juicyscore.JuicyScoreProvider
}

func NewCustomProviders(ctx context.Context, cfg juicyscorebridge.Config, locator *ServiceLocatorImpl) error {
	juicyscoreProvider := juicyscore.NewJuicyScoreClient(cfg.App.JuicyScoreProvider)
	if err := locator.Register("JuicyScoreProvider", juicyscoreProvider); err != nil {
		return fmt.Errorf("failed to register JuicyScoreProvider: %w", err)
	}

	return nil
}
