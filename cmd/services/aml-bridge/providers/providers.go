package providers

import (
	"context"
	"fmt"

	amlbridge "git.redmadrobot.com/zaman/backend/zaman/config/services/aml-bridge"
	"git.redmadrobot.com/zaman/backend/zaman/pkg/aml"
)

// direct - структура для хранения собственных провайдеров.
// Важно: имя полей должны полностью совпадать с именем провайдера при его регистрации.
type direct struct {
	AMLProvider aml.AMLProvider
}

// NewCustomProviders - пример функции, которая добавляет в локатор собственные провайдеры, обходя кодогенерацию.
func NewCustomProviders(ctx context.Context, cfg amlbridge.Config, locator *ServiceLocatorImpl) error {
	amlProvider := aml.NewAMLProvider(&cfg)
	if err := locator.Register("AMLProvider", amlProvider); err != nil {
		return fmt.Errorf("failed to register AMLProvider: %w", err)
	}
	return nil
}
