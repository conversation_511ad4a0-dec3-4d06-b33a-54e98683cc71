package providers

import (
	"context"
	"fmt"

	seonbridge "git.redmadrobot.com/zaman/backend/zaman/config/services/seon-bridge"
	"git.redmadrobot.com/zaman/backend/zaman/pkg/seon"
)

type direct struct {
	SeonProvider seon.SeonProvider
}

func NewCustomProviders(ctx context.Context, cfg seonbridge.Config, locator *ServiceLocatorImpl) error {
	// Регистрация провайдера Seon
	seonProvider := seon.NewClient(cfg.App.Seon)

	if err := locator.Register("SeonProvider", seonProvider); err != nil {
		return fmt.Errorf("failed to register SeonProvider: %w", err)
	}

	return nil
}
