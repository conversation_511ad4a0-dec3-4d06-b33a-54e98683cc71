package providers

import (
	"context"

	"git.redmadrobot.com/backend-go/rmr-pkg/core/logs"

	"git.redmadrobot.com/zaman/backend/zaman/pkg/silkpay"

	processingbridge "git.redmadrobot.com/zaman/backend/zaman/config/services/processing-bridge"
)

// direct - структура для хранения собственных провайдеров.
// Важно: имя полей должны полностью совпадать с именем провайдера при его регистрации.
type direct struct {
	SilkpayProvider silkpay.SilkpayProvider
}

// NewCustomProviders - пример функции, которая добавляет в локатор собственные провайдеры, обходя кодогенерацию.
func NewCustomProviders(ctx context.Context, cfg processingbridge.Config, locator *ServiceLocatorImpl) error {
	logger := logs.FromContext(ctx)

	provider := silkpay.NewSilkPayClient(cfg.App.SilkPayCfg)
	logger.Debug().Msgf("initiated silkpay provider with base URL: %s", provider.BaseURL)

	if err := locator.Register("SilkpayProvider", provider); err != nil {
		return err
	}

	return nil
}
