// Code generated by git.redmadrobot.com/backend-go/meroving. DO NOT EDIT.
package main

import (
	"context"
	"fmt"
	"log"
	"time"

	"git.redmadrobot.com/backend-go/rmr-pkg/providers/otelx"
	"git.redmadrobot.com/backend-go/rmr-pkg/providers/redis"
	"git.redmadrobot.com/backend-go/rmr-pkg/providers/s3"
	"git.redmadrobot.com/backend-go/rmr-pkg/providers/sentry"
	"git.redmadrobot.com/backend-go/rmr-pkg/providers/vault"
	"github.com/google/uuid"
	"go.opentelemetry.io/otel"

	cfgamlBridge "git.redmadrobot.com/zaman/backend/zaman/config/services/aml-bridge"
	cfgbtsBridge "git.redmadrobot.com/zaman/backend/zaman/config/services/bts-bridge"
	cfgcardsAccounts "git.redmadrobot.com/zaman/backend/zaman/config/services/cards-accounts"
	cfgcolvirBridge "git.redmadrobot.com/zaman/backend/zaman/config/services/colvir-bridge"
	cfgdictionary "git.redmadrobot.com/zaman/backend/zaman/config/services/dictionary"
	cfgdocuments "git.redmadrobot.com/zaman/backend/zaman/config/services/documents"
	cfgkeycloakProxy "git.redmadrobot.com/zaman/backend/zaman/config/services/keycloak-proxy"
	cfgkgdBridge "git.redmadrobot.com/zaman/backend/zaman/config/services/kgd-bridge"
	cfgliveness "git.redmadrobot.com/zaman/backend/zaman/config/services/liveness"
	cfgnotifications "git.redmadrobot.com/zaman/backend/zaman/config/services/notifications"
	cfgotp "git.redmadrobot.com/zaman/backend/zaman/config/services/otp"
	cfgpkbBridge "git.redmadrobot.com/zaman/backend/zaman/config/services/pkb-bridge"
	cfgqazpostBridge "git.redmadrobot.com/zaman/backend/zaman/config/services/qazpost-bridge"
	cfgtaskManager "git.redmadrobot.com/zaman/backend/zaman/config/services/task-manager"
	pbamlBridge "git.redmadrobot.com/zaman/backend/zaman/specs/proto/aml-bridge"
	pbbtsBridge "git.redmadrobot.com/zaman/backend/zaman/specs/proto/bts-bridge"
	pbcardsAccounts "git.redmadrobot.com/zaman/backend/zaman/specs/proto/cards-accounts"
	pbcolvirBridge "git.redmadrobot.com/zaman/backend/zaman/specs/proto/colvir-bridge"
	pbdictionary "git.redmadrobot.com/zaman/backend/zaman/specs/proto/dictionary"
	pbdocuments "git.redmadrobot.com/zaman/backend/zaman/specs/proto/documents"
	pbkeycloakProxy "git.redmadrobot.com/zaman/backend/zaman/specs/proto/keycloak-proxy"
	pbkgdBridge "git.redmadrobot.com/zaman/backend/zaman/specs/proto/kgd-bridge"
	pbliveness "git.redmadrobot.com/zaman/backend/zaman/specs/proto/liveness"
	pbnotifications "git.redmadrobot.com/zaman/backend/zaman/specs/proto/notifications"
	pbotp "git.redmadrobot.com/zaman/backend/zaman/specs/proto/otp"
	pbpkbBridge "git.redmadrobot.com/zaman/backend/zaman/specs/proto/pkb-bridge"
	pbqazpostBridge "git.redmadrobot.com/zaman/backend/zaman/specs/proto/qazpost-bridge"
	pbtaskManager "git.redmadrobot.com/zaman/backend/zaman/specs/proto/task-manager"

	"git.redmadrobot.com/backend-go/rmr-pkg/core"
	"git.redmadrobot.com/backend-go/rmr-pkg/core/logs"
	"git.redmadrobot.com/backend-go/rmr-pkg/providers/kafka/confluent"

	"git.redmadrobot.com/backend-go/rmr-pkg/grpcx"

	"git.redmadrobot.com/backend-go/rmr-pkg/db"
	"git.redmadrobot.com/backend-go/rmr-pkg/db/entx"

	"git.redmadrobot.com/zaman/backend/zaman/config/services/users"
	"git.redmadrobot.com/zaman/backend/zaman/services/users/server"
	"git.redmadrobot.com/zaman/backend/zaman/services/users/storage"

	"git.redmadrobot.com/zaman/backend/zaman/cmd/services/users/providers"
	"git.redmadrobot.com/zaman/backend/zaman/services/users/usecase"
)

func main() {
	time.Local = time.UTC

	rootCtx := context.Background()
	cfg, envs := initConfigs(rootCtx)

	// Initialize Sentry
	err := sentry.NewGRPCSentry(rootCtx, cfg.Sentry, nil)
	if err != nil {
		log.Printf("unable to connect to sentry: %v", err)
		return
	}

	// Initialize OTEL
	traceProvider, err := otelx.NewOTELTraceProvider(rootCtx, true, cfg.Trace)
	if err != nil {
		log.Printf("Failed to create OTEL trace provider: %v", err)
		return
	}
	otel.SetTracerProvider(traceProvider)

	// Create initial span
	tracer := otel.Tracer("users")
	rootCtx, span := tracer.Start(rootCtx, "main")

	defer span.End()

	// Create logger
	log := logs.Logger(
		rootCtx,
		cfg.Logger,
		logs.InstanceIDTag.Option(uuid.New()), logs.Options(),
	)
	defer log.Fatal().Msgf("application stopped")

	ctx, cancel := context.WithCancel(log.WithContext(rootCtx))
	defer cancel()

	if err := run(ctx, cancel, cfg, envs); err != nil {
		log.Info().Err(err).Msg("unable to start application")
	}
}

func initConfigs(ctx context.Context) (*users.Config, []string) {
	cfg, envs := users.LoadFromEnv()

	// Initialize Vault
	vClient, err := vault.NewVaultClientKV2(cfg.Vault, cfg.App.AppPrefix)
	if err == nil {
		logs.FromContext(ctx).Info().Msgf("Successfully created Vault client for %s", cfg.App.AppPrefix)
		envSyncer := vault.NewEnvSyncer(vClient)
		if syncErr := envSyncer.SyncEnvWithVault(ctx, envs); syncErr != nil {
			logs.FromContext(ctx).Fatal().Msgf("Error synchronizing environment variables with Vault: %v", syncErr)
		}
		cfg, _ = users.LoadFromEnv()
	}

	return cfg, envs
}

func run(ctx context.Context, cancel context.CancelFunc, cfg *users.Config, envs []string) error {
	var err error
	locator := providers.NewServiceLocator(ctx)

	// Initialize Database
	dbStorage, storageClose, err := databaseStorage(ctx, cfg)
	defer storageClose()
	if err != nil {
		return fmt.Errorf("unable to init db storage: %w", err)
	}
	locator.Register("Storage", dbStorage)

	// Initialize Kafka
	eventBus, err := confluent.NewEventBus(ctx, cfg.Kafka,
		core.ErrorCallbackFn(func(err error) bool {
			logs.FromContext(ctx).Err(err).Msg("failed event bus kafka")
			return true
		}))
	if err != nil {
		return fmt.Errorf("failed to create kafka client: %w", err)
	}
	defer eventBus.Close(ctx)
	defer cancel()
	locator.Register("Event", eventBus)

	// Initialize S3
	objectStorage, err := s3.NewClient(ctx, cfg.S3)
	if err != nil {
		return fmt.Errorf("unable to establish s3 connection: %w", err)
	}
	locator.Register("ObjectStorage", objectStorage)

	// Initialize Redis cache
	redis, err := redis.NewClient(ctx, cfg.Redis)
	if err != nil {
		logs.FromContext(ctx).Fatal().Msgf("Failed to create Redis client: %v", err)
	}
	locator.Register("Redis", redis)
	defer redis.Close(ctx)

	// Initialize documents gRPC client
	cfgGrpcdocuments, envsGrpcdocuments := cfgdocuments.LoadFromEnv()

	vClientdocuments, err := vault.NewVaultClientKV2(cfg.Vault, cfgGrpcdocuments.App.AppPrefix)
	if err == nil {
		log.Printf("Successfully created Vault client for %s", cfgGrpcdocuments.App.AppPrefix)
		envSyncer := vault.NewEnvSyncer(vClientdocuments)
		if syncErr := envSyncer.SyncEnvWithVault(ctx, envsGrpcdocuments); syncErr != nil {
			log.Printf("Error synchronizing environment variables with Vault: %v", syncErr)
		}
		cfgGrpcdocuments, _ = cfgdocuments.LoadFromEnv()
	}

	documentsServer, err := grpcx.ConnectServer(cfgGrpcdocuments.GRPC)
	if err != nil {
		logs.FromContext(ctx).Fatal().Msgf("Failed to create gRPC client: %v", err)
	}
	defer documentsServer.Close()
	documents := pbdocuments.NewDocumentsClient(documentsServer)
	locator.Register("Documents", documents)

	// Initialize notifications gRPC client
	cfgGrpcnotifications, envsGrpcnotifications := cfgnotifications.LoadFromEnv()

	vClientnotifications, err := vault.NewVaultClientKV2(cfg.Vault, cfgGrpcnotifications.App.AppPrefix)
	if err == nil {
		log.Printf("Successfully created Vault client for %s", cfgGrpcnotifications.App.AppPrefix)
		envSyncer := vault.NewEnvSyncer(vClientnotifications)
		if syncErr := envSyncer.SyncEnvWithVault(ctx, envsGrpcnotifications); syncErr != nil {
			log.Printf("Error synchronizing environment variables with Vault: %v", syncErr)
		}
		cfgGrpcnotifications, _ = cfgnotifications.LoadFromEnv()
	}

	notificationsServer, err := grpcx.ConnectServer(cfgGrpcnotifications.GRPC)
	if err != nil {
		logs.FromContext(ctx).Fatal().Msgf("Failed to create gRPC client: %v", err)
	}
	defer notificationsServer.Close()
	notifications := pbnotifications.NewNotificationsClient(notificationsServer)
	locator.Register("Notifications", notifications)

	// Initialize otp gRPC client
	cfgGrpcotp, envsGrpcotp := cfgotp.LoadFromEnv()

	vClientotp, err := vault.NewVaultClientKV2(cfg.Vault, cfgGrpcotp.App.AppPrefix)
	if err == nil {
		log.Printf("Successfully created Vault client for %s", cfgGrpcotp.App.AppPrefix)
		envSyncer := vault.NewEnvSyncer(vClientotp)
		if syncErr := envSyncer.SyncEnvWithVault(ctx, envsGrpcotp); syncErr != nil {
			log.Printf("Error synchronizing environment variables with Vault: %v", syncErr)
		}
		cfgGrpcotp, _ = cfgotp.LoadFromEnv()
	}

	otpServer, err := grpcx.ConnectServer(cfgGrpcotp.GRPC)
	if err != nil {
		logs.FromContext(ctx).Fatal().Msgf("Failed to create gRPC client: %v", err)
	}
	defer otpServer.Close()
	otp := pbotp.NewOtpClient(otpServer)
	locator.Register("Otp", otp)

	// Initialize keycloak-proxy gRPC client
	cfgGrpckeycloakProxy, envsGrpckeycloakProxy := cfgkeycloakProxy.LoadFromEnv()

	vClientkeycloakProxy, err := vault.NewVaultClientKV2(cfg.Vault, cfgGrpckeycloakProxy.App.AppPrefix)
	if err == nil {
		log.Printf("Successfully created Vault client for %s", cfgGrpckeycloakProxy.App.AppPrefix)
		envSyncer := vault.NewEnvSyncer(vClientkeycloakProxy)
		if syncErr := envSyncer.SyncEnvWithVault(ctx, envsGrpckeycloakProxy); syncErr != nil {
			log.Printf("Error synchronizing environment variables with Vault: %v", syncErr)
		}
		cfgGrpckeycloakProxy, _ = cfgkeycloakProxy.LoadFromEnv()
	}

	keycloakProxyServer, err := grpcx.ConnectServer(cfgGrpckeycloakProxy.GRPC)
	if err != nil {
		logs.FromContext(ctx).Fatal().Msgf("Failed to create gRPC client: %v", err)
	}
	defer keycloakProxyServer.Close()
	keycloakProxy := pbkeycloakProxy.NewKeycloakproxyClient(keycloakProxyServer)
	locator.Register("Keycloakproxy", keycloakProxy)

	// Initialize kgd-bridge gRPC client
	cfgGrpckgdBridge, envsGrpckgdBridge := cfgkgdBridge.LoadFromEnv()

	vClientkgdBridge, err := vault.NewVaultClientKV2(cfg.Vault, cfgGrpckgdBridge.App.AppPrefix)
	if err == nil {
		log.Printf("Successfully created Vault client for %s", cfgGrpckgdBridge.App.AppPrefix)
		envSyncer := vault.NewEnvSyncer(vClientkgdBridge)
		if syncErr := envSyncer.SyncEnvWithVault(ctx, envsGrpckgdBridge); syncErr != nil {
			log.Printf("Error synchronizing environment variables with Vault: %v", syncErr)
		}
		cfgGrpckgdBridge, _ = cfgkgdBridge.LoadFromEnv()
	}

	kgdBridgeServer, err := grpcx.ConnectServer(cfgGrpckgdBridge.GRPC)
	if err != nil {
		logs.FromContext(ctx).Fatal().Msgf("Failed to create gRPC client: %v", err)
	}
	defer kgdBridgeServer.Close()
	kgdBridge := pbkgdBridge.NewKgdbridgeClient(kgdBridgeServer)
	locator.Register("Kgdbridge", kgdBridge)

	// Initialize bts-bridge gRPC client
	cfgGrpcbtsBridge, envsGrpcbtsBridge := cfgbtsBridge.LoadFromEnv()

	vClientbtsBridge, err := vault.NewVaultClientKV2(cfg.Vault, cfgGrpcbtsBridge.App.AppPrefix)
	if err == nil {
		log.Printf("Successfully created Vault client for %s", cfgGrpcbtsBridge.App.AppPrefix)
		envSyncer := vault.NewEnvSyncer(vClientbtsBridge)
		if syncErr := envSyncer.SyncEnvWithVault(ctx, envsGrpcbtsBridge); syncErr != nil {
			log.Printf("Error synchronizing environment variables with Vault: %v", syncErr)
		}
		cfgGrpcbtsBridge, _ = cfgbtsBridge.LoadFromEnv()
	}

	btsBridgeServer, err := grpcx.ConnectServer(cfgGrpcbtsBridge.GRPC)
	if err != nil {
		logs.FromContext(ctx).Fatal().Msgf("Failed to create gRPC client: %v", err)
	}
	defer btsBridgeServer.Close()
	btsBridge := pbbtsBridge.NewBtsbridgeClient(btsBridgeServer)
	locator.Register("Btsbridge", btsBridge)

	// Initialize colvir-bridge gRPC client
	cfgGrpccolvirBridge, envsGrpccolvirBridge := cfgcolvirBridge.LoadFromEnv()

	vClientcolvirBridge, err := vault.NewVaultClientKV2(cfg.Vault, cfgGrpccolvirBridge.App.AppPrefix)
	if err == nil {
		log.Printf("Successfully created Vault client for %s", cfgGrpccolvirBridge.App.AppPrefix)
		envSyncer := vault.NewEnvSyncer(vClientcolvirBridge)
		if syncErr := envSyncer.SyncEnvWithVault(ctx, envsGrpccolvirBridge); syncErr != nil {
			log.Printf("Error synchronizing environment variables with Vault: %v", syncErr)
		}
		cfgGrpccolvirBridge, _ = cfgcolvirBridge.LoadFromEnv()
	}

	colvirBridgeServer, err := grpcx.ConnectServer(cfgGrpccolvirBridge.GRPC)
	if err != nil {
		logs.FromContext(ctx).Fatal().Msgf("Failed to create gRPC client: %v", err)
	}
	defer colvirBridgeServer.Close()
	colvirBridge := pbcolvirBridge.NewColvirbridgeClient(colvirBridgeServer)
	locator.Register("Colvirbridge", colvirBridge)

	// Initialize pkb-bridge gRPC client
	cfgGrpcpkbBridge, envsGrpcpkbBridge := cfgpkbBridge.LoadFromEnv()

	vClientpkbBridge, err := vault.NewVaultClientKV2(cfg.Vault, cfgGrpcpkbBridge.App.AppPrefix)
	if err == nil {
		log.Printf("Successfully created Vault client for %s", cfgGrpcpkbBridge.App.AppPrefix)
		envSyncer := vault.NewEnvSyncer(vClientpkbBridge)
		if syncErr := envSyncer.SyncEnvWithVault(ctx, envsGrpcpkbBridge); syncErr != nil {
			log.Printf("Error synchronizing environment variables with Vault: %v", syncErr)
		}
		cfgGrpcpkbBridge, _ = cfgpkbBridge.LoadFromEnv()
	}

	pkbBridgeServer, err := grpcx.ConnectServer(cfgGrpcpkbBridge.GRPC)
	if err != nil {
		logs.FromContext(ctx).Fatal().Msgf("Failed to create gRPC client: %v", err)
	}
	defer pkbBridgeServer.Close()
	pkbBridge := pbpkbBridge.NewPkbbridgeClient(pkbBridgeServer)
	locator.Register("Pkbbridge", pkbBridge)

	// Initialize dictionary gRPC client
	cfgGrpcdictionary, envsGrpcdictionary := cfgdictionary.LoadFromEnv()

	vClientdictionary, err := vault.NewVaultClientKV2(cfg.Vault, cfgGrpcdictionary.App.AppPrefix)
	if err == nil {
		log.Printf("Successfully created Vault client for %s", cfgGrpcdictionary.App.AppPrefix)
		envSyncer := vault.NewEnvSyncer(vClientdictionary)
		if syncErr := envSyncer.SyncEnvWithVault(ctx, envsGrpcdictionary); syncErr != nil {
			log.Printf("Error synchronizing environment variables with Vault: %v", syncErr)
		}
		cfgGrpcdictionary, _ = cfgdictionary.LoadFromEnv()
	}

	dictionaryServer, err := grpcx.ConnectServer(cfgGrpcdictionary.GRPC)
	if err != nil {
		logs.FromContext(ctx).Fatal().Msgf("Failed to create gRPC client: %v", err)
	}
	defer dictionaryServer.Close()
	dictionary := pbdictionary.NewDictionaryClient(dictionaryServer)
	locator.Register("Dictionary", dictionary)

	// Initialize liveness gRPC client
	cfgGrpcliveness, envsGrpcliveness := cfgliveness.LoadFromEnv()

	vClientliveness, err := vault.NewVaultClientKV2(cfg.Vault, cfgGrpcliveness.App.AppPrefix)
	if err == nil {
		log.Printf("Successfully created Vault client for %s", cfgGrpcliveness.App.AppPrefix)
		envSyncer := vault.NewEnvSyncer(vClientliveness)
		if syncErr := envSyncer.SyncEnvWithVault(ctx, envsGrpcliveness); syncErr != nil {
			log.Printf("Error synchronizing environment variables with Vault: %v", syncErr)
		}
		cfgGrpcliveness, _ = cfgliveness.LoadFromEnv()
	}

	livenessServer, err := grpcx.ConnectServer(cfgGrpcliveness.GRPC)
	if err != nil {
		logs.FromContext(ctx).Fatal().Msgf("Failed to create gRPC client: %v", err)
	}
	defer livenessServer.Close()
	liveness := pbliveness.NewLivenessClient(livenessServer)
	locator.Register("Liveness", liveness)

	// Initialize aml-bridge gRPC client
	cfgGrpcamlBridge, envsGrpcamlBridge := cfgamlBridge.LoadFromEnv()

	vClientamlBridge, err := vault.NewVaultClientKV2(cfg.Vault, cfgGrpcamlBridge.App.AppPrefix)
	if err == nil {
		log.Printf("Successfully created Vault client for %s", cfgGrpcamlBridge.App.AppPrefix)
		envSyncer := vault.NewEnvSyncer(vClientamlBridge)
		if syncErr := envSyncer.SyncEnvWithVault(ctx, envsGrpcamlBridge); syncErr != nil {
			log.Printf("Error synchronizing environment variables with Vault: %v", syncErr)
		}
		cfgGrpcamlBridge, _ = cfgamlBridge.LoadFromEnv()
	}

	amlBridgeServer, err := grpcx.ConnectServer(cfgGrpcamlBridge.GRPC)
	if err != nil {
		logs.FromContext(ctx).Fatal().Msgf("Failed to create gRPC client: %v", err)
	}
	defer amlBridgeServer.Close()
	amlBridge := pbamlBridge.NewAmlbridgeClient(amlBridgeServer)
	locator.Register("Amlbridge", amlBridge)

	// Initialize task-manager gRPC client
	cfgGrpctaskManager, envsGrpctaskManager := cfgtaskManager.LoadFromEnv()

	vClienttaskManager, err := vault.NewVaultClientKV2(cfg.Vault, cfgGrpctaskManager.App.AppPrefix)
	if err == nil {
		log.Printf("Successfully created Vault client for %s", cfgGrpctaskManager.App.AppPrefix)
		envSyncer := vault.NewEnvSyncer(vClienttaskManager)
		if syncErr := envSyncer.SyncEnvWithVault(ctx, envsGrpctaskManager); syncErr != nil {
			log.Printf("Error synchronizing environment variables with Vault: %v", syncErr)
		}
		cfgGrpctaskManager, _ = cfgtaskManager.LoadFromEnv()
	}

	taskManagerServer, err := grpcx.ConnectServer(cfgGrpctaskManager.GRPC)
	if err != nil {
		logs.FromContext(ctx).Fatal().Msgf("Failed to create gRPC client: %v", err)
	}
	defer taskManagerServer.Close()
	taskManager := pbtaskManager.NewTaskmanagerClient(taskManagerServer)
	locator.Register("Taskmanager", taskManager)

	// Initialize cards-accounts gRPC client
	cfgGrpccardsAccounts, envsGrpccardsAccounts := cfgcardsAccounts.LoadFromEnv()

	vClientcardsAccounts, err := vault.NewVaultClientKV2(cfg.Vault, cfgGrpccardsAccounts.App.AppPrefix)
	if err == nil {
		log.Printf("Successfully created Vault client for %s", cfgGrpccardsAccounts.App.AppPrefix)
		envSyncer := vault.NewEnvSyncer(vClientcardsAccounts)
		if syncErr := envSyncer.SyncEnvWithVault(ctx, envsGrpccardsAccounts); syncErr != nil {
			log.Printf("Error synchronizing environment variables with Vault: %v", syncErr)
		}
		cfgGrpccardsAccounts, _ = cfgcardsAccounts.LoadFromEnv()
	}

	cardsAccountsServer, err := grpcx.ConnectServer(cfgGrpccardsAccounts.GRPC)
	if err != nil {
		logs.FromContext(ctx).Fatal().Msgf("Failed to create gRPC client: %v", err)
	}
	defer cardsAccountsServer.Close()
	cardsAccounts := pbcardsAccounts.NewCardsaccountsClient(cardsAccountsServer)
	locator.Register("Cardsaccounts", cardsAccounts)

	// Initialize qazpost-bridge gRPC client
	cfgGrpcqazpostBridge, envsGrpcqazpostBridge := cfgqazpostBridge.LoadFromEnv()

	vClientqazpostBridge, err := vault.NewVaultClientKV2(cfg.Vault, cfgGrpcqazpostBridge.App.AppPrefix)
	if err == nil {
		log.Printf("Successfully created Vault client for %s", cfgGrpcqazpostBridge.App.AppPrefix)
		envSyncer := vault.NewEnvSyncer(vClientqazpostBridge)
		if syncErr := envSyncer.SyncEnvWithVault(ctx, envsGrpcqazpostBridge); syncErr != nil {
			log.Printf("Error synchronizing environment variables with Vault: %v", syncErr)
		}
		cfgGrpcqazpostBridge, _ = cfgqazpostBridge.LoadFromEnv()
	}

	qazpostBridgeServer, err := grpcx.ConnectServer(cfgGrpcqazpostBridge.GRPC)
	if err != nil {
		logs.FromContext(ctx).Fatal().Msgf("Failed to create gRPC client: %v", err)
	}
	defer qazpostBridgeServer.Close()
	qazpostBridge := pbqazpostBridge.NewQazpostbridgeClient(qazpostBridgeServer)
	locator.Register("Qazpostbridge", qazpostBridge)

	// Adding custom providers to the locator using the NewCustomProviders method.
	// This method can be used to add providers that were not automatically generated
	// or require special configuration before being registered.
	err = providers.NewCustomProviders(ctx, *cfg, locator)
	if err != nil {
		log.Fatalf("Failed to create providers: %v", err)
	}

	// Initialize Use cases
	useCases := usecase.New(ctx, *locator, cfg)
	// Initialize Kafka Consumer
	useCases.InitConsumer(ctx)

	// Initialize gRPC server
	service := server.NewServerOptions(useCases, cfg)
	grpcServer, err := service.NewServer(cfg.GRPC)
	if err != nil {
		log.Fatalf("Failed to create gRPC Server: %v", err)
	}

	return grpcx.StartServer(ctx, cfg.GRPC, grpcServer)
}

func databaseStorage(ctx context.Context, cfg *users.Config) (storage.Storage, func(), error) {
	// Migrate the database.
	err := db.Migrate(ctx, cfg.PostgresDB, db.MigrateDefault(cfg.PostgresDB))
	if err != nil {
		return nil, func() {}, fmt.Errorf("failed to migrate database: %w", err)
	}

	// Get the database driver.
	postgresDriver, err := entx.Driver(cfg.PostgresDB)
	if err != nil {
		return nil, func() {}, fmt.Errorf("failed connect to DB: %w", err)
	}

	// Initialize the ent client.
	postgresDBClient, err := storage.NewPostgresDBClient(ctx, postgresDriver, cfg.PostgresDB.Debug)
	if err != nil {
		return nil, func() {}, fmt.Errorf("failed to init ent client: %w", err)
	}

	// Define the function to close the database connections.
	storageClientsCloseFunc := func() {
		if err = postgresDriver.Close(); err != nil {
			logs.FromContext(ctx).Err(err).Msg("failed to close ent client")
		}
	}

	storageDeps := storage.StorageDependencies{
		PostgresClient: postgresDBClient,
		SQLClient:      postgresDriver,
	}

	// Return the storage.Storage object and the function to close the database connection.
	return storage.NewStorage(storageDeps), storageClientsCloseFunc, nil
}
