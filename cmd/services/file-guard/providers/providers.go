package providers

import (
	"context"

	fileguard "git.redmadrobot.com/zaman/backend/zaman/config/services/file-guard"
)

// direct - структура для хранения собственных провайдеров.
// Важно: имя полей должны полностью совпадать с именем провайдера при его регистрации.
type direct struct {
	// MyProvider myProvider.Client Пример провайдера, который вы хотите добавить
}

// NewCustomProviders - пример функции, которая добавляет в локатор собственные провайдеры, обходя кодогенерацию.
func NewCustomProviders(ctx context.Context, cfg fileguard.Config, locator *ServiceLocatorImpl) error {
	// Пример регистрации MyProvider в локаторе
	// if err := locator.Register("MyProvider", myProvider); err != nil {
	//	return fmt.Errorf("failed to register MyProvider: %w", err)
	// }

	return nil
}
