package providers

import (
	"context"
	"fmt"

	jirabridge "git.redmadrobot.com/zaman/backend/zaman/config/services/jira-bridge"
	"git.redmadrobot.com/zaman/backend/zaman/pkg/jira"
)

// direct - структура для хранения собственных провайдеров.
// Важно: имя полей должны полностью совпадать с именем провайдера при его регистрации.
type direct struct {
	// Пример провайдера, который вы хотите добавить
	JiraProvider jira.JiraProvider
}

// NewCustomProviders - пример функции, которая добавляет в локатор собственные провайдеры, обходя кодогенерацию.
func NewCustomProviders(ctx context.Context, cfg jirabridge.Config, locator *ServiceLocatorImpl) error {
	// Пример регистрации JiraProvider в локаторе
	jiraProvider := jira.NewJiraProvider(&cfg)
	if err := locator.Register("JiraProvider", jiraProvider); err != nil {
		return fmt.Errorf("failed to register JiraProvider: %w", err)
	}
	return nil
}
