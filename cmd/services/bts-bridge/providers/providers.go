package providers

import (
	"context"
	"fmt"

	"git.redmadrobot.com/zaman/backend/zaman/pkg/bts"

	btsbridge "git.redmadrobot.com/zaman/backend/zaman/config/services/bts-bridge"
)

// direct - структура для хранения собственных провайдеров.
// Важно: имя полей должны полностью совпадать с именем провайдера при его регистрации.
type direct struct {
	BTSProvider bts.BTSProvider
}

// NewCustomProviders - пример функции, которая добавляет в локатор собственные провайдеры, обходя кодогенерацию.
func NewCustomProviders(ctx context.Context, cfg btsbridge.Config, locator *ServiceLocatorImpl) error {
	btsProvider := bts.NewClient(ctx, cfg.App.BTSProvider)
	if err := locator.Register("BTSProvider", btsProvider); err != nil {
		return fmt.Errorf("failed to register MyProvider: %w", err)
	}

	return nil
}
