// Code generated by git.redmadrobot.com/backend-go/meroving. DO NOT EDIT.
package main

import (
	"context"
	"fmt"
	"log"
	"time"

	"git.redmadrobot.com/backend-go/rmr-pkg/providers/otelx"
	"git.redmadrobot.com/backend-go/rmr-pkg/providers/sentry"
	"git.redmadrobot.com/backend-go/rmr-pkg/providers/vault"
	"github.com/google/uuid"
	"go.opentelemetry.io/otel"

	cfgdocuments "git.redmadrobot.com/zaman/backend/zaman/config/services/documents"
	pbdocuments "git.redmadrobot.com/zaman/backend/zaman/specs/proto/documents"

	"git.redmadrobot.com/backend-go/rmr-pkg/core"
	"git.redmadrobot.com/backend-go/rmr-pkg/core/logs"
	"git.redmadrobot.com/backend-go/rmr-pkg/providers/kafka/confluent"

	"git.redmadrobot.com/backend-go/rmr-pkg/grpcx"

	"git.redmadrobot.com/backend-go/rmr-pkg/db"
	"git.redmadrobot.com/backend-go/rmr-pkg/db/entx"

	btsbridge "git.redmadrobot.com/zaman/backend/zaman/config/services/bts-bridge"
	"git.redmadrobot.com/zaman/backend/zaman/services/bts-bridge/server"
	"git.redmadrobot.com/zaman/backend/zaman/services/bts-bridge/storage"

	"git.redmadrobot.com/zaman/backend/zaman/cmd/services/bts-bridge/providers"
	"git.redmadrobot.com/zaman/backend/zaman/services/bts-bridge/usecase"
)

func main() {
	time.Local = time.UTC

	rootCtx := context.Background()
	cfg, envs := initConfigs(rootCtx)

	// Initialize Sentry
	err := sentry.NewGRPCSentry(rootCtx, cfg.Sentry, nil)
	if err != nil {
		log.Printf("unable to connect to sentry: %v", err)
		return
	}

	// Initialize OTEL
	traceProvider, err := otelx.NewOTELTraceProvider(rootCtx, true, cfg.Trace)
	if err != nil {
		log.Printf("Failed to create OTEL trace provider: %v", err)
		return
	}
	otel.SetTracerProvider(traceProvider)

	// Create initial span
	tracer := otel.Tracer("btsBridge")
	rootCtx, span := tracer.Start(rootCtx, "main")

	defer span.End()

	// Create logger
	log := logs.Logger(
		rootCtx,
		cfg.Logger,
		logs.InstanceIDTag.Option(uuid.New()), logs.Options(),
	)
	defer log.Fatal().Msgf("application stopped")

	ctx, cancel := context.WithCancel(log.WithContext(rootCtx))
	defer cancel()

	if err := run(ctx, cancel, cfg, envs); err != nil {
		log.Info().Err(err).Msg("unable to start application")
	}
}

func initConfigs(ctx context.Context) (*btsbridge.Config, []string) {
	cfg, envs := btsbridge.LoadFromEnv()

	// Initialize Vault
	vClient, err := vault.NewVaultClientKV2(cfg.Vault, cfg.App.AppPrefix)
	if err == nil {
		logs.FromContext(ctx).Info().Msgf("Successfully created Vault client for %s", cfg.App.AppPrefix)
		envSyncer := vault.NewEnvSyncer(vClient)
		if syncErr := envSyncer.SyncEnvWithVault(ctx, envs); syncErr != nil {
			logs.FromContext(ctx).Fatal().Msgf("Error synchronizing environment variables with Vault: %v", syncErr)
		}
		cfg, _ = btsbridge.LoadFromEnv()
	}

	return cfg, envs
}

func run(ctx context.Context, cancel context.CancelFunc, cfg *btsbridge.Config, envs []string) error {
	var err error
	locator := providers.NewServiceLocator(ctx)

	// Initialize Database
	dbStorage, storageClose, err := databaseStorage(ctx, cfg)
	defer storageClose()
	if err != nil {
		return fmt.Errorf("unable to init db storage: %w", err)
	}
	locator.Register("Storage", dbStorage)

	// Initialize Kafka
	eventBus, err := confluent.NewEventBus(ctx, cfg.Kafka,
		core.ErrorCallbackFn(func(err error) bool {
			logs.FromContext(ctx).Err(err).Msg("failed event bus kafka")
			return true
		}))
	if err != nil {
		return fmt.Errorf("failed to create kafka client: %w", err)
	}
	defer eventBus.Close(ctx)
	defer cancel()
	locator.Register("Event", eventBus)

	// Initialize documents gRPC client
	cfgGrpcdocuments, envsGrpcdocuments := cfgdocuments.LoadFromEnv()

	vClientdocuments, err := vault.NewVaultClientKV2(cfg.Vault, cfgGrpcdocuments.App.AppPrefix)
	if err == nil {
		log.Printf("Successfully created Vault client for %s", cfgGrpcdocuments.App.AppPrefix)
		envSyncer := vault.NewEnvSyncer(vClientdocuments)
		if syncErr := envSyncer.SyncEnvWithVault(ctx, envsGrpcdocuments); syncErr != nil {
			log.Printf("Error synchronizing environment variables with Vault: %v", syncErr)
		}
		cfgGrpcdocuments, _ = cfgdocuments.LoadFromEnv()
	}

	documentsServer, err := grpcx.ConnectServer(cfgGrpcdocuments.GRPC)
	if err != nil {
		logs.FromContext(ctx).Fatal().Msgf("Failed to create gRPC client: %v", err)
	}
	defer documentsServer.Close()
	documents := pbdocuments.NewDocumentsClient(documentsServer)
	locator.Register("Documents", documents)

	// Adding custom providers to the locator using the NewCustomProviders method.
	// This method can be used to add providers that were not automatically generated
	// or require special configuration before being registered.
	err = providers.NewCustomProviders(ctx, *cfg, locator)
	if err != nil {
		log.Fatalf("Failed to create providers: %v", err)
	}

	// Initialize Use cases
	useCases := usecase.New(ctx, *locator, cfg)
	// Initialize Kafka Consumer
	useCases.InitConsumer(ctx)

	// Initialize gRPC server
	service := server.NewServerOptions(useCases, cfg)
	grpcServer, err := service.NewServer(cfg.GRPC)
	if err != nil {
		log.Fatalf("Failed to create gRPC Server: %v", err)
	}

	return grpcx.StartServer(ctx, cfg.GRPC, grpcServer)
}

func databaseStorage(ctx context.Context, cfg *btsbridge.Config) (storage.Storage, func(), error) {
	// Migrate the database.
	err := db.Migrate(ctx, cfg.PostgresDB, db.MigrateDefault(cfg.PostgresDB))
	if err != nil {
		return nil, func() {}, fmt.Errorf("failed to migrate database: %w", err)
	}

	// Get the database driver.
	postgresDriver, err := entx.Driver(cfg.PostgresDB)
	if err != nil {
		return nil, func() {}, fmt.Errorf("failed connect to DB: %w", err)
	}

	// Initialize the ent client.
	postgresDBClient, err := storage.NewPostgresDBClient(ctx, postgresDriver, cfg.PostgresDB.Debug)
	if err != nil {
		return nil, func() {}, fmt.Errorf("failed to init ent client: %w", err)
	}

	// Initialize the mongo client.
	mongoClient, err := storage.NewMongoDBClient(ctx, cfg.MongoDB)
	if err != nil {
		return nil, func() {}, fmt.Errorf("failed to connect to mongo: %w", err)
	}

	// Define the function to close the database connections.
	storageClientsCloseFunc := func() {
		if err = postgresDriver.Close(); err != nil {
			logs.FromContext(ctx).Err(err).Msg("failed to close ent client")
		}
		if err = mongoClient.Disconnect(ctx); err != nil {
			logs.FromContext(ctx).Err(err).Msg("failed to close mongo client connection")
		}
	}

	storageDeps := storage.StorageDependencies{
		PostgresClient: postgresDBClient,
		SQLClient:      postgresDriver,
		MongoClient:    mongoClient,
	}

	// Return the storage.Storage object and the function to close the database connection.
	return storage.NewStorage(storageDeps), storageClientsCloseFunc, nil
}
