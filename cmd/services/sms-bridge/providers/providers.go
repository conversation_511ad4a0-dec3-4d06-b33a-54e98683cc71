package providers

import (
	"context"
	"fmt"

	"git.redmadrobot.com/zaman/backend/zaman/pkg/sms"

	smsbridge "git.redmadrobot.com/zaman/backend/zaman/config/services/sms-bridge"
)

type direct struct {
	SMSProvider sms.SMSProvider
}

func NewCustomProviders(ctx context.Context, cfg smsbridge.Config, locator *ServiceLocatorImpl) error {
	smsProvider := sms.NewSMSProvider(cfg)
	if err := locator.Register("SMSProvider", smsProvider); err != nil {
		return fmt.Errorf("failed to register SMSProvider: %w", err)
	}

	return nil
}
