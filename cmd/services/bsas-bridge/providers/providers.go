package providers

import (
	"context"
	"fmt"

	bsasbridge "git.redmadrobot.com/zaman/backend/zaman/config/services/bsas-bridge"
	"git.redmadrobot.com/zaman/backend/zaman/pkg/bsas"
)

// direct - структура для хранения собственных провайдеров.
// Важно: имя полей должны полностью совпадать с именем провайдера при его регистрации.
type direct struct {
	// Пример провайдера, который вы хотите добавить
	BSASProvider bsas.BSASProvider
}

// NewCustomProviders - пример функции, которая добавляет в локатор собственные провайдеры, обходя кодогенерацию.
func NewCustomProviders(ctx context.Context, cfg bsasbridge.Config, locator *ServiceLocatorImpl) error {
	// Пример регистрации BSASProvider в локаторе
	bsasProvider := bsas.NewBSASProvider(&cfg)
	if err := locator.Register("BSASProvider", bsasProvider); err != nil {
		return fmt.Errorf("failed to register BSASProvider: %w", err)
	}

	return nil
}
