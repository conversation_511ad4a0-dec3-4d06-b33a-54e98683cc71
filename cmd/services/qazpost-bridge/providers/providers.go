package providers

import (
	"context"
	"fmt"

	"git.redmadrobot.com/backend-go/rmr-pkg/core/logs"

	qazpostbridgecfg "git.redmadrobot.com/zaman/backend/zaman/config/services/qazpost-bridge"
	"git.redmadrobot.com/zaman/backend/zaman/pkg/qazpost"
)

// direct - структура для хранения собственных провайдеров.
type direct struct {
	QazPostProvider qazpost.QazPostProvider
}

func NewCustomProviders(ctx context.Context, cfg qazpostbridgecfg.Config, locator *ServiceLocatorImpl) error {
	log := logs.FromContext(ctx)

	if cfg.App.QazPostConfig.BaseURL == "" {
		return fmt.Errorf("qazpost config is missing base_url")
	}

	// Создаем клиент QazPost
	qazClient, err := qazpost.NewClient(cfg.App.QazPostConfig, log)
	if err != nil {
		return fmt.Errorf("failed to create qazpost client: %w", err)
	}

	// Регистрируем провайдер в локаторе
	if err := locator.Register("QazPostProvider", qazClient); err != nil {
		return fmt.Errorf("failed to register QazPostProvider: %w", err)
	}

	log.Info().Msg("Successfully registered QazPost provider")
	return nil
}
