package providers

import (
	"context"
	"fmt"

	sprbridge "git.redmadrobot.com/zaman/backend/zaman/config/services/spr-bridge"
	"git.redmadrobot.com/zaman/backend/zaman/pkg/spr"
)

// direct - структура для хранения собственных провайдеров.
// Важно: имя полей должны полностью совпадать с именем провайдера при его регистрации.
type direct struct {
	// MyProvider myProvider.Client Пример провайдера, который вы хотите добавить
	SPRProvider spr.SPRProvider
}

// NewCustomProviders - пример функции, которая добавляет в локатор собственные провайдеры, обходя кодогенерацию.
func NewCustomProviders(ctx context.Context, cfg sprbridge.Config, locator *ServiceLocatorImpl) error {
	// Пример регистрации MyProvider в локаторе
	// if err := locator.Register("MyProvider", myProvider); err != nil {
	//	return fmt.Errorf("failed to register MyProvider: %w", err)
	// }

	sprProvider := spr.NewClient(cfg.App.SPRProvider)
	if err := locator.Register("SPRProvider", sprProvider); err != nil {
		return fmt.Errorf("failed to register MyProvider: %w", err)
	}

	return nil
}
