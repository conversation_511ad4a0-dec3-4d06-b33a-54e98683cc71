// Code generated by git.redmadrobot.com/backend-go/meroving. DO NOT EDIT.
package providers

import (
	"context"
	"fmt"
	"reflect"
	"sync"

	"git.redmadrobot.com/backend-go/rmr-pkg/core/bus"
	"git.redmadrobot.com/backend-go/rmr-pkg/core/logs"
	"git.redmadrobot.com/backend-go/rmr-pkg/providers/kafka"

	"git.redmadrobot.com/zaman/backend/zaman/services/spr-bridge/storage"
)

// ServiceLocator интерфейс для регистрации и получения провайдеров
type ServiceLocator interface {
	Register(providerName string, provider interface{}) error
}

// ServiceLocatorImpl реализует интерфейс ServiceLocator
type ServiceLocatorImpl struct {
	direct  // Пользовательские провайдеры
	Storage storage.Storage
	Event   bus.EventBus[*kafka.Message]

	mu  sync.RWMutex // Мьютекс для синхронизации доступа к структуре
	ctx context.Context
}

// NewServiceLocator создает новый экземпляр ServiceLocator
func NewServiceLocator(ctx context.Context) *ServiceLocatorImpl {
	return &ServiceLocatorImpl{
		ctx: ctx,
	}
}

// Register регистрирует новый провайдер по его имени и типу и сохраняет его в соответствующем поле
func (s *ServiceLocatorImpl) Register(providerName string, provider interface{}) error {
	s.mu.Lock()         // Блокируем мьютекс для предотвращения гонок данных
	defer s.mu.Unlock() // Разблокируем мьютекс по завершении функции

	logger := logs.FromContext(s.ctx)

	// Получаем значение структуры (значения всех её полей)
	structValue := reflect.ValueOf(s).Elem()
	// Получаем тип структуры (типы всех её полей)
	structType := structValue.Type()

	// Перебираем все поля структуры
	for i := 0; i < structValue.NumField(); i++ {
		field := structValue.Field(i)    // Получаем значение текущего поля
		fieldType := structType.Field(i) // Получаем тип текущего поля

		// Сравниваем имя и тип поля с именем и типом переданного провайдера
		if fieldType.Name == providerName {
			if field.CanSet() { // Проверяем, можно ли установить значение для данного поля
				// Присваиваем значение провайдера полю структуры
				field.Set(reflect.ValueOf(provider))
				logger.Info().Msgf("Provider '%s' has been set", fieldType.Name)

				return nil
			} else {
				logger.Info().Msgf("Provider '%s' cannot be modified", fieldType.Name)

				return fmt.Errorf("provider '%s' cannot be modified", fieldType.Name)
			}
		}
	}

	// Проверяем поля структуры direct
	directValue := reflect.ValueOf(&s.direct).Elem()
	directType := reflect.TypeOf(s.direct)

	for i := 0; i < directValue.NumField(); i++ {
		field := directValue.Field(i)
		fieldType := directType.Field(i)

		if fieldType.Name == providerName {
			if field.CanSet() {
				field.Set(reflect.ValueOf(provider))
				fmt.Printf("Provider '%s' in direct has been set\n", fieldType.Name)
				return nil
			} else {
				return fmt.Errorf("provider '%s' in direct cannot be modified", fieldType.Name)
			}
		}
	}

	// Возвращаем ошибку, если не найдено подходящее поле для провайдера
	return fmt.Errorf("no suitable field found for provider with name '%s' and type '%s'", providerName, reflect.TypeOf(provider).Name())
}
