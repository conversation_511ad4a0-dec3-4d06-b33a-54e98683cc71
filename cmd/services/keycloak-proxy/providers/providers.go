package providers

import (
	"context"
	"fmt"

	keycloakproxy "git.redmadrobot.com/zaman/backend/zaman/config/services/keycloak-proxy"
	"git.redmadrobot.com/zaman/backend/zaman/pkg/keycloak"
)

// direct - структура для хранения собственных провайдеров.
// Важно: имя полей должны полностью совпадать с именем провайдера при его регистрации.
type direct struct {
	KeycloakProvider keycloak.KeycloakProvider
}

// NewCustomProviders - пример функции, которая добавляет в локатор собственные провайдеры, обходя кодогенерацию.
func NewCustomProviders(ctx context.Context, cfg keycloakproxy.Config, locator *ServiceLocatorImpl) error {
	kcProvider, err := keycloak.NewKeycloakProvider(
		cfg.App.KeycloakProvider,
		cfg.App.KeycloakProvider.KcMobileGwRealm,
		cfg.App.KeycloakProvider.KcSmeGwRealm,
	)
	if err != nil {
		return fmt.Errorf("failed to initialize KeycloakProviderImpl: %w", err)
	}

	if err = locator.Register("KeycloakProvider", kcProvider); err != nil {
		return fmt.Errorf("failed to register KeycloakProviderImpl: %w", err)
	}
	return nil
}
