package providers

import (
	"context"

	o "git.redmadrobot.com/zaman/backend/zaman/pkg/otp"

	"git.redmadrobot.com/backend-go/rmr-pkg/core/errs"

	"git.redmadrobot.com/zaman/backend/zaman/config/services/otp"
)

type direct struct {
	ProviderOtp o.ProviderOtp
}

// NewCustomProviders - пример функции, которая добавляет в локатор собственные провайдеры, обходя кодогенерацию.
func NewCustomProviders(_ context.Context, cfg otp.Config, locator *ServiceLocatorImpl) error {
	otpProviderCfg := o.Config{
		Env:             cfg.App.Environment,
		OtpRequestTTL:   cfg.App.OtpRequestTTL,
		CodeTTL:         cfg.App.CodeTTL,
		MaxAttempts:     cfg.App.MaxAttempts,
		MaxCodeChecks:   cfg.App.MaxCodeChecks,
		NewAttemptDelay: cfg.App.NewAttemptDelay,
		OtpTestCode:     cfg.App.OtpTestCode,
		OtpLength:       cfg.App.OtpLength,
	}

	otpProvider := o.NewProviderOtp(otpProviderCfg, locator.Redis)
	if err := locator.Register("ProviderOtp", otpProvider); err != nil {
		return errs.Wrapf(errs.ErrServiceUnavailable, "failed to register ProviderOtp")
	}

	return nil
}
