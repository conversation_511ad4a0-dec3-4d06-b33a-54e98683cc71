package providers

import (
	"context"
	"fmt"

	apbridge "git.redmadrobot.com/zaman/backend/zaman/config/services/ap-bridge"
	"git.redmadrobot.com/zaman/backend/zaman/pkg/astanaplat"
)

// direct - структура для хранения собственных провайдеров.
// Важно: имя полей должны полностью совпадать с именем провайдера при его регистрации.
type direct struct {
	AstanaPlatProvider astanaplat.AstanaPlatProvider
}

// NewCustomProviders - пример функции, которая добавляет в локатор собственные провайдеры, обходя кодогенерацию.
func NewCustomProviders(ctx context.Context, cfg apbridge.Config, locator *ServiceLocatorImpl) error {
	apProvider := astanaplat.NewAstanaPlatProvider(ctx, cfg.App.APProvider)
	if err := locator.Register("AstanaPlatProvider", apProvider); err != nil {
		return fmt.Errorf("failed to register MyProvider: %w", err)
	}

	return nil
}
