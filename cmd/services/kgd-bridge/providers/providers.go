package providers

import (
	"context"

	"git.redmadrobot.com/backend-go/rmr-pkg/core/logs"

	mock_kgd "git.redmadrobot.com/zaman/backend/zaman/pkg/kgd/mock"

	"git.redmadrobot.com/backend-go/rmr-pkg/core/errs"

	kgdbridge "git.redmadrobot.com/zaman/backend/zaman/config/services/kgd-bridge"
	"git.redmadrobot.com/zaman/backend/zaman/pkg/kgd"
)

// direct - структура для хранения собственных провайдеров.
// Важно: имя полей должны полностью совпадать с именем провайдера при его регистрации.
type direct struct {
	KGDProvider kgd.KGDProvider
}

// NewCustomProviders - пример функции, которая добавляет в локатор собственные провайдеры, обходя кодогенерацию.
func NewCustomProviders(ctx context.Context, cfg kgdbridge.Config, locator *ServiceLocatorImpl) error {
	logger := logs.FromContext(ctx)

	kgdProvider, err := kgd.NewKGDProvider(cfg.App.KGDProvider)
	if err != nil {
		return errs.Wrapf(err, "failed to create KGD provider")
	}

	if cfg.App.KgdMockEnabled {
		logger.Info().Msg("Using mock KGD provider")
		kgdProvider = mock_kgd.NewMockProvider()
	}

	if err := locator.Register("KGDProvider", kgdProvider); err != nil {
		return errs.Wrapf(err, "failed to register KGDProvider")
	}

	return nil
}
